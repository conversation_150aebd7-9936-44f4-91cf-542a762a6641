import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { parseCSV } from "@/lib/csv-utils"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get('file') as File
    const eventId = formData.get('eventId') as string

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 })
    }

    if (!eventId) {
      return NextResponse.json({ error: "Event ID is required" }, { status: 400 })
    }

    // Verify event exists
    const event = await prisma.event.findUnique({
      where: { id: eventId }
    })

    if (!event) {
      return NextResponse.json({ error: "Event not found" }, { status: 404 })
    }

    // Read and parse CSV file
    const csvContent = await file.text()
    let attendeeData

    try {
      attendeeData = parseCSV(csvContent)
    } catch (error) {
      return NextResponse.json({ 
        error: "Invalid CSV format", 
        details: error instanceof Error ? error.message : "Unknown error"
      }, { status: 400 })
    }

    const results = {
      successful: 0,
      failed: 0,
      errors: [] as string[]
    }

    // Process each attendee
    for (const [index, attendee] of attendeeData.entries()) {
      try {
        // Generate unique QR code
        const qrCode = `${event.name.replace(/\s+/g, '').toUpperCase()}-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`

        // Check if attendee with same email already exists for this event
        const existingAttendee = await prisma.attendee.findFirst({
          where: {
            email: attendee.email,
            eventId: eventId
          }
        })

        if (existingAttendee) {
          results.failed++
          results.errors.push(`Row ${index + 2}: Attendee with email ${attendee.email} already exists for this event`)
          continue
        }

        await prisma.attendee.create({
          data: {
            name: attendee.name,
            email: attendee.email,
            qrCode,
            dietaryRestrictions: attendee.dietaryRestrictions,
            specialNotes: attendee.specialNotes,
            eventId,
          }
        })

        results.successful++
      } catch (error) {
        results.failed++
        results.errors.push(`Row ${index + 2}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return NextResponse.json({
      message: `Import completed. ${results.successful} successful, ${results.failed} failed.`,
      results
    })

  } catch (error) {
    console.error("Error importing attendees:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
