import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { QRScanner } from '@/components/scanner/qr-scanner'

// Mock QR Scanner
const mockQrScanner = {
  start: jest.fn().mockResolvedValue(undefined),
  stop: jest.fn(),
  destroy: jest.fn(),
  setCamera: jest.fn().mockResolvedValue(undefined),
  getCamera: jest.fn().mockReturnValue({ id: 'camera1' }),
  turnFlashlightOn: jest.fn().mockResolvedValue(undefined),
  turnFlashlightOff: jest.fn().mockResolvedValue(undefined),
}

jest.mock('qr-scanner', () => ({
  default: jest.fn().mockImplementation(() => mockQrScanner),
  listCameras: jest.fn().mockResolvedValue([
    { id: 'camera1', label: 'Back Camera' },
    { id: 'camera2', label: 'Front Camera' },
  ]),
  scanImage: jest.fn().mockResolvedValue({ data: 'test-qr-data' }),
}))

describe('QRScanner', () => {
  const mockOnScanResult = jest.fn()
  const mockOnScanError = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders scanner interface when active', async () => {
    render(
      <QRScanner
        onScanResult={mockOnScanResult}
        onScanError={mockOnScanError}
        isActive={true}
      />
    )

    expect(screen.getByText('QR Code Scanner')).toBeInTheDocument()
    expect(screen.getByText('Point camera at QR code to scan')).toBeInTheDocument()
    
    await waitFor(() => {
      expect(mockQrScanner.start).toHaveBeenCalled()
    })
  })

  it('shows camera not available message when no cameras found', async () => {
    const QrScanner = require('qr-scanner').default
    QrScanner.listCameras = jest.fn().mockResolvedValue([])

    render(
      <QRScanner
        onScanResult={mockOnScanResult}
        onScanError={mockOnScanError}
        isActive={true}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('Camera Not Available')).toBeInTheDocument()
      expect(screen.getByText('Upload QR Code Image')).toBeInTheDocument()
    })
  })

  it('toggles flashlight when flashlight button is clicked', async () => {
    render(
      <QRScanner
        onScanResult={mockOnScanResult}
        onScanError={mockOnScanError}
        isActive={true}
      />
    )

    await waitFor(() => {
      expect(mockQrScanner.start).toHaveBeenCalled()
    })

    const flashlightButton = screen.getByRole('button', { name: /flashlight/i })
    fireEvent.click(flashlightButton)

    await waitFor(() => {
      expect(mockQrScanner.turnFlashlightOn).toHaveBeenCalled()
    })
  })

  it('switches camera when camera switch button is clicked', async () => {
    render(
      <QRScanner
        onScanResult={mockOnScanResult}
        onScanError={mockOnScanError}
        isActive={true}
      />
    )

    await waitFor(() => {
      expect(mockQrScanner.start).toHaveBeenCalled()
    })

    const switchButton = screen.getByRole('button', { name: /rotate/i })
    fireEvent.click(switchButton)

    await waitFor(() => {
      expect(mockQrScanner.setCamera).toHaveBeenCalled()
    })
  })

  it('handles file upload for QR code scanning', async () => {
    const QrScanner = require('qr-scanner').default
    QrScanner.scanImage = jest.fn().mockResolvedValue({ data: 'uploaded-qr-data' })

    render(
      <QRScanner
        onScanResult={mockOnScanResult}
        onScanError={mockOnScanError}
        isActive={true}
      />
    )

    const file = new File(['test'], 'test.png', { type: 'image/png' })
    const input = screen.getByRole('button', { name: /upload/i })
    
    // Simulate file upload
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false,
    })

    fireEvent.change(fileInput)

    await waitFor(() => {
      expect(QrScanner.scanImage).toHaveBeenCalledWith(file, expect.any(Object))
      expect(mockOnScanResult).toHaveBeenCalledWith('uploaded-qr-data')
    })
  })

  it('handles scan errors gracefully', async () => {
    const QrScanner = require('qr-scanner').default
    QrScanner.scanImage = jest.fn().mockRejectedValue(new Error('No QR code found'))

    render(
      <QRScanner
        onScanResult={mockOnScanResult}
        onScanError={mockOnScanError}
        isActive={true}
      />
    )

    const file = new File(['test'], 'test.png', { type: 'image/png' })
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    Object.defineProperty(fileInput, 'files', {
      value: [file],
      writable: false,
    })

    fireEvent.change(fileInput)

    await waitFor(() => {
      expect(mockOnScanError).toHaveBeenCalledWith('No QR code found in image')
    })
  })

  it('cleans up scanner when component unmounts', async () => {
    const { unmount } = render(
      <QRScanner
        onScanResult={mockOnScanResult}
        onScanError={mockOnScanError}
        isActive={true}
      />
    )

    await waitFor(() => {
      expect(mockQrScanner.start).toHaveBeenCalled()
    })

    unmount()

    expect(mockQrScanner.stop).toHaveBeenCalled()
    expect(mockQrScanner.destroy).toHaveBeenCalled()
  })

  it('does not start scanner when not active', () => {
    render(
      <QRScanner
        onScanResult={mockOnScanResult}
        onScanError={mockOnScanError}
        isActive={false}
      />
    )

    expect(mockQrScanner.start).not.toHaveBeenCalled()
  })

  it('updates scan count when scans are performed', async () => {
    const mockCallback = jest.fn()
    
    // Mock the QR scanner constructor to call the callback
    const QrScanner = require('qr-scanner').default
    QrScanner.mockImplementation((videoElement: any, callback: any) => {
      // Simulate a scan result
      setTimeout(() => callback({ data: 'test-scan-data' }), 100)
      return mockQrScanner
    })

    render(
      <QRScanner
        onScanResult={mockOnScanResult}
        onScanError={mockOnScanError}
        isActive={true}
      />
    )

    await waitFor(() => {
      expect(screen.getByText('1 scans')).toBeInTheDocument()
    })
  })
})
