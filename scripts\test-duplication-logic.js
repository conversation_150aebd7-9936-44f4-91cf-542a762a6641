// Test the duplication logic to ensure it works correctly

function testDuplicationLogic() {
  console.log('🧪 Testing Event Duplication Logic\n');

  // Simulate an existing event (what would come from the database)
  const originalEvent = {
    id: '68936ceab6407a18c2836620',
    name: 'Tech Conference 2024',
    description: 'Annual technology conference',
    startDate: '2024-03-15T08:00:00.000Z',
    endDate: '2024-03-15T18:00:00.000Z',
    serviceStartTime: '1970-01-01T12:00:00.000Z',
    serviceEndTime: '1970-01-01T14:00:00.000Z',
    primaryColor: '#3b82f6',
    secondaryColor: '#1e40af',
    logoUrl: 'https://example.com/logo.png',
    isActive: true,
    mealTypes: [
      {
        id: 'meal1',
        name: 'Main Course',
        description: 'Delicious main course',
        maxQuantityPerAttendee: 1,
        totalQuantity: 100,
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: false
      },
      {
        id: 'meal2',
        name: 'Vegetarian Option',
        description: 'Healthy vegetarian meal',
        maxQuantityPerAttendee: 1,
        totalQuantity: 50,
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true
      }
    ]
  };

  console.log('📋 Original Event:');
  console.log('- ID:', originalEvent.id);
  console.log('- Name:', originalEvent.name);
  console.log('- Meal Types:', originalEvent.mealTypes.length);
  console.log('- First Meal Type ID:', originalEvent.mealTypes[0].id);

  // Simulate the handleDuplicateEvent function
  const handleDuplicateEvent = (event) => {
    // Create a clean copy of the event for duplication
    // Important: Don't include 'id' field so the form dialog treats this as a new event
    const duplicatedEvent = {
      name: `${event.name} (Copy)`,
      description: event.description,
      startDate: event.startDate,
      endDate: event.endDate,
      serviceStartTime: event.serviceStartTime,
      serviceEndTime: event.serviceEndTime,
      primaryColor: event.primaryColor,
      secondaryColor: event.secondaryColor,
      logoUrl: event.logoUrl,
      isActive: event.isActive,
      mealTypes: event.mealTypes?.map((mt) => ({
        // Don't include meal type IDs either
        name: mt.name,
        description: mt.description,
        maxQuantityPerAttendee: mt.maxQuantityPerAttendee,
        totalQuantity: mt.totalQuantity,
        isVegetarian: mt.isVegetarian,
        isVegan: mt.isVegan,
        isGlutenFree: mt.isGlutenFree
      })) || []
    };
    
    return duplicatedEvent;
  };

  // Test the duplication
  const duplicatedEvent = handleDuplicateEvent(originalEvent);

  console.log('\n📋 Duplicated Event:');
  console.log('- ID:', duplicatedEvent.id || 'undefined (✅ correct)');
  console.log('- Name:', duplicatedEvent.name);
  console.log('- Meal Types:', duplicatedEvent.mealTypes.length);
  console.log('- First Meal Type ID:', duplicatedEvent.mealTypes[0].id || 'undefined (✅ correct)');

  // Test the form dialog logic
  const testFormDialogLogic = (event) => {
    const isEditing = event && event.id;
    const url = isEditing ? `/api/events/${event.id}` : '/api/events';
    const method = isEditing ? 'PUT' : 'POST';
    const title = event && event.id ? 'Edit Event' : 'Create New Event';
    
    return { isEditing, url, method, title };
  };

  console.log('\n🔍 Form Dialog Logic Tests:');
  
  // Test with original event (should be editing)
  const originalLogic = testFormDialogLogic(originalEvent);
  console.log('\n1. Original Event:');
  console.log('   - Is Editing:', originalLogic.isEditing, '(should be true)');
  console.log('   - URL:', originalLogic.url);
  console.log('   - Method:', originalLogic.method, '(should be PUT)');
  console.log('   - Title:', originalLogic.title);

  // Test with duplicated event (should be creating)
  const duplicatedLogic = testFormDialogLogic(duplicatedEvent);
  console.log('\n2. Duplicated Event:');
  console.log('   - Is Editing:', duplicatedLogic.isEditing, '(should be false)');
  console.log('   - URL:', duplicatedLogic.url);
  console.log('   - Method:', duplicatedLogic.method, '(should be POST)');
  console.log('   - Title:', duplicatedLogic.title);

  // Test with null event (should be creating)
  const nullLogic = testFormDialogLogic(null);
  console.log('\n3. Null Event (Create New):');
  console.log('   - Is Editing:', nullLogic.isEditing, '(should be false)');
  console.log('   - URL:', nullLogic.url);
  console.log('   - Method:', nullLogic.method, '(should be POST)');
  console.log('   - Title:', nullLogic.title);

  // Validation
  console.log('\n✅ Validation Results:');
  
  const tests = [
    {
      name: 'Duplicated event has no ID',
      condition: !duplicatedEvent.id,
      expected: true
    },
    {
      name: 'Duplicated event name has (Copy) suffix',
      condition: duplicatedEvent.name.includes('(Copy)'),
      expected: true
    },
    {
      name: 'Duplicated meal types have no IDs',
      condition: duplicatedEvent.mealTypes.every(mt => !mt.id),
      expected: true
    },
    {
      name: 'Original event uses PUT method',
      condition: originalLogic.method === 'PUT',
      expected: true
    },
    {
      name: 'Duplicated event uses POST method',
      condition: duplicatedLogic.method === 'POST',
      expected: true
    },
    {
      name: 'Duplicated event shows Create title',
      condition: duplicatedLogic.title === 'Create New Event',
      expected: true
    }
  ];

  tests.forEach(test => {
    const passed = test.condition === test.expected;
    console.log(`   ${passed ? '✅' : '❌'} ${test.name}: ${test.condition}`);
  });

  const allPassed = tests.every(test => test.condition === test.expected);
  console.log(`\n${allPassed ? '🎉' : '❌'} Overall Result: ${allPassed ? 'All tests passed!' : 'Some tests failed!'}`);
  
  if (allPassed) {
    console.log('🚀 Event duplication logic is working correctly!');
  }
}

// Run the test
testDuplicationLogic();
