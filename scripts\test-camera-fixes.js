const { MongoClient } = require('mongodb');

const MONGODB_URI = "mongodb://localhost:27017";
const DB_NAME = "foodscan";

async function testCameraFixes() {
  console.log('📱 Testing Camera Access Fixes and Improvements...\n');
  
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db(DB_NAME);
    const attendeesCollection = db.collection('attendees');
    
    // 1. Verify QR codes exist for testing
    console.log('1️⃣ Verifying QR CODES for testing...');
    const attendees = await attendeesCollection.find({}).toArray();
    
    if (attendees.length === 0) {
      console.log('❌ No attendees found. Please run the seed script first.');
      return;
    }
    
    console.log(`✅ Found ${attendees.length} attendees with QR codes:`);
    attendees.forEach(attendee => {
      console.log(`   - ${attendee.name}: ${attendee.qrCode}`);
    });
    
    // 2. Test QR code validation for scanner
    console.log('\n2️⃣ Testing QR CODE VALIDATION...');
    
    for (const attendee of attendees) {
      const startTime = Date.now();
      const foundAttendee = await attendeesCollection.findOne({ qrCode: attendee.qrCode });
      const endTime = Date.now();
      
      if (foundAttendee) {
        console.log(`✅ QR code lookup: ${attendee.qrCode} -> ${foundAttendee.name} (${endTime - startTime}ms)`);
      } else {
        console.log(`❌ QR code lookup failed: ${attendee.qrCode}`);
      }
    }
    
    // 3. Test scanner API endpoint
    console.log('\n3️⃣ Testing SCANNER API ENDPOINT...');
    
    try {
      const { default: fetch } = await import('node-fetch');
      
      // Test with valid QR code
      const testQRCode = attendees[0].qrCode;
      console.log(`Testing with QR code: ${testQRCode}`);
      
      const scanResponse = await fetch('http://localhost:3000/api/scanner/scan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ qrCode: testQRCode })
      });
      
      if (scanResponse.ok) {
        const result = await scanResponse.json();
        console.log('✅ Scanner API working:');
        console.log(`   Success: ${result.success}`);
        console.log(`   Message: ${result.message}`);
        console.log(`   Attendee: ${result.attendee?.name}`);
      } else {
        const error = await scanResponse.json();
        console.log('❌ Scanner API failed:', error.error);
      }
      
      // Test with invalid QR code
      const invalidResponse = await fetch('http://localhost:3000/api/scanner/scan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ qrCode: 'INVALID-QR-123' })
      });
      
      if (!invalidResponse.ok) {
        const error = await invalidResponse.json();
        console.log('✅ Invalid QR code correctly rejected:', error.error);
      }
      
    } catch (error) {
      console.log('❌ Could not test API endpoint:', error.message);
    }
    
    // 4. Generate test instructions for manual testing
    console.log('\n4️⃣ Generating MANUAL TESTING INSTRUCTIONS...');
    
    const instructions = `
# Camera Access Testing Instructions

## 🔧 Enhanced Features Implemented:

### 1. Robust Error Handling
- ✅ Better camera permission detection
- ✅ Timeout handling for camera initialization
- ✅ Auto-retry mechanism for transient failures
- ✅ Browser-specific error messages and solutions

### 2. Fallback Mechanisms
- ✅ Simple Camera Mode (manual capture)
- ✅ Image upload option
- ✅ Comprehensive error recovery

### 3. Mobile Compatibility
- ✅ Device detection and optimization tips
- ✅ Touch-friendly controls
- ✅ Orientation change handling
- ✅ Browser-specific guidance

### 4. Diagnostic Tools
- ✅ Camera diagnostics panel
- ✅ Real-time device information
- ✅ Permission status checking

## 🧪 Manual Testing Steps:

### Test 1: Camera Access
1. Open http://localhost:3000/scanner
2. Click "Start Scanning"
3. Verify camera initialization works
4. Check console for detailed logs

### Test 2: Error Handling
1. Deny camera permissions when prompted
2. Verify error message is user-friendly
3. Check that retry and fallback options appear
4. Test "Simple Camera Mode" button

### Test 3: Diagnostics
1. Click "Run Camera Diagnostics"
2. Review all diagnostic results
3. Verify browser and device detection
4. Check permission status

### Test 4: QR Code Scanning
Use these test QR codes:
${attendees.map(a => `- ${a.qrCode} (${a.name})`).join('\n')}

### Test 5: Mobile Testing
1. Test on iOS Safari
2. Test on Chrome for Android
3. Verify touch controls work
4. Test device rotation

### Test 6: Fallback Modes
1. Try "Simple Camera Mode"
2. Test image upload functionality
3. Verify QR codes are detected in uploaded images

## 🔍 Expected Behaviors:

### Success Cases:
- Camera initializes within 5-8 seconds
- Clear error messages for permission issues
- Fallback modes work when camera fails
- QR codes are detected quickly and accurately

### Error Cases:
- Permission denied: Shows clear instructions
- No camera: Offers image upload alternative
- Timeout: Auto-retries then offers fallback
- Browser issues: Shows browser-specific help

## 📱 Browser Compatibility:

### Desktop:
- ✅ Chrome (recommended)
- ✅ Firefox
- ✅ Safari (macOS)
- ✅ Edge

### Mobile:
- ✅ iOS Safari (requires permission)
- ✅ Chrome on Android
- ⚠️ Samsung Internet (may need fallback)
- ⚠️ Firefox Mobile (limited support)

## 🚨 Troubleshooting:

If camera still doesn't work:
1. Check browser console for errors
2. Run diagnostics tool
3. Try Simple Camera Mode
4. Use image upload as last resort
5. Ensure HTTPS or localhost access

## 📊 Performance Expectations:
- Camera initialization: < 8 seconds
- QR code detection: < 1 second
- Database lookup: < 50ms
- Error recovery: < 3 seconds
`;

    console.log(instructions);
    
    // 5. Performance testing
    console.log('\n5️⃣ Testing PERFORMANCE...');
    
    const performanceTests = 50;
    const startTime = Date.now();
    
    for (let i = 0; i < performanceTests; i++) {
      const randomAttendee = attendees[Math.floor(Math.random() * attendees.length)];
      await attendeesCollection.findOne({ qrCode: randomAttendee.qrCode });
    }
    
    const endTime = Date.now();
    const avgTime = (endTime - startTime) / performanceTests;
    
    console.log(`✅ Performed ${performanceTests} QR code lookups`);
    console.log(`   Average time: ${avgTime.toFixed(2)}ms`);
    
    if (avgTime < 20) {
      console.log('   🚀 Excellent performance for real-time scanning!');
    } else if (avgTime < 50) {
      console.log('   ✅ Good performance for scanning');
    } else {
      console.log('   ⚠️ Performance could be improved');
    }
    
    console.log('\n🎉 Camera fixes testing completed!');
    console.log('📱 Open http://localhost:3000/scanner to test the enhanced scanner');
    
  } catch (error) {
    console.error('❌ Error during testing:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testCameraFixes().catch(console.error);
