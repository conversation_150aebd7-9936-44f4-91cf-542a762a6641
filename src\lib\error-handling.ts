"use client"

import { useEffect, useState } from "react"

// Error types
export enum ErrorType {
  NETWORK = 'NETWORK',
  VALIDATION = 'VALIDATION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  NOT_FOUND = 'NOT_FOUND',
  SERVER = 'SERVER',
  CLIENT = 'CLIENT',
  UNKNOWN = 'UNKNOWN'
}

export interface AppError {
  id: string
  type: ErrorType
  message: string
  details?: any
  timestamp: Date
  stack?: string
  userId?: string
  sessionId?: string
  url?: string
  userAgent?: string
}

// Error logging service
export class ErrorLogger {
  private static instance: ErrorLogger
  private errors: AppError[] = []
  private maxErrors = 100

  static getInstance(): ErrorLogger {
    if (!ErrorLogger.instance) {
      ErrorLogger.instance = new ErrorLogger()
    }
    return ErrorLogger.instance
  }

  constructor() {
    if (typeof window !== 'undefined') {
      this.setupGlobalErrorHandlers()
    }
  }

  private setupGlobalErrorHandlers() {
    // Handle JavaScript errors
    window.addEventListener('error', (event) => {
      this.logError({
        type: ErrorType.CLIENT,
        message: event.message,
        details: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        },
        stack: event.error?.stack
      })
    })

    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.logError({
        type: ErrorType.CLIENT,
        message: 'Unhandled Promise Rejection',
        details: {
          reason: event.reason
        },
        stack: event.reason?.stack
      })
    })

    // Handle resource loading errors
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        this.logError({
          type: ErrorType.CLIENT,
          message: 'Resource loading error',
          details: {
            element: event.target?.tagName,
            source: (event.target as any)?.src || (event.target as any)?.href
          }
        })
      }
    }, true)
  }

  logError(error: Partial<AppError>): string {
    const errorId = this.generateErrorId()
    const fullError: AppError = {
      id: errorId,
      type: error.type || ErrorType.UNKNOWN,
      message: error.message || 'Unknown error',
      details: error.details,
      timestamp: new Date(),
      stack: error.stack,
      userId: this.getCurrentUserId(),
      sessionId: this.getSessionId(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined
    }

    this.errors.unshift(fullError)
    
    // Keep only the most recent errors
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors)
    }

    // Send to external logging service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToLoggingService(fullError)
    }

    console.error('App Error:', fullError)
    return errorId
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private getCurrentUserId(): string | undefined {
    // Get from session or auth context
    return undefined
  }

  private getSessionId(): string | undefined {
    // Get from session storage or generate
    if (typeof window !== 'undefined') {
      let sessionId = sessionStorage.getItem('session_id')
      if (!sessionId) {
        sessionId = `sess_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        sessionStorage.setItem('session_id', sessionId)
      }
      return sessionId
    }
    return undefined
  }

  private async sendToLoggingService(error: AppError) {
    try {
      await fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(error)
      })
    } catch (err) {
      console.error('Failed to send error to logging service:', err)
    }
  }

  getErrors(): AppError[] {
    return [...this.errors]
  }

  getErrorById(id: string): AppError | undefined {
    return this.errors.find(error => error.id === id)
  }

  clearErrors(): void {
    this.errors = []
  }

  getErrorStats(): {
    total: number
    byType: Record<ErrorType, number>
    recent: number
  } {
    const now = new Date()
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000)
    
    const byType = this.errors.reduce((acc, error) => {
      acc[error.type] = (acc[error.type] || 0) + 1
      return acc
    }, {} as Record<ErrorType, number>)

    const recent = this.errors.filter(error => error.timestamp > oneHourAgo).length

    return {
      total: this.errors.length,
      byType,
      recent
    }
  }
}

// Error boundary hook
export function useErrorHandler() {
  const [error, setError] = useState<AppError | null>(null)
  const logger = ErrorLogger.getInstance()

  const logError = (error: Partial<AppError>) => {
    const errorId = logger.logError(error)
    setError(logger.getErrorById(errorId) || null)
    return errorId
  }

  const clearError = () => {
    setError(null)
  }

  const handleAsyncError = (asyncFn: () => Promise<any>) => {
    return async (...args: any[]) => {
      try {
        return await asyncFn.apply(null, args)
      } catch (err) {
        logError({
          type: ErrorType.CLIENT,
          message: err instanceof Error ? err.message : 'Async operation failed',
          details: { args },
          stack: err instanceof Error ? err.stack : undefined
        })
        throw err
      }
    }
  }

  return {
    error,
    logError,
    clearError,
    handleAsyncError
  }
}

// Network error handler
export function handleNetworkError(error: any): ErrorType {
  if (!navigator.onLine) {
    return ErrorType.NETWORK
  }

  if (error.response) {
    const status = error.response.status
    if (status === 401) return ErrorType.AUTHENTICATION
    if (status === 403) return ErrorType.AUTHORIZATION
    if (status === 404) return ErrorType.NOT_FOUND
    if (status >= 500) return ErrorType.SERVER
    if (status >= 400) return ErrorType.CLIENT
  }

  if (error.request) {
    return ErrorType.NETWORK
  }

  return ErrorType.UNKNOWN
}

// Retry mechanism
export class RetryHandler {
  static async withRetry<T>(
    operation: () => Promise<T>,
    options: {
      maxAttempts?: number
      delay?: number
      backoff?: boolean
      shouldRetry?: (error: any) => boolean
    } = {}
  ): Promise<T> {
    const {
      maxAttempts = 3,
      delay = 1000,
      backoff = true,
      shouldRetry = (error) => {
        const errorType = handleNetworkError(error)
        return errorType === ErrorType.NETWORK || errorType === ErrorType.SERVER
      }
    } = options

    let lastError: any
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await operation()
      } catch (error) {
        lastError = error
        
        if (attempt === maxAttempts || !shouldRetry(error)) {
          throw error
        }

        const waitTime = backoff ? delay * Math.pow(2, attempt - 1) : delay
        await new Promise(resolve => setTimeout(resolve, waitTime))
      }
    }

    throw lastError
  }
}

// Form validation error handler
export function useFormErrorHandler() {
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({})
  const [generalError, setGeneralError] = useState<string | null>(null)

  const setFieldError = (field: string, message: string) => {
    setFieldErrors(prev => ({ ...prev, [field]: message }))
  }

  const clearFieldError = (field: string) => {
    setFieldErrors(prev => {
      const newErrors = { ...prev }
      delete newErrors[field]
      return newErrors
    })
  }

  const clearAllErrors = () => {
    setFieldErrors({})
    setGeneralError(null)
  }

  const hasErrors = Object.keys(fieldErrors).length > 0 || generalError !== null

  return {
    fieldErrors,
    generalError,
    setFieldError,
    setGeneralError,
    clearFieldError,
    clearAllErrors,
    hasErrors
  }
}

// Error recovery strategies
export const ErrorRecoveryStrategies = {
  // Retry with exponential backoff
  retryWithBackoff: async (operation: () => Promise<any>, maxAttempts = 3) => {
    return RetryHandler.withRetry(operation, { maxAttempts, backoff: true })
  },

  // Fallback to cached data
  fallbackToCache: async (operation: () => Promise<any>, cacheKey: string) => {
    try {
      return await operation()
    } catch (error) {
      const cached = localStorage.getItem(cacheKey)
      if (cached) {
        return JSON.parse(cached)
      }
      throw error
    }
  },

  // Graceful degradation
  gracefulDegradation: async (
    operation: () => Promise<any>,
    fallback: () => any
  ) => {
    try {
      return await operation()
    } catch (error) {
      console.warn('Operation failed, using fallback:', error)
      return fallback()
    }
  }
}

// Error reporting component data
export function useErrorReporting() {
  const [errors, setErrors] = useState<AppError[]>([])
  const logger = ErrorLogger.getInstance()

  useEffect(() => {
    const updateErrors = () => {
      setErrors(logger.getErrors())
    }

    updateErrors()
    const interval = setInterval(updateErrors, 5000)

    return () => clearInterval(interval)
  }, [logger])

  const stats = logger.getErrorStats()

  return {
    errors,
    stats,
    clearErrors: () => {
      logger.clearErrors()
      setErrors([])
    }
  }
}
