const { MongoClient } = require('mongodb');

const MONGODB_URI = "mongodb://localhost:27017";
const DB_NAME = "foodscan";

async function testAttendeesCRUD() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db(DB_NAME);
    const attendeesCollection = db.collection('attendees');
    const eventsCollection = db.collection('events');
    
    console.log('\n👥 Testing Attendees CRUD Operations...\n');
    
    // First, get an existing event to associate attendees with
    const existingEvent = await eventsCollection.findOne({});
    if (!existingEvent) {
      console.log('❌ No events found. Please create an event first.');
      return;
    }
    console.log('📅 Using event:', existingEvent.name);
    
    // 1. CREATE - Test attendee creation
    console.log('\n1️⃣ Testing CREATE operation...');
    const newAttendee = {
      id: 'test-attendee-' + Date.now(),
      name: '<PERSON>',
      email: '<EMAIL>',
      qrCode: 'TEST-QR-' + Date.now() + '-' + Math.random().toString(36).substr(2, 6),
      dietaryRestrictions: 'Vegetarian, No nuts',
      specialNotes: 'Prefers early lunch',
      eventId: existingEvent.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const createResult = await attendeesCollection.insertOne(newAttendee);
    console.log('✅ Attendee created with ID:', createResult.insertedId);
    console.log('   QR Code:', newAttendee.qrCode);
    
    // 2. READ - Test attendee retrieval
    console.log('\n2️⃣ Testing READ operation...');
    const foundAttendee = await attendeesCollection.findOne({ id: newAttendee.id });
    if (foundAttendee) {
      console.log('✅ Attendee found:', foundAttendee.name);
      console.log('   Email:', foundAttendee.email);
      console.log('   QR Code:', foundAttendee.qrCode);
      console.log('   Dietary Restrictions:', foundAttendee.dietaryRestrictions);
      console.log('   Special Notes:', foundAttendee.specialNotes);
      console.log('   Event ID:', foundAttendee.eventId);
    } else {
      console.log('❌ Attendee not found');
    }
    
    // Test reading all attendees for the event
    const eventAttendees = await attendeesCollection.find({ eventId: existingEvent.id }).toArray();
    console.log('✅ Found', eventAttendees.length, 'attendees for event');
    
    // 3. UPDATE - Test attendee modification
    console.log('\n3️⃣ Testing UPDATE operation...');
    const updateData = {
      name: 'John Updated Doe',
      email: '<EMAIL>',
      dietaryRestrictions: 'Vegan, Gluten-free',
      specialNotes: 'Updated: Prefers late lunch',
      updatedAt: new Date()
    };
    
    const updateResult = await attendeesCollection.updateOne(
      { id: newAttendee.id },
      { $set: updateData }
    );
    
    if (updateResult.modifiedCount > 0) {
      console.log('✅ Attendee updated successfully');
      const updatedAttendee = await attendeesCollection.findOne({ id: newAttendee.id });
      console.log('   New name:', updatedAttendee.name);
      console.log('   New email:', updatedAttendee.email);
      console.log('   New dietary restrictions:', updatedAttendee.dietaryRestrictions);
      console.log('   New special notes:', updatedAttendee.specialNotes);
    } else {
      console.log('❌ Attendee update failed');
    }
    
    // Test QR Code uniqueness
    console.log('\n🔍 Testing QR Code uniqueness...');
    const qrCodeCheck = await attendeesCollection.findOne({ qrCode: newAttendee.qrCode });
    if (qrCodeCheck) {
      console.log('✅ QR Code is unique and retrievable');
      console.log('   QR Code:', qrCodeCheck.qrCode, '-> Attendee:', qrCodeCheck.name);
    }
    
    // 4. DELETE - Test attendee deletion
    console.log('\n4️⃣ Testing DELETE operation...');
    const deleteResult = await attendeesCollection.deleteOne({ id: newAttendee.id });
    if (deleteResult.deletedCount > 0) {
      console.log('✅ Attendee deleted successfully');
      
      // Verify deletion
      const deletedAttendee = await attendeesCollection.findOne({ id: newAttendee.id });
      if (!deletedAttendee) {
        console.log('✅ Confirmed: Attendee no longer exists in database');
      } else {
        console.log('❌ Error: Attendee still exists after deletion');
      }
    } else {
      console.log('❌ Attendee deletion failed');
    }
    
    // Test bulk operations
    console.log('\n📦 Testing BULK operations...');
    const bulkAttendees = [
      {
        id: 'bulk-1-' + Date.now(),
        name: 'Bulk Attendee 1',
        email: '<EMAIL>',
        qrCode: 'BULK1-' + Date.now(),
        eventId: existingEvent.id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'bulk-2-' + Date.now(),
        name: 'Bulk Attendee 2',
        email: '<EMAIL>',
        qrCode: 'BULK2-' + Date.now(),
        eventId: existingEvent.id,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    const bulkCreateResult = await attendeesCollection.insertMany(bulkAttendees);
    console.log('✅ Bulk created', Object.keys(bulkCreateResult.insertedIds).length, 'attendees');
    
    // Clean up bulk attendees
    const bulkDeleteResult = await attendeesCollection.deleteMany({
      id: { $in: bulkAttendees.map(a => a.id) }
    });
    console.log('✅ Bulk deleted', bulkDeleteResult.deletedCount, 'attendees');
    
    console.log('\n🎉 Attendees CRUD testing completed!');
    
  } catch (error) {
    console.error('❌ Error during CRUD testing:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testAttendeesCRUD().catch(console.error);
