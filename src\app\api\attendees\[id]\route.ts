import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const attendee = await prisma.attendee.findUnique({
      where: { id: params.id },
      include: {
        event: {
          select: {
            name: true,
            id: true,
            startDate: true,
            endDate: true
          }
        },
        mealConsumptions: {
          include: {
            mealType: true
          }
        },
        scanLogs: {
          orderBy: {
            scannedAt: 'desc'
          }
        }
      }
    })

    if (!attendee) {
      return NextResponse.json({ error: "Attendee not found" }, { status: 404 })
    }

    return NextResponse.json(attendee)
  } catch (error) {
    console.error("Error fetching attendee:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      email,
      dietaryRestrictions,
      specialNotes,
    } = body

    const attendee = await prisma.attendee.update({
      where: { id: params.id },
      data: {
        name,
        email,
        dietaryRestrictions,
        specialNotes,
        updatedAt: new Date()
      },
      include: {
        event: {
          select: {
            name: true,
            id: true
          }
        },
        mealConsumptions: {
          include: {
            mealType: true
          }
        }
      }
    })

    return NextResponse.json(attendee)
  } catch (error) {
    console.error("Error updating attendee:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    // Delete related records first
    await prisma.mealConsumption.deleteMany({
      where: { attendeeId: params.id }
    })

    await prisma.scanLog.deleteMany({
      where: { attendeeId: params.id }
    })

    // Delete the attendee
    await prisma.attendee.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: "Attendee deleted successfully" })
  } catch (error) {
    console.error("Error deleting attendee:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
