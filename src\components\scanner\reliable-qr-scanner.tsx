"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Camera, 
  CameraOff, 
  Upload, 
  Flashlight,
  FlashlightOff,
  RotateCcw,
  AlertTriangle,
  RefreshCw,
  CheckCircle
} from "lucide-react"

interface ReliableQRScannerProps {
  onScanResult: (data: string) => void
  onScanError: (error: string) => void
  isActive: boolean
}

export function ReliableQRScanner({ onScanResult, onScanError, isActive }: ReliableQRScannerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const scannerRef = useRef<any>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isReady, setIsReady] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [scanCount, setScanCount] = useState(0)
  const [isFlashlightOn, setIsFlashlightOn] = useState(false)

  // Simple initialization without complex state management
  const initializeScanner = useCallback(async () => {
    if (!isActive || !videoRef.current || isLoading) return

    console.log('🎥 Starting reliable QR scanner...')
    setIsLoading(true)
    setError(null)
    setIsReady(false)

    try {
      // Dynamic import to avoid SSR issues
      console.log('📦 Importing QR Scanner library...')
      const QrScannerModule = await import('qr-scanner')
      const QrScanner = QrScannerModule.default
      console.log('✅ QR Scanner library imported:', typeof QrScanner)

      // Create scanner with minimal configuration
      console.log('🔧 Creating QR Scanner instance...')
      const scanner = new QrScanner(
        videoRef.current,
        (result) => {
          console.log('✅ QR Code detected:', result.data)
          setScanCount(prev => prev + 1)
          onScanResult(result.data)

          // Haptic feedback
          if ('vibrate' in navigator) {
            navigator.vibrate(100)
          }
        },
        {
          onDecodeError: (error) => {
            console.debug('🔍 QR decode attempt:', error.message)
          },
          highlightScanRegion: true,
          highlightCodeOutline: true,
          preferredCamera: 'environment',
          maxScansPerSecond: 2
        }
      )

      console.log('📹 QR Scanner instance created')
      scannerRef.current = scanner

      // Start with timeout and better error handling
      console.log('🚀 Starting QR Scanner...')
      const startPromise = scanner.start()
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Scanner start timeout after 10 seconds')), 10000)
      )

      await Promise.race([startPromise, timeoutPromise])
      console.log('🎉 QR Scanner started successfully!')

      setIsReady(true)
      setIsLoading(false)
      console.log('✅ Reliable QR scanner ready!')

    } catch (err) {
      console.error('❌ Scanner failed:', err)
      setError(err instanceof Error ? err.message : 'Scanner initialization failed')
      setIsLoading(false)
      onScanError('Failed to initialize camera. Please try again or use image upload.')
    }
  }, [isActive, onScanResult, onScanError, isLoading])

  // Cleanup
  const cleanup = useCallback(() => {
    if (scannerRef.current) {
      console.log('🧹 Cleaning up scanner...')
      scannerRef.current.stop()
      scannerRef.current.destroy()
      scannerRef.current = null
    }
    setIsReady(false)
    setIsLoading(false)
  }, [])

  // Toggle flashlight
  const toggleFlashlight = useCallback(async () => {
    if (!scannerRef.current) return

    try {
      if (isFlashlightOn) {
        await scannerRef.current.turnFlashlightOff()
        setIsFlashlightOn(false)
      } else {
        await scannerRef.current.turnFlashlightOn()
        setIsFlashlightOn(true)
      }
    } catch (err) {
      onScanError('Flashlight not supported on this device')
    }
  }, [isFlashlightOn, onScanError])

  // File upload handler
  const handleFileUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith('image/')) {
      onScanError('Please select an image file')
      return
    }

    try {
      console.log('📁 Scanning uploaded image...')
      const QrScanner = (await import('qr-scanner')).default
      const result = await QrScanner.scanImage(file)
      onScanResult(result)
      setScanCount(prev => prev + 1)
      console.log('✅ QR code found in uploaded image')
    } catch (err) {
      console.error('❌ No QR code in image:', err)
      onScanError('No QR code found in the uploaded image')
    }

    e.target.value = ""
  }, [onScanResult, onScanError])

  // Effects
  useEffect(() => {
    if (isActive) {
      initializeScanner()
    } else {
      cleanup()
    }

    return cleanup
  }, [isActive, initializeScanner, cleanup])

  // Loading state
  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            <span>Starting Camera</span>
          </CardTitle>
          <CardDescription>
            Initializing QR code scanner...
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center py-8">
          <div className="space-y-4">
            <RefreshCw className="mx-auto h-12 w-12 animate-spin text-muted-foreground" />
            <p className="text-muted-foreground">Please wait while we start your camera...</p>
            <Button 
              variant="outline" 
              onClick={() => {
                cleanup()
                setTimeout(initializeScanner, 500)
              }}
            >
              Cancel & Retry
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Error state
  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CameraOff className="h-5 w-5" />
            <span>Camera Error</span>
          </CardTitle>
          <CardDescription>
            Unable to access camera
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Error:</strong> {error}
            </AlertDescription>
          </Alert>

          <div className="flex flex-col gap-2">
            <Button onClick={initializeScanner}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            
            <Button onClick={() => fileInputRef.current?.click()} variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Upload QR Code Image
            </Button>
            
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>
        </CardContent>
      </Card>
    )
  }

  // Working scanner
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Camera className="h-5 w-5 text-green-600" />
              <span>QR Scanner</span>
              <Badge variant="outline" className="text-green-600 border-green-600">
                <CheckCircle className="h-3 w-3 mr-1" />
                Ready
              </Badge>
            </CardTitle>
            <CardDescription>
              Point camera at QR code • {scanCount} scans completed
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Video Container */}
        <div className="relative aspect-square bg-black rounded-lg overflow-hidden">
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            playsInline
            muted
            autoPlay
          />
          
          {/* Scanning overlay */}
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="w-48 h-48 border-2 border-primary rounded-lg relative">
              <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-primary rounded-tl-lg"></div>
              <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-primary rounded-tr-lg"></div>
              <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-primary rounded-bl-lg"></div>
              <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-primary rounded-br-lg"></div>
            </div>
          </div>

          {/* Status */}
          <div className="absolute top-4 left-4">
            <Badge variant="secondary" className="bg-black bg-opacity-50 text-white border-none">
              <Camera className="h-3 w-3 mr-1" />
              Live
            </Badge>
          </div>
        </div>

        {/* Controls */}
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={toggleFlashlight}
            disabled={!isReady}
          >
            {isFlashlightOn ? (
              <FlashlightOff className="h-4 w-4" />
            ) : (
              <Flashlight className="h-4 w-4" />
            )}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="h-4 w-4" />
          </Button>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            className="hidden"
          />
        </div>

        {/* Instructions */}
        <div className="text-center text-sm text-muted-foreground">
          <p>📱 Position QR code within the frame for automatic detection</p>
        </div>
      </CardContent>
    </Card>
  )
}
