"use client"

import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Users, 
  QrCode, 
  Clock, 
  TrendingUp,
  CheckCircle,
  AlertCircle
} from "lucide-react"

interface ScannerDashboardProps {
  todayScans: number
  totalAttendees: number
  pendingScans: number
  scanRate: number
}

export function ScannerDashboard({ 
  todayScans, 
  totalAttendees, 
  pendingScans, 
  scanRate 
}: ScannerDashboardProps) {
  const completionRate = ((todayScans / totalAttendees) * 100).toFixed(1)
  const scansPerHour = scanRate
  
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle>Today's Progress</CardTitle>
          <CardDescription>
            Real-time scanning statistics
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Completion Rate</span>
              <span className="font-medium">{completionRate}%</span>
            </div>
            <div className="w-full bg-muted rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(parseFloat(completionRate), 100)}%` }}
              />
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-center mb-1">
                <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-2xl font-bold">{todayScans}</span>
              </div>
              <p className="text-xs text-muted-foreground">Scanned</p>
            </div>
            
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-center mb-1">
                <Clock className="h-4 w-4 text-yellow-500 mr-1" />
                <span className="text-2xl font-bold">{pendingScans}</span>
              </div>
              <p className="text-xs text-muted-foreground">Pending</p>
            </div>
          </div>

          {/* Additional Stats */}
          <div className="space-y-2 pt-2 border-t">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-muted-foreground" />
                <span>Total Attendees</span>
              </div>
              <span className="font-medium">{totalAttendees}</span>
            </div>
            
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                <span>Scan Rate</span>
              </div>
              <span className="font-medium">{scansPerHour}/hour</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Scanner Status</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm">Camera</span>
            <Badge variant="success">Active</Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm">Network</span>
            <Badge variant="success">Online</Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm">Sync Status</span>
            <Badge variant="success">Up to date</Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm">Last Sync</span>
            <span className="text-xs text-muted-foreground">Just now</span>
          </div>
        </CardContent>
      </Card>

      {/* Performance Indicators */}
      <Card>
        <CardHeader>
          <CardTitle>Performance</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm">Avg. Scan Time</span>
            <span className="text-sm font-medium">0.8s</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm">Success Rate</span>
            <span className="text-sm font-medium">98.5%</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm">Queue Length</span>
            <span className="text-sm font-medium">0</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
