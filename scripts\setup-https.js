const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🔒 Setting up HTTPS for iPhone compatibility...\n');

// Check if mkcert is installed
function checkMkcert() {
  try {
    execSync('mkcert -version', { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

// Install mkcert instructions
function showMkcertInstructions() {
  console.log('📋 To enable HTTPS, you need to install mkcert:');
  console.log('');
  console.log('Windows (with Chocolatey):');
  console.log('  choco install mkcert');
  console.log('');
  console.log('Windows (with Scoop):');
  console.log('  scoop bucket add extras');
  console.log('  scoop install mkcert');
  console.log('');
  console.log('macOS (with Homebrew):');
  console.log('  brew install mkcert');
  console.log('');
  console.log('Linux:');
  console.log('  # Download from: https://github.com/FiloSottile/mkcert/releases');
  console.log('');
  console.log('After installing mkcert, run this script again.');
}

// Create certificates
function createCertificates() {
  const certsDir = path.join(__dirname, '..', 'certs');
  
  // Create certs directory
  if (!fs.existsSync(certsDir)) {
    fs.mkdirSync(certsDir);
  }

  try {
    console.log('🔧 Installing local CA...');
    execSync('mkcert -install', { stdio: 'inherit' });
    
    console.log('📜 Creating certificates...');
    execSync(`mkcert -key-file "${path.join(certsDir, 'localhost-key.pem')}" -cert-file "${path.join(certsDir, 'localhost.pem')}" localhost 127.0.0.1 ::1`, { 
      stdio: 'inherit',
      cwd: certsDir 
    });
    
    console.log('✅ Certificates created successfully!');
    return true;
  } catch (error) {
    console.error('❌ Failed to create certificates:', error.message);
    return false;
  }
}

// Create HTTPS server script
function createHttpsServer() {
  const serverScript = `const { createServer } = require('https');
const { parse } = require('url');
const next = require('next');
const fs = require('fs');
const path = require('path');

const dev = process.env.NODE_ENV !== 'production';
const hostname = 'localhost';
const port = 3000;

// Load SSL certificates
const httpsOptions = {
  key: fs.readFileSync(path.join(__dirname, 'certs', 'localhost-key.pem')),
  cert: fs.readFileSync(path.join(__dirname, 'certs', 'localhost.pem')),
};

const app = next({ dev, hostname, port });
const handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer(httpsOptions, async (req, res) => {
    try {
      const parsedUrl = parse(req.url, true);
      await handle(req, res, parsedUrl);
    } catch (err) {
      console.error('Error occurred handling', req.url, err);
      res.statusCode = 500;
      res.end('internal server error');
    }
  })
    .once('error', (err) => {
      console.error(err);
      process.exit(1);
    })
    .listen(port, () => {
      console.log(\`🔒 HTTPS Server ready at https://\${hostname}:\${port}\`);
      console.log(\`📱 iPhone-compatible URL: https://localhost:3000\`);
    });
});`;

  fs.writeFileSync(path.join(__dirname, '..', 'server-https.js'), serverScript);
  console.log('✅ HTTPS server script created!');
}

// Update package.json
function updatePackageJson() {
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  if (!packageJson.scripts['dev:https']) {
    packageJson.scripts['dev:https'] = 'node server-https.js';
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2));
    console.log('✅ Added dev:https script to package.json');
  }
}

// Main execution
async function main() {
  if (!checkMkcert()) {
    showMkcertInstructions();
    return;
  }

  if (createCertificates()) {
    createHttpsServer();
    updatePackageJson();
    
    console.log('');
    console.log('🎉 HTTPS setup complete!');
    console.log('');
    console.log('📱 To test on iPhone:');
    console.log('1. Run: npm run dev:https');
    console.log('2. Open iPhone Safari');
    console.log('3. Navigate to: https://localhost:3000');
    console.log('4. Accept the security certificate');
    console.log('5. Test the QR scanner!');
    console.log('');
    console.log('🔒 The HTTPS connection will enable camera access on iPhone.');
  }
}

main().catch(console.error);
