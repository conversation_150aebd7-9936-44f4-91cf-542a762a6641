export default function TestPage() {
  return (
    <div className="min-h-screen bg-blue-500 flex items-center justify-center">
      <div className="bg-white p-8 rounded-lg shadow-lg">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          Tailwind CSS Test
        </h1>
        <p className="text-gray-600 mb-4">
          If you can see this styled properly, Tailwind CSS is working!
        </p>
        <div className="flex space-x-4">
          <button className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Primary Button
          </button>
          <button className="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Secondary Button
          </button>
        </div>
        <div className="mt-6 grid grid-cols-3 gap-4">
          <div className="bg-red-100 p-4 rounded text-center">Red</div>
          <div className="bg-green-100 p-4 rounded text-center">Green</div>
          <div className="bg-blue-100 p-4 rounded text-center">Blue</div>
        </div>
      </div>
    </div>
  )
}
