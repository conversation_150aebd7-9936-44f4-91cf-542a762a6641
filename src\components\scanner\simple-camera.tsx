"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Camera, 
  CameraOff, 
  Upload, 
  RefreshCw,
  AlertTriangle,
  CheckCircle
} from "lucide-react"

interface SimpleCameraProps {
  onImageCapture: (imageData: string) => void
  onError: (error: string) => void
  isActive: boolean
}

export function SimpleCamera({ onImageCapture, onError, isActive }: SimpleCameraProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [stream, setStream] = useState<MediaStream | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [hasCamera, setHasCamera] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const startCamera = useCallback(async () => {
    if (!isActive || !videoRef.current) return

    setIsLoading(true)
    setError(null)

    try {
      console.log('🎥 Starting simple camera...')
      
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment',
          width: { ideal: 1280, max: 1920 },
          height: { ideal: 720, max: 1080 }
        }
      })

      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream
        videoRef.current.play()
        setStream(mediaStream)
        setHasCamera(true)
        console.log('✅ Simple camera started successfully')
      }
    } catch (err) {
      console.error('❌ Simple camera failed:', err)
      setError(err instanceof Error ? err.message : 'Camera access failed')
      setHasCamera(false)
      onError('Failed to access camera')
    } finally {
      setIsLoading(false)
    }
  }, [isActive, onError])

  const stopCamera = useCallback(() => {
    if (stream) {
      console.log('🛑 Stopping simple camera...')
      stream.getTracks().forEach(track => track.stop())
      setStream(null)
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null
    }
    setHasCamera(false)
  }, [stream])

  const captureImage = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return

    const video = videoRef.current
    const canvas = canvasRef.current
    const context = canvas.getContext('2d')

    if (!context) return

    // Set canvas size to match video
    canvas.width = video.videoWidth
    canvas.height = video.videoHeight

    // Draw current video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height)

    // Get image data as base64
    const imageData = canvas.toDataURL('image/jpeg', 0.8)
    onImageCapture(imageData)

    console.log('📸 Image captured from simple camera')
  }, [onImageCapture])

  const handleFileUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith('image/')) {
      onError('Please select an image file')
      return
    }

    const reader = new FileReader()
    reader.onload = (event) => {
      const imageData = event.target?.result as string
      onImageCapture(imageData)
      console.log('📁 Image uploaded from file')
    }
    reader.readAsDataURL(file)

    // Reset file input
    e.target.value = ""
  }, [onImageCapture, onError])

  useEffect(() => {
    if (isActive) {
      startCamera()
    } else {
      stopCamera()
    }

    return () => {
      stopCamera()
    }
  }, [isActive, startCamera, stopCamera])

  if (error || !hasCamera) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CameraOff className="h-5 w-5" />
            <span>Simple Camera Mode</span>
          </CardTitle>
          <CardDescription>
            Basic camera access for manual image capture
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Camera Error:</strong> {error}
              </AlertDescription>
            </Alert>
          )}

          <div className="text-center py-8">
            <CameraOff className="mx-auto h-16 w-16 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-semibold">Camera Not Available</h3>
            <p className="text-muted-foreground">
              Use the image upload option to scan QR codes
            </p>
          </div>

          <div className="flex flex-col gap-2">
            <Button onClick={startCamera} disabled={isLoading}>
              <RefreshCw className={`mr-2 h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? 'Trying Camera...' : 'Try Camera Again'}
            </Button>
            
            <Button onClick={() => fileInputRef.current?.click()} variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Upload QR Code Image
            </Button>
            
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Camera className="h-5 w-5 text-green-600" />
          <span>Simple Camera Mode</span>
          <Badge variant="outline" className="text-green-600 border-green-600">
            Active
          </Badge>
        </CardTitle>
        <CardDescription>
          Capture images manually to scan QR codes
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Video Container */}
        <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            playsInline
            muted
            autoPlay
          />
          
          {/* Capture Overlay */}
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="w-48 h-48 border-2 border-white rounded-lg relative">
              <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-white rounded-tl-lg"></div>
              <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-white rounded-tr-lg"></div>
              <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-white rounded-bl-lg"></div>
              <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-white rounded-br-lg"></div>
            </div>
          </div>

          {/* Status indicator */}
          <div className="absolute top-4 left-4">
            <Badge variant="secondary" className="bg-black bg-opacity-50 text-white border-none">
              <Camera className="h-3 w-3 mr-1" />
              Live
            </Badge>
          </div>
        </div>

        {/* Controls */}
        <div className="flex justify-center space-x-2">
          <Button onClick={captureImage} size="lg">
            <Camera className="mr-2 h-4 w-4" />
            Capture Image
          </Button>
          
          <Button onClick={() => fileInputRef.current?.click()} variant="outline">
            <Upload className="mr-2 h-4 w-4" />
            Upload Image
          </Button>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            className="hidden"
          />
        </div>

        {/* Instructions */}
        <div className="text-center text-sm text-muted-foreground">
          <p>📱 Position the QR code within the frame and tap "Capture Image"</p>
          <p className="mt-1">The captured image will be processed for QR codes</p>
        </div>

        {/* Hidden canvas for image capture */}
        <canvas ref={canvasRef} className="hidden" />
      </CardContent>
    </Card>
  )
}
