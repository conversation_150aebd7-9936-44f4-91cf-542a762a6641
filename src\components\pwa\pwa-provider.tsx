"use client"

import { useEffect, useState } from "react"
import { PWAInstallPrompt } from "./install-prompt"
import { OfflineBanner } from "./offline-sync"
import { initializePWA } from "@/lib/pwa"

export function PWAProvider({ children }: { children: React.ReactNode }) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
    // Initialize PWA features on client side
    if (typeof window !== 'undefined') {
      initializePWA()
    }
  }, [])

  // Don't render PWA components on server
  if (!mounted) {
    return <>{children}</>
  }

  return (
    <>
      <OfflineBanner />
      {children}
      <PWAInstallPrompt />
    </>
  )
}
