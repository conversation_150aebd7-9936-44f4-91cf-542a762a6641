"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  Users, 
  Activity,
  AlertCircle,
  CheckCircle,
  Clock
} from "lucide-react"
import { useWebSocket, type SystemStatusEvent } from "@/lib/websocket"

export function RealtimeStatus() {
  const { isConnected, reconnectAttempts, on, off, reconnect } = useWebSocket()
  const [systemStatus, setSystemStatus] = useState<SystemStatusEvent['data'] | null>(null)
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null)
  const [isReconnecting, setIsReconnecting] = useState(false)

  useEffect(() => {
    const handleSystemStatus = (data: SystemStatusEvent['data']) => {
      setSystemStatus(data)
      setLastUpdate(new Date())
    }

    const handleConnectionStatus = (data: { connected: boolean; reason?: string }) => {
      if (data.connected) {
        setIsReconnecting(false)
      }
    }

    const handleConnectionError = () => {
      setIsReconnecting(true)
    }

    on('system_status', handleSystemStatus)
    on('connection_status', handleConnectionStatus)
    on('connection_error', handleConnectionError)

    return () => {
      off('system_status', handleSystemStatus)
      off('connection_status', handleConnectionStatus)
      off('connection_error', handleConnectionError)
    }
  }, [on, off])

  const handleReconnect = () => {
    setIsReconnecting(true)
    reconnect()
  }

  const getStatusColor = () => {
    if (!isConnected) return 'destructive'
    if (!systemStatus) return 'warning'
    
    switch (systemStatus.systemHealth) {
      case 'healthy': return 'success'
      case 'warning': return 'warning'
      case 'error': return 'destructive'
      default: return 'secondary'
    }
  }

  const getStatusIcon = () => {
    if (isReconnecting) return <RefreshCw className="h-4 w-4 animate-spin" />
    if (!isConnected) return <WifiOff className="h-4 w-4" />
    if (!systemStatus) return <Clock className="h-4 w-4" />
    
    switch (systemStatus.systemHealth) {
      case 'healthy': return <CheckCircle className="h-4 w-4" />
      case 'warning': return <AlertCircle className="h-4 w-4" />
      case 'error': return <AlertCircle className="h-4 w-4" />
      default: return <Activity className="h-4 w-4" />
    }
  }

  const getStatusText = () => {
    if (isReconnecting) return 'Reconnecting...'
    if (!isConnected) return `Disconnected ${reconnectAttempts > 0 ? `(${reconnectAttempts} attempts)` : ''}`
    if (!systemStatus) return 'Connected'
    
    switch (systemStatus.systemHealth) {
      case 'healthy': return 'All systems operational'
      case 'warning': return 'Minor issues detected'
      case 'error': return 'System errors detected'
      default: return 'Status unknown'
    }
  }

  const formatLastUpdate = (date: Date | null) => {
    if (!date) return 'Never'
    
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)
    
    if (diffInSeconds < 10) return 'Just now'
    if (diffInSeconds < 60) return `${diffInSeconds}s ago`
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    return date.toLocaleTimeString()
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon()}
            <CardTitle className="text-lg">Real-time Status</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={getStatusColor()}>
              {isConnected ? 'Connected' : 'Disconnected'}
            </Badge>
            {!isConnected && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleReconnect}
                disabled={isReconnecting}
              >
                {isReconnecting ? (
                  <RefreshCw className="h-3 w-3 animate-spin" />
                ) : (
                  'Reconnect'
                )}
              </Button>
            )}
          </div>
        </div>
        <CardDescription>
          {getStatusText()}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* System Metrics */}
        {systemStatus && (
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-center mb-1">
                <Activity className="h-4 w-4 text-blue-500 mr-1" />
                <span className="text-2xl font-bold">{systemStatus.activeScans}</span>
              </div>
              <p className="text-xs text-muted-foreground">Active Scans</p>
            </div>
            
            <div className="text-center p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center justify-center mb-1">
                <Users className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-2xl font-bold">{systemStatus.connectedDevices}</span>
              </div>
              <p className="text-xs text-muted-foreground">Connected Devices</p>
            </div>
          </div>
        )}

        {/* Connection Details */}
        <div className="space-y-2 text-sm">
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Connection</span>
            <div className="flex items-center space-x-1">
              <Wifi className="h-3 w-3" />
              <span>{isConnected ? 'WebSocket' : 'Disconnected'}</span>
            </div>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-muted-foreground">Last Update</span>
            <span>{formatLastUpdate(lastUpdate)}</span>
          </div>
          
          {reconnectAttempts > 0 && (
            <div className="flex items-center justify-between">
              <span className="text-muted-foreground">Reconnect Attempts</span>
              <Badge variant="warning">{reconnectAttempts}</Badge>
            </div>
          )}
        </div>

        {/* Health Status */}
        {systemStatus && (
          <div className="pt-2 border-t">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">System Health</span>
              <Badge variant={getStatusColor()}>
                {systemStatus.systemHealth.charAt(0).toUpperCase() + systemStatus.systemHealth.slice(1)}
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export function RealtimeIndicator() {
  const { isConnected } = useWebSocket()
  const [pulseCount, setPulseCount] = useState(0)

  useEffect(() => {
    if (!isConnected) return

    const interval = setInterval(() => {
      setPulseCount(prev => prev + 1)
    }, 2000)

    return () => clearInterval(interval)
  }, [isConnected])

  if (!isConnected) {
    return (
      <div className="flex items-center space-x-1 text-red-500">
        <WifiOff className="h-3 w-3" />
        <span className="text-xs">Offline</span>
      </div>
    )
  }

  return (
    <div className="flex items-center space-x-1 text-green-500">
      <div className="relative">
        <Wifi className="h-3 w-3" />
        <div 
          key={pulseCount}
          className="absolute inset-0 h-3 w-3 rounded-full bg-green-500 animate-ping opacity-75"
        />
      </div>
      <span className="text-xs">Live</span>
    </div>
  )
}
