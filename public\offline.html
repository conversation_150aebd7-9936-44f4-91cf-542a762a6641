<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FoodScan - Offline</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .container {
            text-align: center;
            padding: 2rem;
            max-width: 500px;
        }
        .icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }
        p {
            font-size: 1.1rem;
            margin-bottom: 2rem;
            opacity: 0.9;
            line-height: 1.6;
        }
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }
        .features {
            margin-top: 3rem;
            text-align: left;
        }
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            opacity: 0.8;
        }
        .feature-icon {
            margin-right: 0.5rem;
            font-size: 1.2rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📱</div>
        <h1>You're Offline</h1>
        <p>
            Don't worry! FoodScan works offline too. You can still scan QR codes and the data will sync when you're back online.
        </p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            Try Again
        </button>
        
        <div class="features">
            <div class="feature">
                <span class="feature-icon">📷</span>
                <span>QR code scanning works offline</span>
            </div>
            <div class="feature">
                <span class="feature-icon">💾</span>
                <span>Data is saved locally</span>
            </div>
            <div class="feature">
                <span class="feature-icon">🔄</span>
                <span>Auto-sync when back online</span>
            </div>
        </div>
    </div>

    <script>
        // Check for connectivity and reload when back online
        window.addEventListener('online', function() {
            window.location.reload();
        });

        // Show connection status
        function updateConnectionStatus() {
            if (navigator.onLine) {
                window.location.reload();
            }
        }

        // Check connection status periodically
        setInterval(updateConnectionStatus, 5000);
    </script>
</body>
</html>
