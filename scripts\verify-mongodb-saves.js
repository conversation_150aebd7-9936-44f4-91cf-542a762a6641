const { MongoClient, ObjectId } = require('mongodb');

const url = 'mongodb://localhost:27017';
const dbName = 'foodscan';

async function verifyMongoDBSaves() {
  const client = new MongoClient(url);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db(dbName);
    const eventsCollection = db.collection('events');
    const mealTypesCollection = db.collection('meal_types');
    
    console.log('📊 Current Database State:');
    
    // Count existing events
    const eventCount = await eventsCollection.countDocuments();
    console.log(`- Total Events: ${eventCount}`);
    
    // Count existing meal types
    const mealTypeCount = await mealTypesCollection.countDocuments();
    console.log(`- Total Meal Types: ${mealTypeCount}`);
    
    // Show recent events
    const recentEvents = await eventsCollection.find({})
      .sort({ createdAt: -1 })
      .limit(5)
      .toArray();
    
    console.log('\n📋 Recent Events:');
    recentEvents.forEach((event, index) => {
      console.log(`${index + 1}. ${event.name} (ID: ${event._id.toString()})`);
      console.log(`   Created: ${event.createdAt ? event.createdAt.toISOString() : 'Unknown'}`);
      console.log(`   Active: ${event.isActive}`);
    });
    
    // Test creating a new event (simulating API call)
    console.log('\n🧪 Testing Event Creation...');
    
    const testEventId = new ObjectId();
    const testEvent = {
      _id: testEventId,
      name: 'MongoDB Save Test Event',
      description: 'Testing direct MongoDB saves',
      startDate: new Date('2024-03-20T09:00:00Z'),
      endDate: new Date('2024-03-20T17:00:00Z'),
      serviceStartTime: new Date('1970-01-01T12:00:00Z'),
      serviceEndTime: new Date('1970-01-01T14:00:00Z'),
      primaryColor: '#ff6b6b',
      secondaryColor: '#ee5a52',
      logoUrl: null,
      isActive: true,
      creatorId: 'test-user',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Insert test event
    const insertResult = await eventsCollection.insertOne(testEvent);
    console.log('✅ Test event inserted:', insertResult.insertedId.toString());
    
    // Create test meal types
    const testMealTypes = [
      {
        _id: new ObjectId(),
        name: 'Test Main Course',
        description: 'Test main course description',
        maxQuantityPerAttendee: 1,
        totalQuantity: 100,
        consumedQuantity: 0,
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: false,
        isAvailable: true,
        eventId: testEventId.toString(),
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        _id: new ObjectId(),
        name: 'Test Vegetarian Option',
        description: 'Test vegetarian meal',
        maxQuantityPerAttendee: 1,
        totalQuantity: 50,
        consumedQuantity: 0,
        isVegetarian: true,
        isVegan: true,
        isGlutenFree: true,
        isAvailable: true,
        eventId: testEventId.toString(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    const mealTypeInsertResult = await mealTypesCollection.insertMany(testMealTypes);
    console.log('✅ Test meal types inserted:', mealTypeInsertResult.insertedCount);
    
    // Verify the data was saved
    console.log('\n🔍 Verifying Saved Data...');
    
    const savedEvent = await eventsCollection.findOne({ _id: testEventId });
    const savedMealTypes = await mealTypesCollection.find({ eventId: testEventId.toString() }).toArray();
    
    console.log('📋 Saved Event Details:');
    console.log(`- Name: ${savedEvent.name}`);
    console.log(`- Description: ${savedEvent.description}`);
    console.log(`- Start Date: ${savedEvent.startDate.toISOString()}`);
    console.log(`- End Date: ${savedEvent.endDate.toISOString()}`);
    console.log(`- Primary Color: ${savedEvent.primaryColor}`);
    console.log(`- Is Active: ${savedEvent.isActive}`);
    console.log(`- Creator ID: ${savedEvent.creatorId}`);
    console.log(`- Created At: ${savedEvent.createdAt.toISOString()}`);
    
    console.log('\n🍽️ Saved Meal Types:');
    savedMealTypes.forEach((mt, index) => {
      console.log(`${index + 1}. ${mt.name}`);
      console.log(`   Description: ${mt.description}`);
      console.log(`   Max per person: ${mt.maxQuantityPerAttendee}`);
      console.log(`   Total quantity: ${mt.totalQuantity}`);
      console.log(`   Dietary: ${mt.isVegetarian ? 'V' : ''}${mt.isVegan ? 'VG' : ''}${mt.isGlutenFree ? 'GF' : ''}`);
      console.log(`   Event ID: ${mt.eventId}`);
    });
    
    // Test updating the event
    console.log('\n🔄 Testing Event Update...');
    
    const updateResult = await eventsCollection.updateOne(
      { _id: testEventId },
      {
        $set: {
          name: 'MongoDB Save Test Event (Updated)',
          description: 'Updated description',
          primaryColor: '#00ff00',
          updatedAt: new Date()
        }
      }
    );
    
    console.log('✅ Event updated:', updateResult.modifiedCount > 0);
    
    // Verify update
    const updatedEvent = await eventsCollection.findOne({ _id: testEventId });
    console.log('📋 Updated Event:');
    console.log(`- Name: ${updatedEvent.name}`);
    console.log(`- Description: ${updatedEvent.description}`);
    console.log(`- Primary Color: ${updatedEvent.primaryColor}`);
    console.log(`- Updated At: ${updatedEvent.updatedAt.toISOString()}`);
    
    // Final counts
    console.log('\n📊 Final Database State:');
    const finalEventCount = await eventsCollection.countDocuments();
    const finalMealTypeCount = await mealTypesCollection.countDocuments();
    console.log(`- Total Events: ${finalEventCount} (was ${eventCount})`);
    console.log(`- Total Meal Types: ${finalMealTypeCount} (was ${mealTypeCount})`);
    
    // Cleanup test data
    console.log('\n🧹 Cleaning up test data...');
    await eventsCollection.deleteOne({ _id: testEventId });
    await mealTypesCollection.deleteMany({ eventId: testEventId.toString() });
    console.log('✅ Test data cleaned up');
    
    console.log('\n🎉 MongoDB Save Verification Complete!');
    console.log('✅ Events are being saved to MongoDB correctly');
    console.log('✅ Meal types are being saved to MongoDB correctly');
    console.log('✅ Updates are working correctly');
    console.log('✅ All CRUD operations functional');
    
  } catch (error) {
    console.error('❌ Error during MongoDB verification:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the verification
verifyMongoDBSaves().catch(console.error);
