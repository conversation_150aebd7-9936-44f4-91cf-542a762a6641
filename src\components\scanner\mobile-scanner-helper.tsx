"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>ert, AlertDescription } from "@/components/ui/alert"
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  Wifi, 
  WifiOff,
  Battery,
  Signal,
  Info
} from "lucide-react"

interface DeviceInfo {
  isMobile: boolean
  isTablet: boolean
  isDesktop: boolean
  browser: string
  os: string
  isOnline: boolean
  hasCamera: boolean
  hasTouchScreen: boolean
  screenSize: string
  orientation: string
}

export function MobileScannerHelper() {
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null)
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    const detectDevice = () => {
      const userAgent = navigator.userAgent.toLowerCase()
      const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
      const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent)
      const isDesktop = !isMobile && !isTablet

      // Detect browser
      let browser = 'Unknown'
      if (userAgent.includes('chrome')) browser = 'Chrome'
      else if (userAgent.includes('firefox')) browser = 'Firefox'
      else if (userAgent.includes('safari')) browser = 'Safari'
      else if (userAgent.includes('edge')) browser = 'Edge'

      // Detect OS
      let os = 'Unknown'
      if (userAgent.includes('android')) os = 'Android'
      else if (userAgent.includes('iphone') || userAgent.includes('ipad')) os = 'iOS'
      else if (userAgent.includes('windows')) os = 'Windows'
      else if (userAgent.includes('mac')) os = 'macOS'
      else if (userAgent.includes('linux')) os = 'Linux'

      // Check capabilities
      const hasCamera = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
      const hasTouchScreen = 'ontouchstart' in window || navigator.maxTouchPoints > 0
      const isOnline = navigator.onLine

      // Screen info
      const screenSize = `${window.screen.width}x${window.screen.height}`
      const orientation = window.screen.orientation?.type || 
                         (window.innerWidth > window.innerHeight ? 'landscape' : 'portrait')

      setDeviceInfo({
        isMobile,
        isTablet,
        isDesktop,
        browser,
        os,
        isOnline,
        hasCamera,
        hasTouchScreen,
        screenSize,
        orientation
      })
    }

    detectDevice()

    // Listen for orientation changes
    const handleOrientationChange = () => {
      setTimeout(detectDevice, 100) // Small delay to ensure screen dimensions are updated
    }

    window.addEventListener('orientationchange', handleOrientationChange)
    window.addEventListener('resize', handleOrientationChange)
    window.addEventListener('online', detectDevice)
    window.addEventListener('offline', detectDevice)

    return () => {
      window.removeEventListener('orientationchange', handleOrientationChange)
      window.removeEventListener('resize', handleOrientationChange)
      window.removeEventListener('online', detectDevice)
      window.removeEventListener('offline', detectDevice)
    }
  }, [])

  if (!deviceInfo || !isVisible) return null

  const getDeviceIcon = () => {
    if (deviceInfo.isMobile) return <Smartphone className="h-4 w-4" />
    if (deviceInfo.isTablet) return <Tablet className="h-4 w-4" />
    return <Monitor className="h-4 w-4" />
  }

  const getOptimizationTips = () => {
    const tips = []

    if (deviceInfo.isMobile) {
      tips.push("📱 Hold your phone steady and ensure good lighting")
      tips.push("🔄 Try both portrait and landscape orientations")
      if (deviceInfo.os === 'iOS') {
        tips.push("🍎 iOS: Make sure Safari has camera permissions")
      } else if (deviceInfo.os === 'Android') {
        tips.push("🤖 Android: Chrome works best for camera access")
      }
    }

    if (deviceInfo.isTablet) {
      tips.push("📱 Tablets work great for QR scanning")
      tips.push("🔄 Landscape mode often provides better stability")
    }

    if (deviceInfo.isDesktop) {
      tips.push("💻 Desktop scanning works with external cameras")
      tips.push("📷 Built-in webcams work but may need good lighting")
    }

    if (!deviceInfo.isOnline) {
      tips.push("📶 You're offline - some features may be limited")
    }

    if (!deviceInfo.hasCamera) {
      tips.push("📷 No camera detected - use image upload instead")
    }

    return tips
  }

  return (
    <Card className="mb-4">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2 text-sm">
            {getDeviceIcon()}
            <span>Device Information</span>
          </CardTitle>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setIsVisible(false)}
            className="h-6 w-6 p-0"
          >
            ×
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {/* Device Status */}
        <div className="flex flex-wrap gap-2">
          <Badge variant="outline" className="text-xs">
            {getDeviceIcon()}
            <span className="ml-1">
              {deviceInfo.isMobile ? 'Mobile' : deviceInfo.isTablet ? 'Tablet' : 'Desktop'}
            </span>
          </Badge>
          
          <Badge variant="outline" className="text-xs">
            {deviceInfo.browser} on {deviceInfo.os}
          </Badge>
          
          <Badge variant={deviceInfo.isOnline ? "default" : "destructive"} className="text-xs">
            {deviceInfo.isOnline ? <Wifi className="h-3 w-3 mr-1" /> : <WifiOff className="h-3 w-3 mr-1" />}
            {deviceInfo.isOnline ? 'Online' : 'Offline'}
          </Badge>
          
          <Badge variant={deviceInfo.hasCamera ? "default" : "secondary"} className="text-xs">
            📷 Camera {deviceInfo.hasCamera ? 'Available' : 'Not Found'}
          </Badge>
        </div>

        {/* Screen Info */}
        <div className="text-xs text-muted-foreground">
          📐 {deviceInfo.screenSize} • {deviceInfo.orientation} • {deviceInfo.hasTouchScreen ? 'Touch' : 'Mouse'}
        </div>

        {/* Optimization Tips */}
        {getOptimizationTips().length > 0 && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-1">
                <div className="font-medium text-sm">Optimization Tips:</div>
                {getOptimizationTips().map((tip, index) => (
                  <div key={index} className="text-xs">{tip}</div>
                ))}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Browser-specific warnings */}
        {!deviceInfo.hasCamera && (
          <Alert>
            <AlertDescription className="text-xs">
              <strong>Camera not available:</strong> This could be due to browser restrictions, 
              missing permissions, or hardware limitations. Try the image upload option instead.
            </AlertDescription>
          </Alert>
        )}

        {deviceInfo.browser === 'Safari' && deviceInfo.os === 'iOS' && (
          <Alert>
            <AlertDescription className="text-xs">
              <strong>iOS Safari:</strong> Make sure to allow camera access when prompted. 
              If you denied it previously, go to Settings → Safari → Camera and enable it.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
