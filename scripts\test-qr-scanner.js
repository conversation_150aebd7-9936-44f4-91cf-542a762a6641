const { MongoClient } = require('mongodb');
const QRCode = require('qrcode');
const fs = require('fs');
const path = require('path');

const MONGODB_URI = "mongodb://localhost:27017";
const DB_NAME = "foodscan";

async function testQRScanner() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db(DB_NAME);
    const attendeesCollection = db.collection('attendees');
    
    console.log('\n📱 Testing QR Scanner Functionality...\n');
    
    // 1. Get existing attendees with QR codes
    console.log('1️⃣ Testing QR CODE GENERATION...');
    const attendees = await attendeesCollection.find({}).toArray();
    
    if (attendees.length === 0) {
      console.log('❌ No attendees found. Please run the seed script first.');
      return;
    }
    
    console.log(`✅ Found ${attendees.length} attendees with QR codes:`);
    attendees.forEach(attendee => {
      console.log(`   - ${attendee.name}: ${attendee.qrCode}`);
    });
    
    // 2. Generate QR code images for testing
    console.log('\n2️⃣ Generating QR CODE IMAGES for testing...');
    const testDir = path.join(__dirname, '../public/test-qr-codes');
    
    // Create test directory if it doesn't exist
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }
    
    for (const attendee of attendees) {
      try {
        const qrCodePath = path.join(testDir, `${attendee.qrCode}.png`);
        await QRCode.toFile(qrCodePath, attendee.qrCode, {
          width: 300,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        });
        console.log(`✅ Generated QR code image: ${attendee.qrCode}.png`);
      } catch (error) {
        console.error(`❌ Failed to generate QR code for ${attendee.name}:`, error.message);
      }
    }
    
    // 3. Test QR code validation
    console.log('\n3️⃣ Testing QR CODE VALIDATION...');
    
    // Test valid QR codes
    for (const attendee of attendees.slice(0, 2)) { // Test first 2
      const foundAttendee = await attendeesCollection.findOne({ qrCode: attendee.qrCode });
      if (foundAttendee) {
        console.log(`✅ Valid QR code: ${attendee.qrCode} -> ${foundAttendee.name}`);
      } else {
        console.log(`❌ QR code validation failed for: ${attendee.qrCode}`);
      }
    }
    
    // Test invalid QR codes
    const invalidCodes = ['INVALID-001', 'TEST-FAKE-123', 'NONEXISTENT-QR'];
    for (const invalidCode of invalidCodes) {
      const foundAttendee = await attendeesCollection.findOne({ qrCode: invalidCode });
      if (!foundAttendee) {
        console.log(`✅ Invalid QR code correctly rejected: ${invalidCode}`);
      } else {
        console.log(`❌ Invalid QR code incorrectly accepted: ${invalidCode}`);
      }
    }
    
    // 4. Test QR code formats and variations
    console.log('\n4️⃣ Testing QR CODE FORMATS...');
    
    // Generate different format QR codes for testing
    const testFormats = [
      { data: 'JD-2024-001', description: 'Standard format' },
      { data: 'https://example.com/attendee/JD-2024-001', description: 'URL format' },
      { data: JSON.stringify({ attendeeId: 'JD-2024-001', event: 'tech-conf' }), description: 'JSON format' },
      { data: 'BEGIN:VCARD\nFN:John Doe\nORG:Tech Conference\nEND:VCARD', description: 'vCard format' }
    ];
    
    for (const format of testFormats) {
      try {
        const formatPath = path.join(testDir, `test-${format.description.replace(/\s+/g, '-').toLowerCase()}.png`);
        await QRCode.toFile(formatPath, format.data, {
          width: 300,
          margin: 2
        });
        console.log(`✅ Generated ${format.description} QR code`);
      } catch (error) {
        console.error(`❌ Failed to generate ${format.description} QR code:`, error.message);
      }
    }
    
    // 5. Test different QR code sizes
    console.log('\n5️⃣ Testing QR CODE SIZES...');
    
    const testSizes = [
      { width: 100, name: 'small' },
      { width: 200, name: 'medium' },
      { width: 400, name: 'large' },
      { width: 600, name: 'extra-large' }
    ];
    
    const testQRCode = attendees[0].qrCode;
    for (const size of testSizes) {
      try {
        const sizePath = path.join(testDir, `test-${size.name}-${size.width}px.png`);
        await QRCode.toFile(sizePath, testQRCode, {
          width: size.width,
          margin: 2
        });
        console.log(`✅ Generated ${size.name} QR code (${size.width}px)`);
      } catch (error) {
        console.error(`❌ Failed to generate ${size.name} QR code:`, error.message);
      }
    }
    
    // 6. Test QR code with different error correction levels
    console.log('\n6️⃣ Testing ERROR CORRECTION LEVELS...');
    
    const errorLevels = ['L', 'M', 'Q', 'H']; // Low, Medium, Quartile, High
    for (const level of errorLevels) {
      try {
        const levelPath = path.join(testDir, `test-error-level-${level}.png`);
        await QRCode.toFile(levelPath, testQRCode, {
          width: 300,
          margin: 2,
          errorCorrectionLevel: level
        });
        console.log(`✅ Generated QR code with error correction level ${level}`);
      } catch (error) {
        console.error(`❌ Failed to generate QR code with error level ${level}:`, error.message);
      }
    }
    
    // 7. Performance testing
    console.log('\n7️⃣ Testing QR CODE LOOKUP PERFORMANCE...');
    
    const performanceTests = 100;
    const startTime = Date.now();
    
    for (let i = 0; i < performanceTests; i++) {
      const randomAttendee = attendees[Math.floor(Math.random() * attendees.length)];
      await attendeesCollection.findOne({ qrCode: randomAttendee.qrCode });
    }
    
    const endTime = Date.now();
    const avgTime = (endTime - startTime) / performanceTests;
    
    console.log(`✅ Performed ${performanceTests} QR code lookups`);
    console.log(`   Total time: ${endTime - startTime}ms`);
    console.log(`   Average time per lookup: ${avgTime.toFixed(2)}ms`);
    
    if (avgTime < 10) {
      console.log('   🚀 Excellent performance!');
    } else if (avgTime < 50) {
      console.log('   ✅ Good performance');
    } else {
      console.log('   ⚠️ Performance could be improved');
    }
    
    // 8. Generate test instructions
    console.log('\n8️⃣ Generating TEST INSTRUCTIONS...');
    
    const instructions = `
# QR Scanner Testing Instructions

## Generated Test Files
The following QR code images have been generated in /public/test-qr-codes/:

### Valid Attendee QR Codes:
${attendees.map(a => `- ${a.qrCode}.png (${a.name})`).join('\n')}

### Test Format QR Codes:
- test-standard-format.png
- test-url-format.png  
- test-json-format.png
- test-vcard-format.png

### Test Size QR Codes:
- test-small-100px.png
- test-medium-200px.png
- test-large-400px.png
- test-extra-large-600px.png

### Error Correction Level QR Codes:
- test-error-level-L.png (Low)
- test-error-level-M.png (Medium)
- test-error-level-Q.png (Quartile)
- test-error-level-H.png (High)

## Testing Steps:

1. **Camera Access Testing:**
   - Open /scanner page in different browsers
   - Test on desktop and mobile devices
   - Verify camera permission prompts work correctly

2. **QR Code Scanning Testing:**
   - Use the generated QR code images above
   - Test scanning from screen and printed copies
   - Verify different sizes and formats work
   - Test in various lighting conditions

3. **Mobile Compatibility Testing:**
   - Test on iOS Safari, Chrome on Android
   - Verify camera switching works (front/back)
   - Test device orientation changes
   - Verify touch controls work properly

4. **Error Handling Testing:**
   - Deny camera permissions and verify fallback
   - Test with invalid QR codes
   - Test image upload functionality
   - Verify error messages are user-friendly

5. **Performance Testing:**
   - Measure scan speed and accuracy
   - Test with multiple QR codes quickly
   - Verify no memory leaks during extended use

## Expected Results:
- Valid attendee QR codes should return attendee information
- Invalid QR codes should show appropriate error messages
- Camera should initialize within 5-10 seconds
- QR code detection should be near-instantaneous
- All fallback mechanisms should work smoothly
`;

    const instructionsPath = path.join(testDir, 'TESTING_INSTRUCTIONS.md');
    fs.writeFileSync(instructionsPath, instructions);
    console.log('✅ Generated testing instructions: TESTING_INSTRUCTIONS.md');
    
    console.log('\n🎉 QR Scanner testing setup completed!');
    console.log(`📁 Test files generated in: ${testDir}`);
    console.log('🔗 Open http://localhost:3000/scanner to test the scanner');
    
  } catch (error) {
    console.error('❌ Error during QR scanner testing:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testQRScanner().catch(console.error);
