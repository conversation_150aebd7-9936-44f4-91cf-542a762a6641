"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Al<PERSON>, AlertDescription } from "@/components/ui/alert"
import { 
  Camera, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Info,
  Monitor,
  Smartphone,
  Wifi,
  WifiOff
} from "lucide-react"

interface DiagnosticResult {
  test: string
  status: 'pass' | 'fail' | 'warning' | 'info'
  message: string
  details?: string
}

export function CameraDiagnostics() {
  const [results, setResults] = useState<DiagnosticResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const [isVisible, setIsVisible] = useState(false)

  const runDiagnostics = async () => {
    setIsRunning(true)
    const diagnosticResults: DiagnosticResult[] = []

    // Test 1: Browser Support
    try {
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        diagnosticResults.push({
          test: 'Browser Support',
          status: 'pass',
          message: 'MediaDevices API is supported',
          details: 'Your browser supports camera access'
        })
      } else {
        diagnosticResults.push({
          test: 'Browser Support',
          status: 'fail',
          message: 'MediaDevices API not supported',
          details: 'Your browser does not support camera access. Try Chrome, Firefox, or Safari.'
        })
      }
    } catch (error) {
      diagnosticResults.push({
        test: 'Browser Support',
        status: 'fail',
        message: 'Error checking browser support',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    // Test 2: HTTPS Check
    if (location.protocol === 'https:' || location.hostname === 'localhost') {
      diagnosticResults.push({
        test: 'HTTPS/Localhost',
        status: 'pass',
        message: 'Secure context detected',
        details: 'Camera access is allowed on HTTPS or localhost'
      })
    } else {
      diagnosticResults.push({
        test: 'HTTPS/Localhost',
        status: 'fail',
        message: 'Insecure context',
        details: 'Camera access requires HTTPS. Try accessing via https:// or localhost'
      })
    }

    // Test 3: Device Detection
    try {
      const devices = await navigator.mediaDevices.enumerateDevices()
      const videoDevices = devices.filter(device => device.kind === 'videoinput')
      
      if (videoDevices.length > 0) {
        diagnosticResults.push({
          test: 'Camera Detection',
          status: 'pass',
          message: `Found ${videoDevices.length} camera(s)`,
          details: videoDevices.map(d => d.label || 'Unknown Camera').join(', ')
        })
      } else {
        diagnosticResults.push({
          test: 'Camera Detection',
          status: 'fail',
          message: 'No cameras detected',
          details: 'No video input devices found on this system'
        })
      }
    } catch (error) {
      diagnosticResults.push({
        test: 'Camera Detection',
        status: 'warning',
        message: 'Could not enumerate devices',
        details: 'This might be due to permission restrictions'
      })
    }

    // Test 4: Permission Test
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ 
        video: { facingMode: 'environment' } 
      })
      
      diagnosticResults.push({
        test: 'Camera Permission',
        status: 'pass',
        message: 'Camera access granted',
        details: 'Successfully obtained camera stream'
      })
      
      // Clean up
      stream.getTracks().forEach(track => track.stop())
    } catch (error) {
      const err = error as any
      if (err.name === 'NotAllowedError') {
        diagnosticResults.push({
          test: 'Camera Permission',
          status: 'fail',
          message: 'Camera permission denied',
          details: 'User denied camera access or browser blocked it'
        })
      } else if (err.name === 'NotFoundError') {
        diagnosticResults.push({
          test: 'Camera Permission',
          status: 'fail',
          message: 'No camera found',
          details: 'No camera device available'
        })
      } else {
        diagnosticResults.push({
          test: 'Camera Permission',
          status: 'fail',
          message: 'Camera access failed',
          details: err.message || 'Unknown error'
        })
      }
    }

    // Test 5: QR Scanner Library
    try {
      const QrScanner = (await import('qr-scanner')).default
      diagnosticResults.push({
        test: 'QR Scanner Library',
        status: 'pass',
        message: 'QR Scanner library loaded',
        details: 'qr-scanner library is available'
      })
    } catch (error) {
      diagnosticResults.push({
        test: 'QR Scanner Library',
        status: 'fail',
        message: 'QR Scanner library failed to load',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    }

    // Test 6: Device Info
    const userAgent = navigator.userAgent.toLowerCase()
    const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent)
    const isTablet = /ipad|android(?!.*mobile)/i.test(userAgent)
    
    let browser = 'Unknown'
    if (userAgent.includes('chrome')) browser = 'Chrome'
    else if (userAgent.includes('firefox')) browser = 'Firefox'
    else if (userAgent.includes('safari')) browser = 'Safari'
    else if (userAgent.includes('edge')) browser = 'Edge'

    diagnosticResults.push({
      test: 'Device Information',
      status: 'info',
      message: `${browser} on ${isMobile ? 'Mobile' : isTablet ? 'Tablet' : 'Desktop'}`,
      details: `Screen: ${window.screen.width}x${window.screen.height}, Online: ${navigator.onLine}`
    })

    setResults(diagnosticResults)
    setIsRunning(false)
  }

  const getStatusIcon = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'fail':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'info':
        return <Info className="h-4 w-4 text-blue-600" />
    }
  }

  const getStatusBadge = (status: DiagnosticResult['status']) => {
    switch (status) {
      case 'pass':
        return <Badge variant="default" className="bg-green-600">Pass</Badge>
      case 'fail':
        return <Badge variant="destructive">Fail</Badge>
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-600 text-white">Warning</Badge>
      case 'info':
        return <Badge variant="outline">Info</Badge>
    }
  }

  if (!isVisible) {
    return (
      <div className="mb-4">
        <Button 
          variant="outline" 
          size="sm" 
          onClick={() => setIsVisible(true)}
        >
          <Camera className="h-4 w-4 mr-2" />
          Run Camera Diagnostics
        </Button>
      </div>
    )
  }

  return (
    <Card className="mb-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Camera className="h-5 w-5" />
              <span>Camera Diagnostics</span>
            </CardTitle>
            <CardDescription>
              Diagnose camera access issues and compatibility
            </CardDescription>
          </div>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={() => setIsVisible(false)}
          >
            ×
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <Button 
            onClick={runDiagnostics} 
            disabled={isRunning}
            size="sm"
          >
            {isRunning ? 'Running...' : 'Run Diagnostics'}
          </Button>
        </div>

        {results.length > 0 && (
          <div className="space-y-3">
            {results.map((result, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 border rounded-lg">
                {getStatusIcon(result.status)}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium">{result.test}</h4>
                    {getStatusBadge(result.status)}
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">{result.message}</p>
                  {result.details && (
                    <p className="text-xs text-muted-foreground mt-1">{result.details}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {results.length > 0 && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>Next Steps:</strong> If you see any failed tests, try the suggested solutions or use the "Simple Camera Mode" or "Upload Image" options as alternatives.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
