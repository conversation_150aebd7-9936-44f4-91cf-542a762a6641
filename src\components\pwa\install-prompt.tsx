"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Download, 
  Smartphone, 
  X,
  CheckCircle,
  Wifi,
  Camera,
  Bell
} from "lucide-react"
import { installPWA, isPWA } from "@/lib/pwa"

export function PWAInstallPrompt() {
  const [showPrompt, setShowPrompt] = useState(false)
  const [isInstalling, setIsInstalling] = useState(false)
  const [isInstalled, setIsInstalled] = useState(false)

  useEffect(() => {
    // Check if already installed
    if (isPWA()) {
      setIsInstalled(true)
      return
    }

    // Listen for install prompt availability
    const handleInstallAvailable = () => {
      setShowPrompt(true)
    }

    const handleInstalled = () => {
      setIsInstalled(true)
      setShowPrompt(false)
    }

    window.addEventListener('pwa-install-available', handleInstallAvailable)
    window.addEventListener('pwa-installed', handleInstalled)

    return () => {
      window.removeEventListener('pwa-install-available', handleInstallAvailable)
      window.removeEventListener('pwa-installed', handleInstalled)
    }
  }, [])

  const handleInstall = async () => {
    setIsInstalling(true)
    try {
      const installed = await installPWA()
      if (installed) {
        setIsInstalled(true)
        setShowPrompt(false)
      }
    } catch (error) {
      console.error('PWA installation failed:', error)
    } finally {
      setIsInstalling(false)
    }
  }

  const handleDismiss = () => {
    setShowPrompt(false)
    // Don't show again for this session
    sessionStorage.setItem('pwa-prompt-dismissed', 'true')
  }

  // Don't show if already installed or dismissed
  if (isInstalled || !showPrompt || sessionStorage.getItem('pwa-prompt-dismissed')) {
    return null
  }

  return (
    <Card className="fixed bottom-4 right-4 w-80 z-50 shadow-lg border-primary">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Smartphone className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">Install FoodScan</CardTitle>
          </div>
          <Button variant="ghost" size="sm" onClick={handleDismiss}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        <CardDescription>
          Install our app for the best scanning experience
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Features */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2 text-sm">
            <Wifi className="h-4 w-4 text-green-500" />
            <span>Works offline</span>
          </div>
          <div className="flex items-center space-x-2 text-sm">
            <Camera className="h-4 w-4 text-blue-500" />
            <span>Fast camera access</span>
          </div>
          <div className="flex items-center space-x-2 text-sm">
            <Bell className="h-4 w-4 text-purple-500" />
            <span>Push notifications</span>
          </div>
        </div>

        {/* Install Button */}
        <div className="flex space-x-2">
          <Button 
            onClick={handleInstall} 
            disabled={isInstalling}
            className="flex-1"
          >
            {isInstalling ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                Installing...
              </>
            ) : (
              <>
                <Download className="mr-2 h-4 w-4" />
                Install App
              </>
            )}
          </Button>
          <Button variant="outline" onClick={handleDismiss}>
            Later
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export function PWAStatus() {
  const [isInstalled, setIsInstalled] = useState(false)
  const [isOnline, setIsOnline] = useState(true)

  useEffect(() => {
    setIsInstalled(isPWA())
    setIsOnline(navigator.onLine)

    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  if (!isInstalled) return null

  return (
    <div className="flex items-center space-x-2">
      <Badge variant={isOnline ? "success" : "warning"}>
        {isOnline ? (
          <>
            <Wifi className="h-3 w-3 mr-1" />
            Online
          </>
        ) : (
          <>
            <Wifi className="h-3 w-3 mr-1" />
            Offline
          </>
        )}
      </Badge>
      <Badge variant="info">
        <Smartphone className="h-3 w-3 mr-1" />
        PWA
      </Badge>
    </div>
  )
}
