"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

export default function TestScannerPage() {
  const videoRef = useRef<HTMLVideoElement>(null)
  const [status, setStatus] = useState<string>('Not started')
  const [error, setError] = useState<string | null>(null)
  const [scanResult, setScanResult] = useState<string | null>(null)
  const [isScanning, setIsScanning] = useState(false)

  const startBasicCamera = async () => {
    setStatus('Starting basic camera...')
    setError(null)
    
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' }
      })
      
      if (videoRef.current) {
        videoRef.current.srcObject = stream
        videoRef.current.play()
        setStatus('Basic camera started')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Camera failed')
      setStatus('Camera failed')
    }
  }

  const startQRScanner = async () => {
    setStatus('Loading QR Scanner library...')
    setError(null)
    setIsScanning(true)
    
    try {
      // Dynamic import
      const QrScanner = (await import('qr-scanner')).default
      setStatus('QR Scanner library loaded')
      
      if (!videoRef.current) {
        throw new Error('Video element not found')
      }

      setStatus('Creating scanner instance...')
      
      const scanner = new QrScanner(
        videoRef.current,
        (result) => {
          console.log('QR Code detected:', result.data)
          setScanResult(result.data)
          setStatus(`QR Code found: ${result.data}`)
        },
        {
          onDecodeError: (error) => {
            console.debug('Decode error (normal):', error.message)
          },
          highlightScanRegion: true,
          highlightCodeOutline: true,
          preferredCamera: 'environment',
          maxScansPerSecond: 2
        }
      )

      setStatus('Starting scanner...')
      await scanner.start()
      setStatus('QR Scanner active and ready!')
      
      // Cleanup function
      return () => {
        scanner.stop()
        scanner.destroy()
      }
      
    } catch (err) {
      console.error('QR Scanner error:', err)
      setError(err instanceof Error ? err.message : 'QR Scanner failed')
      setStatus('QR Scanner failed')
      setIsScanning(false)
    }
  }

  const testImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setStatus('Scanning uploaded image...')
    
    try {
      const QrScanner = (await import('qr-scanner')).default
      const result = await QrScanner.scanImage(file)
      setScanResult(result)
      setStatus(`QR Code found in image: ${result}`)
    } catch (err) {
      setError('No QR code found in uploaded image')
      setStatus('No QR code found in image')
    }

    e.target.value = ""
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold">QR Scanner Test Page</h1>
        <p className="text-muted-foreground mt-2">
          Minimal test environment for debugging QR scanner issues
        </p>
      </div>

      {/* Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Scanner Status</span>
            <Badge variant={error ? "destructive" : isScanning ? "default" : "secondary"}>
              {error ? "Error" : isScanning ? "Active" : "Inactive"}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm">
            <strong>Status:</strong> {status}
          </p>
          {error && (
            <p className="text-sm text-red-600 mt-2">
              <strong>Error:</strong> {error}
            </p>
          )}
          {scanResult && (
            <p className="text-sm text-green-600 mt-2">
              <strong>Last Scan:</strong> {scanResult}
            </p>
          )}
        </CardContent>
      </Card>

      {/* Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Test Controls</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button onClick={startBasicCamera}>
              Test Basic Camera
            </Button>
            <Button onClick={startQRScanner}>
              Start QR Scanner
            </Button>
            <Button variant="outline" onClick={() => window.location.reload()}>
              Reset Page
            </Button>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">
              Test Image Upload:
            </label>
            <input
              type="file"
              accept="image/*"
              onChange={testImageUpload}
              className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
            />
          </div>
        </CardContent>
      </Card>

      {/* Video Display */}
      <Card>
        <CardHeader>
          <CardTitle>Camera Feed</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
            <video
              ref={videoRef}
              className="w-full h-full object-cover"
              playsInline
              muted
              autoPlay
            />
            
            {/* Simple overlay */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="w-48 h-48 border-2 border-white rounded-lg"></div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Debug Info */}
      <Card>
        <CardHeader>
          <CardTitle>Debug Information</CardTitle>
        </CardHeader>
        <CardContent className="text-sm space-y-2">
          <p><strong>User Agent:</strong> {navigator.userAgent}</p>
          <p><strong>Protocol:</strong> {window.location.protocol}</p>
          <p><strong>Host:</strong> {window.location.host}</p>
          <p><strong>MediaDevices Support:</strong> {navigator.mediaDevices ? 'Yes' : 'No'}</p>
          <p><strong>getUserMedia Support:</strong> {navigator.mediaDevices?.getUserMedia ? 'Yes' : 'No'}</p>
        </CardContent>
      </Card>
    </div>
  )
}
