"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Plus, Edit, Trash2, Utensils } from "lucide-react"

interface MealType {
  id: string
  name: string
  description: string
  maxQuantityPerAttendee: number
  isAvailable: boolean
  availableFrom?: string
  availableTo?: string
  isVegetarian: boolean
  isVegan: boolean
  isGlutenFree: boolean
  totalQuantity?: number
  consumedQuantity: number
}

interface MealConfigurationProps {
  eventId: string
  mealTypes: MealType[]
  onAddMealType: (mealType: Omit<MealType, "id" | "consumedQuantity">) => void
  onUpdateMealType: (id: string, mealType: Partial<MealType>) => void
  onDeleteMealType: (id: string) => void
}

export function MealConfiguration({
  eventId,
  mealTypes,
  onAddMealType,
  onUpdateMealType,
  onDeleteMealType
}: MealConfigurationProps) {
  const [isAddingMeal, setIsAddingMeal] = useState(false)
  const [editingMeal, setEditingMeal] = useState<string | null>(null)
  const [newMeal, setNewMeal] = useState<Omit<MealType, "id" | "consumedQuantity">>({
    name: "",
    description: "",
    maxQuantityPerAttendee: 1,
    isAvailable: true,
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
  })

  const handleAddMeal = () => {
    if (newMeal.name.trim()) {
      onAddMealType(newMeal)
      setNewMeal({
        name: "",
        description: "",
        maxQuantityPerAttendee: 1,
        isAvailable: true,
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: false,
      })
      setIsAddingMeal(false)
    }
  }

  const getDietaryBadges = (mealType: MealType) => {
    const badges = []
    if (mealType.isVegetarian) badges.push({ label: "Vegetarian", variant: "success" as const })
    if (mealType.isVegan) badges.push({ label: "Vegan", variant: "success" as const })
    if (mealType.isGlutenFree) badges.push({ label: "Gluten-Free", variant: "info" as const })
    return badges
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Meal Configuration</CardTitle>
              <CardDescription>
                Configure meal types, quantities, and dietary options for this event
              </CardDescription>
            </div>
            <Button onClick={() => setIsAddingMeal(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Meal Type
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Add New Meal Form */}
          {isAddingMeal && (
            <Card className="border-dashed">
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="mealName">Meal Name *</Label>
                      <Input
                        id="mealName"
                        value={newMeal.name}
                        onChange={(e) => setNewMeal({ ...newMeal, name: e.target.value })}
                        placeholder="e.g., Main Course, Dessert"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="maxQuantity">Max Quantity per Attendee</Label>
                      <Input
                        id="maxQuantity"
                        type="number"
                        min="1"
                        value={newMeal.maxQuantityPerAttendee}
                        onChange={(e) => setNewMeal({ ...newMeal, maxQuantityPerAttendee: parseInt(e.target.value) || 1 })}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="mealDescription">Description</Label>
                    <Input
                      id="mealDescription"
                      value={newMeal.description}
                      onChange={(e) => setNewMeal({ ...newMeal, description: e.target.value })}
                      placeholder="Brief description of the meal"
                    />
                  </div>

                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="space-y-2">
                      <Label htmlFor="availableFrom">Available From</Label>
                      <Input
                        id="availableFrom"
                        type="time"
                        value={newMeal.availableFrom || ""}
                        onChange={(e) => setNewMeal({ ...newMeal, availableFrom: e.target.value })}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="availableTo">Available To</Label>
                      <Input
                        id="availableTo"
                        type="time"
                        value={newMeal.availableTo || ""}
                        onChange={(e) => setNewMeal({ ...newMeal, availableTo: e.target.value })}
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="totalQuantity">Total Inventory (Optional)</Label>
                    <Input
                      id="totalQuantity"
                      type="number"
                      min="0"
                      value={newMeal.totalQuantity || ""}
                      onChange={(e) => setNewMeal({ ...newMeal, totalQuantity: parseInt(e.target.value) || undefined })}
                      placeholder="Leave empty for unlimited"
                    />
                  </div>

                  {/* Dietary Options */}
                  <div className="space-y-3">
                    <Label>Dietary Options</Label>
                    <div className="flex flex-wrap gap-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="vegetarian"
                          checked={newMeal.isVegetarian}
                          onCheckedChange={(checked) => setNewMeal({ ...newMeal, isVegetarian: checked })}
                        />
                        <Label htmlFor="vegetarian">Vegetarian</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="vegan"
                          checked={newMeal.isVegan}
                          onCheckedChange={(checked) => setNewMeal({ ...newMeal, isVegan: checked })}
                        />
                        <Label htmlFor="vegan">Vegan</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="glutenFree"
                          checked={newMeal.isGlutenFree}
                          onCheckedChange={(checked) => setNewMeal({ ...newMeal, isGlutenFree: checked })}
                        />
                        <Label htmlFor="glutenFree">Gluten-Free</Label>
                      </div>
                    </div>
                  </div>

                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => setIsAddingMeal(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleAddMeal}>
                      Add Meal Type
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Existing Meal Types */}
          {mealTypes.length === 0 && !isAddingMeal ? (
            <div className="text-center py-8">
              <Utensils className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">No meal types configured</h3>
              <p className="text-muted-foreground">
                Add meal types to start configuring your event menu
              </p>
            </div>
          ) : (
            <div className="grid gap-4">
              {mealTypes.map((mealType) => (
                <Card key={mealType.id}>
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-semibold">{mealType.name}</h4>
                          <Badge variant={mealType.isAvailable ? "success" : "secondary"}>
                            {mealType.isAvailable ? "Available" : "Unavailable"}
                          </Badge>
                          {getDietaryBadges(mealType).map((badge, index) => (
                            <Badge key={index} variant={badge.variant}>
                              {badge.label}
                            </Badge>
                          ))}
                        </div>
                        {mealType.description && (
                          <p className="text-sm text-muted-foreground">{mealType.description}</p>
                        )}
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <span>Max per person: {mealType.maxQuantityPerAttendee}</span>
                          {mealType.totalQuantity && (
                            <span>
                              Inventory: {mealType.totalQuantity - mealType.consumedQuantity} / {mealType.totalQuantity}
                            </span>
                          )}
                          {(mealType.availableFrom || mealType.availableTo) && (
                            <span>
                              Time: {mealType.availableFrom || "00:00"} - {mealType.availableTo || "23:59"}
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-destructive hover:text-destructive"
                          onClick={() => onDeleteMealType(mealType.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
