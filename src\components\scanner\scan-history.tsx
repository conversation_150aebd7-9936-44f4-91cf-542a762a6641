"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Check<PERSON>ircle, 
  AlertCircle, 
  Clock,
  RefreshCw,
  Download
} from "lucide-react"
import { formatDateTime } from "@/lib/utils"

interface ScanRecord {
  id: string
  attendeeName: string
  qrCode: string
  scannedAt: string
  success: boolean
  mealType?: string
  error?: string
}

interface ScanHistoryProps {
  scans: ScanRecord[]
  onRefresh?: () => void
  onExport?: () => void
}

export function ScanHistory({ scans, onRefresh, onExport }: ScanHistoryProps) {
  const successfulScans = scans.filter(scan => scan.success)
  const failedScans = scans.filter(scan => !scan.success)

  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return "Just now"
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return formatDateTime(dateString)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Recent Scans</CardTitle>
            <CardDescription>
              Latest scanning activity ({scans.length} total)
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            {onRefresh && (
              <Button variant="outline" size="sm" onClick={onRefresh}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}
            {onExport && (
              <Button variant="outline" size="sm" onClick={onExport}>
                <Download className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {scans.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-semibold">No scans yet</h3>
            <p className="text-muted-foreground">
              Scan history will appear here as you process QR codes
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Summary Stats */}
            <div className="flex items-center space-x-4 p-3 bg-muted/50 rounded-lg">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-500" />
                <span className="text-sm font-medium">{successfulScans.length} successful</span>
              </div>
              {failedScans.length > 0 && (
                <div className="flex items-center space-x-2">
                  <AlertCircle className="h-4 w-4 text-red-500" />
                  <span className="text-sm font-medium">{failedScans.length} failed</span>
                </div>
              )}
            </div>

            {/* Scan List */}
            <div className="space-y-3 max-h-96 overflow-auto">
              {scans.map((scan) => (
                <div
                  key={scan.id}
                  className={`flex items-center justify-between p-3 rounded-lg border ${
                    scan.success 
                      ? 'bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800' 
                      : 'bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      {scan.success ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <AlertCircle className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                    
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium text-foreground">
                          {scan.attendeeName}
                        </p>
                        {scan.mealType && (
                          <Badge variant="secondary" className="text-xs">
                            {scan.mealType}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center space-x-2 mt-1">
                        <p className="text-xs text-muted-foreground">
                          {scan.qrCode}
                        </p>
                        <span className="text-xs text-muted-foreground">•</span>
                        <p className="text-xs text-muted-foreground">
                          {formatRelativeTime(scan.scannedAt)}
                        </p>
                      </div>
                      
                      {scan.error && (
                        <p className="text-xs text-red-600 mt-1">
                          {scan.error}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex-shrink-0">
                    <Badge 
                      variant={scan.success ? "success" : "destructive"}
                      className="text-xs"
                    >
                      {scan.success ? "Success" : "Failed"}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>

            {/* Load More */}
            {scans.length >= 10 && (
              <div className="text-center pt-4 border-t">
                <Button variant="outline" size="sm">
                  Load More History
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
