"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { AppLayout } from "@/components/layout/app-layout"
import { QRScanner } from "@/components/scanner/qr-scanner"
import { ReliableQRScanner } from "@/components/scanner/reliable-qr-scanner"
import { DirectQRScanner } from "@/components/scanner/direct-qr-scanner"
import { IOSQRScanner } from "@/components/scanner/ios-qr-scanner"
import { SimpleIOSScanner } from "@/components/scanner/simple-ios-scanner"
import { UniversalQRScanner } from "@/components/scanner/universal-qr-scanner"
import { ScannerDashboard } from "@/components/scanner/scanner-dashboard"
import { ScanHistory } from "@/components/scanner/scan-history"
import { MobileScannerHelper } from "@/components/scanner/mobile-scanner-helper"
import { CameraDiagnostics } from "@/components/scanner/camera-diagnostics"
import { CompatibilityChecker } from "@/components/scanner/compatibility-checker"
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  QrCode, 
  Camera, 
  Users, 
  CheckCircle, 
  Clock,
  TrendingUp,
  Wifi,
  WifiOff
} from "lucide-react"

// Mock data - will be replaced with real data later
const mockScanData = {
  todayScans: 127,
  totalAttendees: 850,
  pendingScans: 723,
  scanRate: 14.9,
  recentScans: [
    {
      id: "1",
      attendeeName: "John Doe",
      qrCode: "JD-2024-001",
      scannedAt: new Date().toISOString(),
      success: true,
      mealType: "Lunch"
    },
    {
      id: "2", 
      attendeeName: "Jane Smith",
      qrCode: "JS-2024-002",
      scannedAt: new Date(Date.now() - 300000).toISOString(),
      success: true,
      mealType: "Lunch"
    },
    {
      id: "3",
      attendeeName: "Invalid QR",
      qrCode: "INVALID-CODE",
      scannedAt: new Date(Date.now() - 600000).toISOString(),
      success: false,
      error: "QR code not found"
    }
  ]
}

export default function ScannerPage() {
  const { data: session, status } = useSession()
  const [isScanning, setIsScanning] = useState(true) // Start scanning immediately
  const [isOnline, setIsOnline] = useState(true)
  const [mounted, setMounted] = useState(false)
  const [scanData] = useState(mockScanData)
  const [lastScanResult, setLastScanResult] = useState<{
    success: boolean
    message: string
    attendee?: any
  } | null>(null)
  const [isIOS, setIsIOS] = useState(false)

  // All hooks at the top level - no conditional hooks
  useEffect(() => {
    setMounted(true)

    // Detect iOS devices
    const userAgent = navigator.userAgent.toLowerCase()
    const isIOSDevice = /iphone|ipad|ipod/.test(userAgent)
    setIsIOS(isIOSDevice)

    if (isIOSDevice) {
      console.log('📱 iOS device detected, using iOS-optimized scanner')
    }
  }, [])

  // Monitor online status
  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Conditional returns after all hooks
  if (status === "loading" || !mounted) {
    return (
      <AppLayout>
        <div className="flex h-screen items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </AppLayout>
    )
  }

  if (!session) {
    redirect("/auth/signin")
    return null
  }

  if (session.user.role !== "SCANNER" && session.user.role !== "ADMIN") {
    redirect("/dashboard")
    return null
  }

  const handleScanResult = async (qrData: string) => {
    try {
      const response = await fetch('/api/scan', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          qrCode: qrData,
          eventId: 'sample-event-1', // Use the seeded event ID
          mealTypeId: null // Allow any meal type for now
        })
      })

      const result = await response.json()

      if (result.success) {
        setLastScanResult({
          success: true,
          message: "Scan successful!",
          attendee: result.attendee
        })
      } else {
        setLastScanResult({
          success: false,
          message: result.error || "Scan failed"
        })
      }
    } catch (error) {
      setLastScanResult({
        success: false,
        message: "Network error - scan saved offline"
      })
    }
  }

  const handleScanError = (error: string) => {
    setLastScanResult({
      success: false,
      message: error
    })
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header with Online Status */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Scanner</h1>
            <p className="text-muted-foreground">
              Scan QR codes to check in attendees
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <Badge variant={isOnline ? "success" : "warning"} className="flex items-center space-x-1">
              {isOnline ? <Wifi className="h-3 w-3" /> : <WifiOff className="h-3 w-3" />}
              <span>{isOnline ? "Online" : "Offline"}</span>
            </Badge>
            <Button
              onClick={() => setIsScanning(!isScanning)}
              variant={isScanning ? "destructive" : "default"}
              size="lg"
            >
              {isScanning ? (
                <>
                  <Clock className="mr-2 h-4 w-4" />
                  Stop Scanner
                </>
              ) : (
                <>
                  <Camera className="mr-2 h-4 w-4" />
                  Start Scanner
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Mobile Device Helper */}
        <MobileScannerHelper />

        {/* Compatibility Checker */}
        <CompatibilityChecker />

        {/* Camera Diagnostics */}
        <CameraDiagnostics />

        {/* Scanner Interface */}
        {isScanning ? (
          <div className="grid gap-6 lg:grid-cols-3">
            <div className="lg:col-span-2">
              <UniversalQRScanner
                onScanResult={handleScanResult}
                onScanError={handleScanError}
              />
            </div>
            <div className="space-y-4">
              {/* Last Scan Result */}
              {lastScanResult && (
                <Card className={lastScanResult.success ? "scanner-success" : "scanner-error"}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center space-x-2">
                      {lastScanResult.success ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <Clock className="h-5 w-5 text-red-600" />
                      )}
                      <CardTitle className="text-lg">
                        {lastScanResult.success ? "Success" : "Error"}
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm">{lastScanResult.message}</p>
                    {lastScanResult.attendee && (
                      <div className="mt-3 space-y-1">
                        <p className="font-medium">{lastScanResult.attendee.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {lastScanResult.attendee.email}
                        </p>
                        <Badge variant="secondary">
                          {lastScanResult.attendee.mealType}
                        </Badge>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Quick Stats */}
              <ScannerDashboard
                todayScans={scanData.todayScans}
                totalAttendees={scanData.totalAttendees}
                pendingScans={scanData.pendingScans}
                scanRate={scanData.scanRate}
              />
            </div>
          </div>
        ) : (
          <div className="grid gap-6 md:grid-cols-2">
            {/* Dashboard */}
            <ScannerDashboard
              todayScans={scanData.todayScans}
              totalAttendees={scanData.totalAttendees}
              pendingScans={scanData.pendingScans}
              scanRate={scanData.scanRate}
            />

            {/* Quick Start */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Start</CardTitle>
                <CardDescription>
                  Ready to start scanning QR codes
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center py-8">
                  <QrCode className="mx-auto h-16 w-16 text-muted-foreground" />
                  <h3 className="mt-4 text-lg font-semibold">Ready to Scan</h3>
                  <p className="text-muted-foreground">
                    Click the "Start Scanning" button to begin
                  </p>
                </div>
                
                <div className="space-y-2 text-sm text-muted-foreground">
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span>Camera access enabled</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {isOnline ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : (
                      <Clock className="h-4 w-4 text-yellow-500" />
                    )}
                    <span>{isOnline ? "Online mode" : "Offline mode - scans will sync later"}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Recent Scans */}
        <ScanHistory scans={scanData.recentScans} />
      </div>
    </AppLayout>
  )
}
