import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || (session.user.role !== "SCANNER" && session.user.role !== "ADMIN")) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { qrCode } = body

    if (!qrCode) {
      return NextResponse.json({ error: "QR code is required" }, { status: 400 })
    }

    // Find attendee by QR code
    const attendee = await prisma.attendee.findUnique({
      where: { qrCode },
      include: {
        event: {
          select: {
            id: true,
            name: true,
            startDate: true,
            endDate: true,
            serviceStartTime: true,
            serviceEndTime: true
          }
        },
        mealConsumptions: {
          include: {
            mealType: {
              select: {
                id: true,
                name: true,
                description: true,
                isVegetarian: true,
                isVegan: true,
                isGlutenFree: true
              }
            }
          }
        },
        scanLogs: {
          orderBy: {
            scannedAt: 'desc'
          },
          take: 5
        }
      }
    })

    if (!attendee) {
      return NextResponse.json({ 
        success: false,
        error: "Attendee not found",
        message: "Invalid QR code"
      }, { status: 404 })
    }

    // Check if event is active
    const now = new Date()
    const eventStart = new Date(attendee.event.startDate)
    const eventEnd = new Date(attendee.event.endDate)

    if (now < eventStart) {
      return NextResponse.json({
        success: false,
        error: "Event not started",
        message: `Event starts on ${eventStart.toLocaleDateString()}`,
        attendee: {
          name: attendee.name,
          email: attendee.email,
          event: attendee.event.name
        }
      }, { status: 400 })
    }

    if (now > eventEnd) {
      return NextResponse.json({
        success: false,
        error: "Event ended",
        message: `Event ended on ${eventEnd.toLocaleDateString()}`,
        attendee: {
          name: attendee.name,
          email: attendee.email,
          event: attendee.event.name
        }
      }, { status: 400 })
    }

    // Check if within service hours
    const serviceStart = new Date(attendee.event.serviceStartTime)
    const serviceEnd = new Date(attendee.event.serviceEndTime)
    const currentTime = new Date()
    
    // Set the date to today for time comparison
    serviceStart.setFullYear(currentTime.getFullYear(), currentTime.getMonth(), currentTime.getDate())
    serviceEnd.setFullYear(currentTime.getFullYear(), currentTime.getMonth(), currentTime.getDate())

    const isWithinServiceHours = currentTime >= serviceStart && currentTime <= serviceEnd

    // Create scan log
    await prisma.scanLog.create({
      data: {
        attendeeId: attendee.id,
        scannedBy: session.user.id,
        scannedAt: new Date(),
        isSuccessful: true,
        notes: isWithinServiceHours ? "Scanned during service hours" : "Scanned outside service hours"
      }
    })

    return NextResponse.json({
      success: true,
      message: isWithinServiceHours ? "Check-in successful" : "Check-in successful (outside service hours)",
      attendee: {
        id: attendee.id,
        name: attendee.name,
        email: attendee.email,
        dietaryRestrictions: attendee.dietaryRestrictions,
        specialNotes: attendee.specialNotes,
        event: attendee.event,
        mealConsumptions: attendee.mealConsumptions,
        lastScan: attendee.scanLogs[0]?.scannedAt || null,
        totalScans: attendee.scanLogs.length + 1
      },
      serviceInfo: {
        isWithinServiceHours,
        serviceStart: serviceStart.toLocaleTimeString(),
        serviceEnd: serviceEnd.toLocaleTimeString()
      }
    })

  } catch (error) {
    console.error("Error processing scan:", error)
    return NextResponse.json({ 
      success: false,
      error: "Internal server error",
      message: "Failed to process scan"
    }, { status: 500 })
  }
}
