"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { 
  Upload, 
  Download, 
  FileText, 
  CheckCircle, 
  AlertCircle,
  X
} from "lucide-react"
import { 
  parseCSV, 
  validateCSVFile, 
  readFileAsText, 
  downloadCSVTemplate,
  attendeesToCSV,
  downloadCSV,
  type AttendeeCSVRow,
  type AttendeeExportRow
} from "@/lib/csv-utils"

interface ImportExportProps {
  eventId: string
  eventName: string
  attendees?: AttendeeExportRow[]
  onImport: (attendees: AttendeeCSVRow[]) => Promise<void>
  onClose: () => void
}

export function ImportExport({ 
  eventId, 
  eventName, 
  attendees = [], 
  onImport, 
  onClose 
}: ImportExportProps) {
  const [activeTab, setActiveTab] = useState<'import' | 'export'>('import')
  const [isLoading, setIsLoading] = useState(false)
  const [importFile, setImportFile] = useState<File | null>(null)
  const [importPreview, setImportPreview] = useState<AttendeeCSVRow[]>([])
  const [importError, setImportError] = useState<string>("")
  const [importSuccess, setImportSuccess] = useState(false)

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    setImportError("")
    setImportPreview([])
    setImportSuccess(false)

    // Validate file
    const validation = validateCSVFile(file)
    if (!validation.valid) {
      setImportError(validation.error || "Invalid file")
      return
    }

    setImportFile(file)
    setIsLoading(true)

    try {
      const content = await readFileAsText(file)
      const parsedData = parseCSV(content)
      setImportPreview(parsedData)
    } catch (error) {
      setImportError(error instanceof Error ? error.message : "Failed to parse CSV")
    } finally {
      setIsLoading(false)
    }
  }

  const handleImport = async () => {
    if (importPreview.length === 0) return

    setIsLoading(true)
    try {
      await onImport(importPreview)
      setImportSuccess(true)
      setImportPreview([])
      setImportFile(null)
    } catch (error) {
      setImportError(error instanceof Error ? error.message : "Failed to import attendees")
    } finally {
      setIsLoading(false)
    }
  }

  const handleExport = () => {
    if (attendees.length === 0) {
      setImportError("No attendees to export")
      return
    }

    const csvContent = attendeesToCSV(attendees)
    const filename = `${eventName.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_attendees_${new Date().toISOString().split('T')[0]}.csv`
    downloadCSV(csvContent, filename)
  }

  const clearImport = () => {
    setImportFile(null)
    setImportPreview([])
    setImportError("")
    setImportSuccess(false)
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Import/Export Attendees</CardTitle>
              <CardDescription>
                Manage attendees for {eventName}
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Tabs */}
          <div className="flex space-x-1 mb-6 bg-muted p-1 rounded-lg">
            <button
              onClick={() => setActiveTab('import')}
              className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'import'
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              Import
            </button>
            <button
              onClick={() => setActiveTab('export')}
              className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'export'
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              Export
            </button>
          </div>

          {/* Import Tab */}
          {activeTab === 'import' && (
            <div className="space-y-6">
              {importSuccess ? (
                <div className="text-center py-8">
                  <CheckCircle className="mx-auto h-12 w-12 text-green-500" />
                  <h3 className="mt-4 text-lg font-semibold">Import Successful!</h3>
                  <p className="text-muted-foreground">
                    {importPreview.length} attendees have been imported successfully.
                  </p>
                  <Button className="mt-4" onClick={clearImport}>
                    Import More
                  </Button>
                </div>
              ) : (
                <>
                  {/* File Upload */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="csvFile">Select CSV File</Label>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={downloadCSVTemplate}
                      >
                        <Download className="mr-2 h-4 w-4" />
                        Download Template
                      </Button>
                    </div>
                    
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
                      <div className="text-center">
                        <Upload className="mx-auto h-8 w-8 text-muted-foreground" />
                        <div className="mt-2">
                          <Input
                            id="csvFile"
                            type="file"
                            accept=".csv"
                            onChange={handleFileSelect}
                            className="hidden"
                          />
                          <Button
                            variant="outline"
                            onClick={() => document.getElementById('csvFile')?.click()}
                            disabled={isLoading}
                          >
                            {isLoading ? (
                              <>
                                <LoadingSpinner size="sm" className="mr-2" />
                                Processing...
                              </>
                            ) : (
                              <>
                                <Upload className="mr-2 h-4 w-4" />
                                Choose CSV File
                              </>
                            )}
                          </Button>
                        </div>
                        <p className="mt-2 text-sm text-muted-foreground">
                          Upload a CSV file with attendee information
                        </p>
                      </div>
                    </div>

                    {importFile && (
                      <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
                        <div className="flex items-center space-x-2">
                          <FileText className="h-4 w-4" />
                          <span className="text-sm font-medium">{importFile.name}</span>
                          <Badge variant="secondary">
                            {(importFile.size / 1024).toFixed(1)} KB
                          </Badge>
                        </div>
                        <Button variant="ghost" size="sm" onClick={clearImport}>
                          <X className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>

                  {/* Error Display */}
                  {importError && (
                    <div className="flex items-start space-x-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
                      <AlertCircle className="h-4 w-4 text-destructive mt-0.5" />
                      <div className="text-sm text-destructive">
                        <div className="font-medium">Import Error</div>
                        <div className="mt-1 whitespace-pre-line">{importError}</div>
                      </div>
                    </div>
                  )}

                  {/* Preview */}
                  {importPreview.length > 0 && (
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium">Preview ({importPreview.length} attendees)</h4>
                        <Button onClick={handleImport} disabled={isLoading}>
                          {isLoading ? (
                            <>
                              <LoadingSpinner size="sm" className="mr-2" />
                              Importing...
                            </>
                          ) : (
                            <>
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Import Attendees
                            </>
                          )}
                        </Button>
                      </div>
                      
                      <div className="border rounded-lg max-h-64 overflow-auto">
                        <table className="w-full text-sm">
                          <thead className="bg-muted">
                            <tr>
                              <th className="text-left p-2 font-medium">Name</th>
                              <th className="text-left p-2 font-medium">Email</th>
                              <th className="text-left p-2 font-medium">Dietary</th>
                              <th className="text-left p-2 font-medium">Notes</th>
                            </tr>
                          </thead>
                          <tbody>
                            {importPreview.slice(0, 10).map((attendee, index) => (
                              <tr key={index} className="border-t">
                                <td className="p-2">{attendee.name}</td>
                                <td className="p-2">{attendee.email || '-'}</td>
                                <td className="p-2">{attendee.dietaryRestrictions || '-'}</td>
                                <td className="p-2">{attendee.specialNotes || '-'}</td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                        {importPreview.length > 10 && (
                          <div className="p-2 text-center text-muted-foreground border-t">
                            ... and {importPreview.length - 10} more attendees
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          )}

          {/* Export Tab */}
          {activeTab === 'export' && (
            <div className="space-y-6">
              <div className="text-center py-8">
                <Download className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-semibold">Export Attendees</h3>
                <p className="text-muted-foreground">
                  Download attendee data as CSV file
                </p>
                
                <div className="mt-6 space-y-4">
                  <div className="flex items-center justify-center space-x-4 text-sm text-muted-foreground">
                    <span>{attendees.length} attendees</span>
                    <span>•</span>
                    <span>Includes QR codes and scan status</span>
                  </div>
                  
                  <Button onClick={handleExport} disabled={attendees.length === 0}>
                    <Download className="mr-2 h-4 w-4" />
                    Export to CSV
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
