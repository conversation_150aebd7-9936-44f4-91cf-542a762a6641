"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { useToast } from "@/hooks/use-toast"
import { Calendar, Plus, Trash2 } from "lucide-react"

interface EventFormDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  event?: any
  onEventSaved: (event: any) => void
}

export function EventFormDialog({ open, onOpenChange, event, onEventSaved }: EventFormDialogProps) {
  const { toast } = useToast()
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    startDate: '',
    endDate: '',
    serviceStartTime: '',
    serviceEndTime: '',
    primaryColor: '#3b82f6',
    secondaryColor: '#1e40af',
    logoUrl: '',
    isActive: true,
    mealTypes: [{ name: '', description: '', maxQuantityPerAttendee: 1, totalQuantity: 0, isVegetarian: false, isVegan: false, isGlutenFree: false }]
  })
  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (event) {
      setFormData({
        name: event.name || '',
        description: event.description || '',
        startDate: event.startDate ? new Date(event.startDate).toISOString().split('T')[0] : '',
        endDate: event.endDate ? new Date(event.endDate).toISOString().split('T')[0] : '',
        serviceStartTime: event.serviceStartTime ? new Date(event.serviceStartTime).toTimeString().slice(0, 5) : '',
        serviceEndTime: event.serviceEndTime ? new Date(event.serviceEndTime).toTimeString().slice(0, 5) : '',
        primaryColor: event.primaryColor || '#3b82f6',
        secondaryColor: event.secondaryColor || '#1e40af',
        logoUrl: event.logoUrl || '',
        isActive: event.isActive ?? true,
        mealTypes: event.mealTypes?.length > 0 ? event.mealTypes : [{ name: '', description: '', maxQuantityPerAttendee: 1, totalQuantity: 0, isVegetarian: false, isVegan: false, isGlutenFree: false }]
      })
    } else {
      // Reset form for new event
      setFormData({
        name: '',
        description: '',
        startDate: '',
        endDate: '',
        serviceStartTime: '',
        serviceEndTime: '',
        primaryColor: '#3b82f6',
        secondaryColor: '#1e40af',
        logoUrl: '',
        isActive: true,
        mealTypes: [{ name: '', description: '', maxQuantityPerAttendee: 1, totalQuantity: 0, isVegetarian: false, isVegan: false, isGlutenFree: false }]
      })
    }
    setErrors({})
  }, [event, open])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Event name is required'
    }

    if (!formData.startDate) {
      newErrors.startDate = 'Start date is required'
    }

    if (!formData.endDate) {
      newErrors.endDate = 'End date is required'
    }

    if (formData.startDate && formData.endDate && new Date(formData.startDate) > new Date(formData.endDate)) {
      newErrors.endDate = 'End date must be after start date'
    }

    // Validate meal types
    formData.mealTypes.forEach((mealType, index) => {
      if (!mealType.name.trim()) {
        newErrors[`mealType_${index}_name`] = 'Meal type name is required'
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setLoading(true)

    try {
      const isEditing = event && event.id
      const url = isEditing ? `/api/events/${event.id}` : '/api/events'
      const method = isEditing ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          startDate: new Date(formData.startDate).toISOString(),
          endDate: new Date(formData.endDate).toISOString(),
          serviceStartTime: formData.serviceStartTime ? new Date(`1970-01-01T${formData.serviceStartTime}:00`).toISOString() : null,
          serviceEndTime: formData.serviceEndTime ? new Date(`1970-01-01T${formData.serviceEndTime}:00`).toISOString() : null,
          mealTypes: formData.mealTypes.filter(mt => mt.name.trim())
        })
      })

      if (response.ok) {
        const savedEvent = await response.json()
        onEventSaved(savedEvent)
        onOpenChange(false)
      } else {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || errorData.message || `Failed to save event (${response.status})`
        setErrors({ submit: errorMessage })
      }
    } catch (error) {
      const errorMessage = 'Network error occurred'
      setErrors({ submit: errorMessage })
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const addMealType = () => {
    setFormData(prev => ({
      ...prev,
      mealTypes: [...prev.mealTypes, { name: '', description: '', maxQuantityPerAttendee: 1, totalQuantity: 0, isVegetarian: false, isVegan: false, isGlutenFree: false }]
    }))
  }

  const removeMealType = (index: number) => {
    setFormData(prev => ({
      ...prev,
      mealTypes: prev.mealTypes.filter((_, i) => i !== index)
    }))
  }

  const updateMealType = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      mealTypes: prev.mealTypes.map((mt, i) => 
        i === index ? { ...mt, [field]: value } : mt
      )
    }))
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {event && event.id ? 'Edit Event' : 'Create New Event'}
          </DialogTitle>
          <DialogDescription>
            {event && event.id
              ? 'Update event details and settings'
              : 'Create a new event with meal types and configurations'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="name">Event Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Enter event name"
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="logoUrl">Logo URL</Label>
                <Input
                  id="logoUrl"
                  value={formData.logoUrl}
                  onChange={(e) => setFormData(prev => ({ ...prev, logoUrl: e.target.value }))}
                  placeholder="https://example.com/logo.png"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter event description"
                rows={3}
              />
            </div>
          </div>

          {/* Date and Time */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Date and Time</h3>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="startDate">Start Date *</Label>
                <Input
                  id="startDate"
                  type="date"
                  value={formData.startDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, startDate: e.target.value }))}
                  className={errors.startDate ? 'border-red-500' : ''}
                />
                {errors.startDate && <p className="text-sm text-red-500">{errors.startDate}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="endDate">End Date *</Label>
                <Input
                  id="endDate"
                  type="date"
                  value={formData.endDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
                  className={errors.endDate ? 'border-red-500' : ''}
                />
                {errors.endDate && <p className="text-sm text-red-500">{errors.endDate}</p>}
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="serviceStartTime">Service Start Time</Label>
                <Input
                  id="serviceStartTime"
                  type="time"
                  value={formData.serviceStartTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, serviceStartTime: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="serviceEndTime">Service End Time</Label>
                <Input
                  id="serviceEndTime"
                  type="time"
                  value={formData.serviceEndTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, serviceEndTime: e.target.value }))}
                />
              </div>
            </div>
          </div>

          {/* Theme Colors */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Theme Colors</h3>
            
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="primaryColor">Primary Color</Label>
                <div className="flex space-x-2">
                  <Input
                    id="primaryColor"
                    type="color"
                    value={formData.primaryColor}
                    onChange={(e) => setFormData(prev => ({ ...prev, primaryColor: e.target.value }))}
                    className="w-16 h-10"
                  />
                  <Input
                    value={formData.primaryColor}
                    onChange={(e) => setFormData(prev => ({ ...prev, primaryColor: e.target.value }))}
                    placeholder="#3b82f6"
                    className="flex-1"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="secondaryColor">Secondary Color</Label>
                <div className="flex space-x-2">
                  <Input
                    id="secondaryColor"
                    type="color"
                    value={formData.secondaryColor}
                    onChange={(e) => setFormData(prev => ({ ...prev, secondaryColor: e.target.value }))}
                    className="w-16 h-10"
                  />
                  <Input
                    value={formData.secondaryColor}
                    onChange={(e) => setFormData(prev => ({ ...prev, secondaryColor: e.target.value }))}
                    placeholder="#1e40af"
                    className="flex-1"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Meal Types */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium">Meal Types</h3>
              <Button type="button" onClick={addMealType} variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Meal Type
              </Button>
            </div>

            {formData.mealTypes.map((mealType, index) => (
              <div key={index} className="p-4 border rounded-lg space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">Meal Type {index + 1}</h4>
                  {formData.mealTypes.length > 1 && (
                    <Button
                      type="button"
                      onClick={() => removeMealType(index)}
                      variant="outline"
                      size="sm"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>

                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <Label>Name *</Label>
                    <Input
                      value={mealType.name}
                      onChange={(e) => updateMealType(index, 'name', e.target.value)}
                      placeholder="e.g., Lunch, Breakfast"
                      className={errors[`mealType_${index}_name`] ? 'border-red-500' : ''}
                    />
                    {errors[`mealType_${index}_name`] && (
                      <p className="text-sm text-red-500">{errors[`mealType_${index}_name`]}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label>Total Quantity</Label>
                    <Input
                      type="number"
                      value={mealType.totalQuantity}
                      onChange={(e) => updateMealType(index, 'totalQuantity', parseInt(e.target.value) || 0)}
                      placeholder="0 for unlimited"
                      min="0"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Description</Label>
                  <Textarea
                    value={mealType.description}
                    onChange={(e) => updateMealType(index, 'description', e.target.value)}
                    placeholder="Describe this meal type"
                    rows={2}
                  />
                </div>

                <div className="flex flex-wrap gap-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={mealType.isVegetarian}
                      onCheckedChange={(checked) => updateMealType(index, 'isVegetarian', checked)}
                    />
                    <Label>Vegetarian</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={mealType.isVegan}
                      onCheckedChange={(checked) => updateMealType(index, 'isVegan', checked)}
                    />
                    <Label>Vegan</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={mealType.isGlutenFree}
                      onCheckedChange={(checked) => updateMealType(index, 'isGlutenFree', checked)}
                    />
                    <Label>Gluten Free</Label>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Settings</h3>
            
            <div className="flex items-center space-x-2">
              <Switch
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
              />
              <Label>Event is active</Label>
            </div>
          </div>

          {errors.submit && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-800">{errors.submit}</p>
            </div>
          )}

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  {event ? 'Updating...' : 'Creating...'}
                </>
              ) : (
                event ? 'Update Event' : 'Create Event'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
