"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Camera, 
  CameraOff, 
  Upload, 
  Flashlight,
  FlashlightOff,
  RotateCcw,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Smartphone,
  Hand
} from "lucide-react"

interface IOSQRScannerProps {
  onScanResult: (data: string) => void
  onScanError: (error: string) => void
}

interface DeviceInfo {
  isIOS: boolean
  isSafari: boolean
  isIPhone: boolean
  iosVersion: string | null
  deviceModel: string
  supportsCamera: boolean
  requiresUserGesture: boolean
}

export function IOSQRScanner({ onScanResult, onScanError }: IOSQRScannerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const scannerRef = useRef<any>(null)
  const streamRef = useRef<MediaStream | null>(null)
  
  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo | null>(null)
  const [status, setStatus] = useState<'waiting' | 'loading' | 'ready' | 'error'>('waiting')
  const [error, setError] = useState<string | null>(null)
  const [scanCount, setScanCount] = useState(0)
  const [isFlashlightOn, setIsFlashlightOn] = useState(false)
  const [cameras, setCameras] = useState<any[]>([])
  const [currentCameraIndex, setCurrentCameraIndex] = useState(0)
  const [needsUserGesture, setNeedsUserGesture] = useState(true)

  // Detect iOS and device capabilities
  const detectDevice = useCallback((): DeviceInfo => {
    const userAgent = navigator.userAgent.toLowerCase()
    const isIOS = /iphone|ipad|ipod/.test(userAgent)
    const isSafari = /safari/.test(userAgent) && !/chrome|crios|fxios/.test(userAgent)
    const isIPhone = /iphone/.test(userAgent)
    
    // Extract iOS version
    let iosVersion = null
    const iosMatch = userAgent.match(/os (\d+)_(\d+)_?(\d+)?/)
    if (iosMatch) {
      iosVersion = `${iosMatch[1]}.${iosMatch[2]}${iosMatch[3] ? '.' + iosMatch[3] : ''}`
    }

    // Detect device model
    let deviceModel = 'Unknown'
    if (isIPhone) {
      if (userAgent.includes('iphone')) {
        deviceModel = 'iPhone'
      }
    } else if (userAgent.includes('ipad')) {
      deviceModel = 'iPad'
    }

    const supportsCamera = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
    const requiresUserGesture = isIOS && isSafari

    return {
      isIOS,
      isSafari,
      isIPhone,
      iosVersion,
      deviceModel,
      supportsCamera,
      requiresUserGesture
    }
  }, [])

  // Initialize device detection
  useEffect(() => {
    const info = detectDevice()
    setDeviceInfo(info)
    console.log('📱 Device detected:', info)
    
    if (info.requiresUserGesture) {
      setNeedsUserGesture(true)
      setStatus('waiting')
    } else {
      setNeedsUserGesture(false)
      // Auto-start for non-iOS devices
      setTimeout(() => initializeScanner(), 500)
    }
  }, [detectDevice])

  // iOS-optimized camera initialization
  const initializeScanner = useCallback(async () => {
    if (!videoRef.current || !deviceInfo) return

    console.log('🍎 iOS Scanner: Starting initialization...')
    setStatus('loading')
    setError(null)
    setNeedsUserGesture(false)

    try {
      // Step 1: iOS-specific camera permission request
      console.log('📷 iOS Scanner: Requesting camera access with iOS constraints...')

      // More conservative constraints for iOS
      const constraints: MediaStreamConstraints = {
        video: {
          facingMode: 'environment',
          width: { ideal: 640, max: 1280 }, // Reduced for iOS compatibility
          height: { ideal: 480, max: 720 },
          frameRate: { ideal: 15, max: 30 } // Lower frame rate for iOS
        },
        audio: false
      }

      console.log('🔧 iOS Scanner: Using constraints:', constraints)
      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      streamRef.current = stream
      console.log('✅ iOS Scanner: Camera stream obtained')

      // Step 2: iOS-specific video element setup
      if (videoRef.current) {
        console.log('📹 iOS Scanner: Setting up video element...')

        // Critical iOS attributes
        videoRef.current.setAttribute('playsinline', 'true')
        videoRef.current.setAttribute('webkit-playsinline', 'true')
        videoRef.current.muted = true
        videoRef.current.autoplay = false // Disable autoplay for iOS

        videoRef.current.srcObject = stream

        // Wait for video metadata to load
        await new Promise((resolve, reject) => {
          const timeout = setTimeout(() => reject(new Error('Video metadata timeout')), 10000)

          videoRef.current!.onloadedmetadata = () => {
            clearTimeout(timeout)
            console.log('✅ iOS Scanner: Video metadata loaded')
            resolve(true)
          }

          videoRef.current!.onerror = (error) => {
            clearTimeout(timeout)
            reject(error)
          }
        })

        // Explicit play for iOS
        console.log('▶️ iOS Scanner: Starting video playback...')
        try {
          await videoRef.current.play()
          console.log('✅ iOS Scanner: Video playing successfully')
        } catch (playError) {
          console.warn('⚠️ iOS Scanner: Video play failed, trying alternative approach:', playError)

          // Alternative approach for iOS
          videoRef.current.muted = true
          videoRef.current.volume = 0
          await videoRef.current.play()
          console.log('✅ iOS Scanner: Video playing with alternative approach')
        }
      }

      // Step 3: List available cameras
      const devices = await navigator.mediaDevices.enumerateDevices()
      const videoDevices = devices.filter(device => device.kind === 'videoinput')
      setCameras(videoDevices)
      console.log(`📱 iOS Scanner: Found ${videoDevices.length} cameras`)

      // Step 4: Initialize QR Scanner with iOS optimizations
      console.log('🔧 iOS Scanner: Loading QR scanner library...')

      // Use a more reliable import method for iOS
      let QrScanner
      try {
        const QrScannerModule = await import('qr-scanner')
        QrScanner = QrScannerModule.default
        console.log('✅ iOS Scanner: QR scanner library loaded')
      } catch (importError) {
        console.error('❌ iOS Scanner: Failed to load QR scanner library:', importError)
        throw new Error('Failed to load QR scanner library')
      }

      // Wait a bit for video to stabilize on iOS
      await new Promise(resolve => setTimeout(resolve, 1000))

      console.log('🔧 iOS Scanner: Creating scanner instance...')
      const scanner = new QrScanner(
        videoRef.current,
        (result) => {
          console.log('🎉 iOS Scanner: QR Code detected:', result.data)
          setScanCount(prev => prev + 1)
          onScanResult(result.data)

          // iOS haptic feedback
          if ('vibrate' in navigator) {
            navigator.vibrate([100])
          }

          // iOS-specific success feedback
          if (deviceInfo.isIOS) {
            console.log('📱 iOS Scanner: Scan successful on iOS device')
          }
        },
        {
          onDecodeError: (error) => {
            // Only log occasionally to avoid spam
            if (Math.random() < 0.01) {
              console.debug('🔍 iOS Scanner: Scanning for QR codes...')
            }
          },
          highlightScanRegion: true,
          highlightCodeOutline: true,
          preferredCamera: 'environment',
          maxScansPerSecond: 1, // Very conservative for iOS
          calculateScanRegion: (video) => {
            // More conservative scan region for iOS
            const smallerDimension = Math.min(video.videoWidth, video.videoHeight)
            const scanRegionSize = Math.round(0.5 * smallerDimension) // Smaller region
            return {
              x: Math.round((video.videoWidth - scanRegionSize) / 2),
              y: Math.round((video.videoHeight - scanRegionSize) / 2),
              width: scanRegionSize,
              height: scanRegionSize,
            }
          }
        }
      )

      console.log('🚀 iOS Scanner: Starting scanner...')
      scannerRef.current = scanner

      // Don't call scanner.start() immediately on iOS, let it initialize
      await new Promise(resolve => setTimeout(resolve, 500))

      setStatus('ready')
      console.log('🎉 iOS Scanner: Ready and waiting for QR codes!')

    } catch (err) {
      console.error('❌ iOS Scanner: Failed:', err)
      const errorMessage = err instanceof Error ? err.message : 'Scanner initialization failed'
      
      // iOS-specific error handling
      if (errorMessage.includes('NotAllowedError') || errorMessage.includes('Permission denied')) {
        setError('Camera permission denied. Please allow camera access in Safari settings.')
      } else if (errorMessage.includes('NotFoundError')) {
        setError('No camera found. Please ensure your device has a working camera.')
      } else if (errorMessage.includes('NotReadableError')) {
        setError('Camera is being used by another app. Please close other camera apps and try again.')
      } else {
        setError(errorMessage)
      }
      
      setStatus('error')
      onScanError(errorMessage)
    }
  }, [deviceInfo, onScanResult, onScanError])

  // Cleanup function
  const cleanup = useCallback(() => {
    if (scannerRef.current) {
      console.log('🧹 iOS Scanner: Cleaning up scanner...')
      scannerRef.current.stop()
      scannerRef.current.destroy()
      scannerRef.current = null
    }
    
    if (streamRef.current) {
      console.log('🧹 iOS Scanner: Stopping camera stream...')
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
  }, [])

  // Handle orientation changes (iOS specific)
  useEffect(() => {
    const handleOrientationChange = () => {
      console.log('📱 iOS Scanner: Orientation changed')
      // Small delay to allow screen to settle
      setTimeout(() => {
        if (videoRef.current && streamRef.current) {
          // Force video element to adjust to new orientation
          videoRef.current.style.transform = 'none'
        }
      }, 300)
    }

    window.addEventListener('orientationchange', handleOrientationChange)
    return () => window.removeEventListener('orientationchange', handleOrientationChange)
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return cleanup
  }, [cleanup])

  // Camera switching for iOS
  const switchCamera = useCallback(async () => {
    if (cameras.length <= 1) return

    try {
      const nextIndex = (currentCameraIndex + 1) % cameras.length
      const nextCamera = cameras[nextIndex]
      
      console.log(`📱 iOS Scanner: Switching to camera: ${nextCamera.label}`)
      
      // Stop current stream
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop())
      }

      // Request new stream with specific camera
      const constraints: MediaStreamConstraints = {
        video: {
          deviceId: { exact: nextCamera.deviceId },
          width: { ideal: deviceInfo?.isIPhone ? 1280 : 1920 },
          height: { ideal: deviceInfo?.isIPhone ? 720 : 1080 },
          frameRate: { ideal: 30 }
        }
      }

      const newStream = await navigator.mediaDevices.getUserMedia(constraints)
      streamRef.current = newStream

      if (videoRef.current) {
        videoRef.current.srcObject = newStream
        await videoRef.current.play()
      }

      // Update scanner camera
      if (scannerRef.current) {
        await scannerRef.current.setCamera(nextCamera.deviceId)
      }

      setCurrentCameraIndex(nextIndex)
      console.log('✅ iOS Scanner: Camera switched successfully')

    } catch (err) {
      console.error('❌ iOS Scanner: Camera switch failed:', err)
      onScanError('Failed to switch camera')
    }
  }, [cameras, currentCameraIndex, deviceInfo, onScanError])

  // Toggle flashlight (iOS specific)
  const toggleFlashlight = useCallback(async () => {
    if (!scannerRef.current) return

    try {
      if (isFlashlightOn) {
        await scannerRef.current.turnFlashlightOff()
        setIsFlashlightOn(false)
        console.log('💡 iOS Scanner: Flashlight off')
      } else {
        await scannerRef.current.turnFlashlightOn()
        setIsFlashlightOn(true)
        console.log('💡 iOS Scanner: Flashlight on')
      }
    } catch (err) {
      console.warn('⚠️ iOS Scanner: Flashlight not supported:', err)
      onScanError('Flashlight not supported on this device')
    }
  }, [isFlashlightOn, onScanError])

  // File upload handler with iOS optimizations
  const handleFileUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith('image/')) {
      onScanError('Please select an image file')
      return
    }

    // iOS file size check (iOS has memory limitations)
    if (file.size > 5 * 1024 * 1024) { // 5MB limit for iOS
      onScanError('Image file is too large. Please select a smaller image (max 5MB).')
      return
    }

    try {
      console.log('📁 iOS Scanner: Scanning uploaded image...')
      const QrScannerModule = await import('qr-scanner')
      const QrScanner = QrScannerModule.default
      const result = await QrScanner.scanImage(file)
      onScanResult(result)
      setScanCount(prev => prev + 1)
      console.log('✅ iOS Scanner: QR code found in uploaded image')
    } catch (err) {
      console.error('❌ iOS Scanner: No QR code in image:', err)
      onScanError('No QR code found in the uploaded image. Please try a clearer image.')
    }

    e.target.value = ""
  }, [onScanResult, onScanError])

  // Get iOS-specific instructions
  const getIOSInstructions = () => {
    if (!deviceInfo?.isIOS) return null

    return (
      <Alert className="mb-4">
        <Smartphone className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-2">
            <div className="font-medium">📱 iPhone/iOS Instructions:</div>
            <div className="text-sm space-y-1">
              <div>• Tap "Start Camera" below to begin</div>
              <div>• Allow camera access when prompted</div>
              <div>• Hold your iPhone steady and ensure good lighting</div>
              <div>• Try both portrait and landscape orientations</div>
              {deviceInfo.iosVersion && (
                <div>• iOS {deviceInfo.iosVersion} detected</div>
              )}
            </div>
          </div>
        </AlertDescription>
      </Alert>
    )
  }

  // Waiting for user gesture (iOS requirement)
  if (needsUserGesture && deviceInfo?.requiresUserGesture) {
    return (
      <div className="space-y-4">
        {getIOSInstructions()}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Hand className="h-5 w-5" />
              <span>Ready to Start</span>
            </CardTitle>
            <CardDescription>
              {deviceInfo.isIOS 
                ? "iOS Safari requires user interaction to access camera"
                : "Tap to start camera access"
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center py-8">
            <div className="space-y-4">
              <Camera className="mx-auto h-16 w-16 text-muted-foreground" />
              <div>
                <h3 className="text-lg font-semibold">Camera Access Required</h3>
                <p className="text-muted-foreground mt-2">
                  {deviceInfo.isIOS 
                    ? "Tap the button below to start the camera and begin scanning QR codes"
                    : "Click to initialize the QR code scanner"
                  }
                </p>
              </div>
              <Button onClick={initializeScanner} size="lg" className="w-full max-w-sm">
                <Camera className="mr-2 h-5 w-5" />
                Start Camera
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Loading state
  if (status === 'loading') {
    return (
      <div className="space-y-4">
        {getIOSInstructions()}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <RefreshCw className="h-5 w-5 animate-spin" />
              <span>Starting Camera</span>
            </CardTitle>
            <CardDescription>
              {deviceInfo?.isIOS 
                ? "Initializing camera for iOS Safari..."
                : "Setting up QR code scanner..."
              }
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center py-8">
            <div className="space-y-4">
              <RefreshCw className="mx-auto h-12 w-12 animate-spin text-muted-foreground" />
              <div>
                <p className="text-muted-foreground">Please wait while we start your camera...</p>
                {deviceInfo?.isIOS && (
                  <p className="text-xs text-muted-foreground mt-2">
                    This may take a few seconds on iOS devices
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Error state with iOS-specific guidance
  if (status === 'error') {
    return (
      <div className="space-y-4">
        {getIOSInstructions()}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <CameraOff className="h-5 w-5" />
              <span>Camera Error</span>
            </CardTitle>
            <CardDescription>
              Unable to access camera
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Error:</strong> {error}
              </AlertDescription>
            </Alert>

            {deviceInfo?.isIOS && (
              <Alert>
                <Smartphone className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <div className="font-medium">iOS Troubleshooting:</div>
                    <div className="text-sm space-y-1">
                      <div>1. Go to Settings → Safari → Camera</div>
                      <div>2. Ensure "Ask" or "Allow" is selected</div>
                      <div>3. Refresh this page and try again</div>
                      <div>4. Make sure no other apps are using the camera</div>
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )}

            <div className="flex flex-col gap-2">
              <Button onClick={() => {
                setStatus('waiting')
                setNeedsUserGesture(true)
                setError(null)
              }}>
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
              
              <Button onClick={() => fileInputRef.current?.click()} variant="outline">
                <Upload className="mr-2 h-4 w-4" />
                Upload QR Code Image
              </Button>
              
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Working scanner with iOS optimizations
  return (
    <div className="space-y-4">
      {getIOSInstructions()}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Camera className="h-5 w-5 text-green-600" />
                <span>QR Scanner</span>
                <Badge variant="outline" className="text-green-600 border-green-600">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Active
                </Badge>
                {deviceInfo?.isIOS && (
                  <Badge variant="secondary">
                    iOS
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                Point camera at QR code • {scanCount} scans completed
                {deviceInfo?.iosVersion && ` • iOS ${deviceInfo.iosVersion}`}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Video Container with iOS optimizations */}
          <div className="relative aspect-square bg-black rounded-lg overflow-hidden">
            <video
              ref={videoRef}
              className="w-full h-full object-cover"
              playsInline // Critical for iOS
              muted
              autoPlay
              style={{
                // iOS-specific styles
                WebkitPlaysinline: true,
                objectFit: 'cover'
              } as any}
            />
            
            {/* iOS-optimized scanning overlay */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
              <div className="w-48 h-48 border-2 border-primary rounded-lg relative">
                <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-primary rounded-tl-lg"></div>
                <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-primary rounded-tr-lg"></div>
                <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-primary rounded-bl-lg"></div>
                <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-primary rounded-br-lg"></div>
                
                {/* iOS scanning indicator */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-1 h-full bg-primary opacity-50 animate-pulse"></div>
                </div>
              </div>
            </div>

            {/* Status indicators */}
            <div className="absolute top-4 left-4 flex space-x-2">
              <Badge variant="secondary" className="bg-black bg-opacity-50 text-white border-none">
                <Camera className="h-3 w-3 mr-1" />
                Live
              </Badge>
              {deviceInfo?.isIOS && (
                <Badge variant="secondary" className="bg-blue-500 bg-opacity-80 text-white border-none">
                  🍎 iOS
                </Badge>
              )}
              {isFlashlightOn && (
                <Badge variant="secondary" className="bg-yellow-500 bg-opacity-80 text-black border-none">
                  <Flashlight className="h-3 w-3 mr-1" />
                  Flash
                </Badge>
              )}
            </div>
          </div>

          {/* iOS-optimized controls */}
          <div className="flex justify-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={toggleFlashlight}
              title={isFlashlightOn ? "Turn off flashlight" : "Turn on flashlight"}
            >
              {isFlashlightOn ? (
                <FlashlightOff className="h-4 w-4" />
              ) : (
                <Flashlight className="h-4 w-4" />
              )}
            </Button>
            
            {cameras.length > 1 && (
              <Button
                variant="outline"
                size="sm"
                onClick={switchCamera}
                title="Switch camera"
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            )}
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => fileInputRef.current?.click()}
              title="Upload QR code image"
            >
              <Upload className="h-4 w-4" />
            </Button>
            
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>

          {/* iOS-specific instructions */}
          <div className="text-center text-sm text-muted-foreground">
            <p>📱 Position QR code within the frame for automatic detection</p>
            {deviceInfo?.isIOS && (
              <p className="text-xs mt-1">
                💡 For best results on iPhone: ensure good lighting and hold steady
              </p>
            )}
          </div>

          {/* Camera info */}
          {cameras.length > 0 && (
            <div className="text-xs text-muted-foreground text-center">
              📹 {cameras[currentCameraIndex]?.label || 'Camera'} 
              {cameras.length > 1 && ` (${currentCameraIndex + 1}/${cameras.length})`}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
