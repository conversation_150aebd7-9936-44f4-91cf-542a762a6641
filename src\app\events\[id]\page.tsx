"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { AppLayout } from "@/components/layout/app-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { EventFormDialog } from "@/components/events/event-form-dialog"
import { 
  ArrowLeft,
  Edit,
  Users,
  Calendar,
  Clock,
  MapPin,
  Utensils,
  AlertCircle,
  CheckCircle,
  Settings
} from "lucide-react"

interface Event {
  id: string
  name: string
  description: string
  startDate: string
  endDate: string
  serviceStartTime?: string
  serviceEndTime?: string
  primaryColor?: string
  secondaryColor?: string
  logoUrl?: string
  isActive: boolean
  mealTypes: MealType[]
  _count: {
    attendees: number
    scanLogs: number
  }
}

interface MealType {
  id: string
  name: string
  description?: string
  maxQuantityPerAttendee: number
  totalQuantity?: number
  consumedQuantity: number
  isVegetarian: boolean
  isVegan: boolean
  isGlutenFree: boolean
}

export default function EventDetailsPage() {
  const { data: session, status } = useSession()
  const params = useParams()
  const router = useRouter()
  const [event, setEvent] = useState<Event | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showEditDialog, setShowEditDialog] = useState(false)

  useEffect(() => {
    if (session && params.id) {
      fetchEvent()
    }
  }, [session, params.id])

  const fetchEvent = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch(`/api/events/${params.id}`)
      
      if (response.ok) {
        const data = await response.json()
        setEvent(data)
      } else {
        const errorData = await response.json().catch(() => ({}))
        setError(errorData.error || `Failed to fetch event (${response.status})`)
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Network error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleEventUpdated = (updatedEvent: Event) => {
    setEvent(updatedEvent)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric"
    })
  }

  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit"
    })
  }

  if (status === "loading" || loading) {
    return (
      <AppLayout>
        <div className="flex h-screen items-center justify-center">
          <LoadingSpinner size="lg" />
        </div>
      </AppLayout>
    )
  }

  if (!session) {
    router.push("/auth/signin")
    return null
  }

  if (error) {
    return (
      <AppLayout>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
          
          <div className="bg-destructive/15 border border-destructive/20 rounded-lg p-6">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              <h2 className="text-lg font-semibold text-destructive">Error loading event</h2>
            </div>
            <p className="text-destructive/80 mt-2">{error}</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={fetchEvent}
            >
              Try Again
            </Button>
          </div>
        </div>
      </AppLayout>
    )
  }

  if (!event) {
    return (
      <AppLayout>
        <div className="space-y-6">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </div>
          
          <div className="text-center py-12">
            <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-semibold">Event not found</h3>
            <p className="text-muted-foreground">
              The event you're looking for doesn't exist or has been deleted.
            </p>
          </div>
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">{event.name}</h1>
              <p className="text-muted-foreground">{event.description}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge variant={event.isActive ? "success" : "secondary"}>
              {event.isActive ? "Active" : "Inactive"}
            </Badge>
            {session.user.role === "ADMIN" && (
              <Button onClick={() => setShowEditDialog(true)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Event
              </Button>
            )}
          </div>
        </div>

        {/* Event Details Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Event Details
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Start Date</p>
                <p className="text-lg">{formatDate(event.startDate)}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">End Date</p>
                <p className="text-lg">{formatDate(event.endDate)}</p>
              </div>
              {event.serviceStartTime && event.serviceEndTime && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Service Hours</p>
                  <p className="text-lg">
                    {formatTime(event.serviceStartTime)} - {formatTime(event.serviceEndTime)}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Statistics
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Attendees</p>
                <p className="text-2xl font-bold">{event._count.attendees}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Scans</p>
                <p className="text-2xl font-bold">{event._count.scanLogs}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">Meal Types</p>
                <p className="text-2xl font-bold">{event.mealTypes.length}</p>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push(`/attendees?eventId=${event.id}`)}
              >
                <Users className="h-4 w-4 mr-2" />
                Manage Attendees
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push(`/scanner?eventId=${event.id}`)}
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Open Scanner
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start"
                onClick={() => router.push(`/dashboard?eventId=${event.id}`)}
              >
                <Calendar className="h-4 w-4 mr-2" />
                View Analytics
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Meal Types */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Utensils className="h-5 w-5 mr-2" />
              Meal Types
            </CardTitle>
            <CardDescription>
              Available meal options for this event
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {event.mealTypes.map((mealType) => (
                <div key={mealType.id} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium">{mealType.name}</h4>
                    <div className="flex space-x-1">
                      {mealType.isVegetarian && (
                        <Badge variant="secondary" className="text-xs">V</Badge>
                      )}
                      {mealType.isVegan && (
                        <Badge variant="secondary" className="text-xs">VG</Badge>
                      )}
                      {mealType.isGlutenFree && (
                        <Badge variant="secondary" className="text-xs">GF</Badge>
                      )}
                    </div>
                  </div>
                  {mealType.description && (
                    <p className="text-sm text-muted-foreground mb-2">
                      {mealType.description}
                    </p>
                  )}
                  <div className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <span>Max per person:</span>
                      <span>{mealType.maxQuantityPerAttendee}</span>
                    </div>
                    {mealType.totalQuantity && (
                      <div className="flex justify-between">
                        <span>Available:</span>
                        <span>{mealType.totalQuantity - mealType.consumedQuantity} / {mealType.totalQuantity}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Edit Dialog */}
        <EventFormDialog
          open={showEditDialog}
          onOpenChange={setShowEditDialog}
          event={event}
          onEventSaved={handleEventUpdated}
        />
      </div>
    </AppLayout>
  )
}
