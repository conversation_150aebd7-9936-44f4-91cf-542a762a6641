const { MongoClient, ObjectId } = require('mongodb');
const bcrypt = require('bcryptjs');

const url = 'mongodb://localhost:27017';
const dbName = 'foodscan';

async function seedDatabase() {
  const client = new MongoClient(url);
  
  try {
    await client.connect();
    console.log('🌱 Connected to MongoDB, seeding database...');
    
    const db = client.db(dbName);
    
    // Clear existing data
    await db.collection('users').deleteMany({});
    await db.collection('events').deleteMany({});
    await db.collection('attendees').deleteMany({});
    await db.collection('meal_types').deleteMany({});
    
    // Create admin user
    const adminPassword = await bcrypt.hash('admin123', 10);
    const adminResult = await db.collection('users').insertOne({
      _id: new ObjectId(),
      email: '<EMAIL>',
      name: 'Admin User',
      password: adminPassword,
      role: 'ADMIN',
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Create scanner user
    const scannerPassword = await bcrypt.hash('scanner123', 10);
    const scannerResult = await db.collection('users').insertOne({
      _id: new ObjectId(),
      email: '<EMAIL>',
      name: 'Scanner User',
      password: scannerPassword,
      role: 'SCANNER',
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Create sample event
    const eventResult = await db.collection('events').insertOne({
      _id: new ObjectId(),
      name: 'Tech Conference 2024',
      description: 'Annual technology conference with networking and meals',
      startDate: new Date('2024-03-15T08:00:00Z'),
      endDate: new Date('2024-03-15T18:00:00Z'),
      serviceStartTime: new Date('2024-03-15T12:00:00Z'),
      serviceEndTime: new Date('2024-03-15T14:00:00Z'),
      primaryColor: '#3b82f6',
      secondaryColor: '#1e40af',
      isActive: true,
      creatorId: adminResult.insertedId,
      createdAt: new Date(),
      updatedAt: new Date()
    });
    
    // Create meal types
    const mealTypesResult = await db.collection('meal_types').insertMany([
      {
        _id: new ObjectId(),
        name: 'Main Course',
        description: 'Primary meal with protein and sides',
        maxQuantityPerAttendee: 1,
        totalQuantity: 500,
        consumedQuantity: 0,
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: false,
        isAvailable: true,
        eventId: eventResult.insertedId,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        _id: new ObjectId(),
        name: 'Vegetarian Option',
        description: 'Plant-based main course',
        maxQuantityPerAttendee: 1,
        totalQuantity: 100,
        consumedQuantity: 0,
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: false,
        isAvailable: true,
        eventId: eventResult.insertedId,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);
    
    // Create sample attendees
    const attendeesResult = await db.collection('attendees').insertMany([
      {
        _id: new ObjectId(),
        name: 'John Doe',
        email: '<EMAIL>',
        qrCode: 'JD-2024-001',
        dietaryRestrictions: 'None',
        eventId: eventResult.insertedId,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        _id: new ObjectId(),
        name: 'Jane Smith',
        email: '<EMAIL>',
        qrCode: 'JS-2024-002',
        dietaryRestrictions: 'Vegetarian',
        eventId: eventResult.insertedId,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        _id: new ObjectId(),
        name: 'Mike Johnson',
        email: '<EMAIL>',
        qrCode: 'MJ-2024-003',
        dietaryRestrictions: 'Gluten-free',
        specialNotes: 'VIP guest',
        eventId: eventResult.insertedId,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]);
    
    console.log('✅ Database seeded successfully!');
    console.log(`👤 Created ${1} admin user`);
    console.log(`📱 Created ${1} scanner user`);
    console.log(`🎉 Created ${1} event`);
    console.log(`🍽️ Created ${mealTypesResult.insertedCount} meal types`);
    console.log(`👥 Created ${attendeesResult.insertedCount} attendees`);
    console.log('');
    console.log('🔑 Login credentials:');
    console.log('👤 Admin: <EMAIL> / admin123');
    console.log('📱 Scanner: <EMAIL> / scanner123');
    
  } catch (error) {
    console.error('❌ Error seeding database:', error);
  } finally {
    await client.close();
  }
}

seedDatabase();
