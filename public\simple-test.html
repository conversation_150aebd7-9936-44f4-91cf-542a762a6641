<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple QR Scanner Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            text-align: center;
        }
        
        .status.loading { background: #fff3cd; color: #856404; }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.waiting { background: #d1ecf1; color: #0c5460; }
        
        video {
            width: 100%;
            max-width: 400px;
            height: 300px;
            background: #000;
            border-radius: 8px;
            display: block;
            margin: 0 auto;
        }
        
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            width: 100%;
            max-width: 200px;
        }
        
        button:hover { background: #0056b3; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        
        .console {
            background: #000;
            color: #00ff00;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .hidden { display: none; }
        
        .center { text-align: center; }
    </style>
</head>
<body>
    <div class="card">
        <h1>🔍 Simple QR Scanner Test</h1>
        <p>Universal camera and QR code testing</p>
        
        <div id="status" class="status waiting">
            Ready to test camera access
        </div>
        
        <div id="device-info" class="card">
            <strong>Device Info:</strong>
            <div id="info-content">Detecting...</div>
        </div>
        
        <div class="center">
            <video id="video" class="hidden" playsinline muted></video>
        </div>
        
        <div class="center">
            <button id="start-btn">📷 Start Camera</button>
            <button id="scan-btn" class="hidden" disabled>📱 Scan Frame</button>
            <button id="upload-btn">📁 Upload Image</button>
        </div>
        
        <input type="file" id="file-input" accept="image/*" class="hidden">
        
        <div class="card">
            <h3>🧪 Test QR Codes:</h3>
            <div>JD-2024-001 (John Doe)</div>
            <div>JS-2024-002 (Jane Smith)</div>
            <div>MJ-2024-003 (Mike Johnson)</div>
        </div>
        
        <div class="console" id="console"></div>
    </div>

    <script>
        // Elements
        const statusDiv = document.getElementById('status');
        const deviceInfoDiv = document.getElementById('info-content');
        const video = document.getElementById('video');
        const startBtn = document.getElementById('start-btn');
        const scanBtn = document.getElementById('scan-btn');
        const uploadBtn = document.getElementById('upload-btn');
        const fileInput = document.getElementById('file-input');
        const consoleDiv = document.getElementById('console');
        
        let stream = null;

        // Console logging
        const originalLog = console.log;
        const originalError = console.error;
        
        function addToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type}: ${args.join(' ')}\n`;
            consoleDiv.textContent += message;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('LOG', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('ERROR', ...args);
        };

        // Status update
        function updateStatus(message, type = 'waiting') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            console.log('Status:', message);
        }

        // Device detection with compatibility checks
        function detectDevice() {
            const userAgent = navigator.userAgent.toLowerCase();
            const isIOS = /iphone|ipad|ipod/.test(userAgent);
            const isAndroid = /android/.test(userAgent);
            const isSafari = /safari/.test(userAgent) && !/chrome/.test(userAgent);
            const isSecure = location.protocol === 'https:' || location.hostname === 'localhost';

            let info = `
                User Agent: ${navigator.userAgent}
                Platform: ${navigator.platform}
                Screen: ${screen.width}x${screen.height}
                Protocol: ${location.protocol}
                Secure Context: ${isSecure}
                iOS: ${isIOS}
                Android: ${isAndroid}
                Safari: ${isSafari}
                MediaDevices: ${!!navigator.mediaDevices}
                getUserMedia: ${!!navigator.mediaDevices?.getUserMedia}
            `;

            deviceInfoDiv.textContent = info;
            console.log('Device info:', info);

            // Show warnings for common issues
            if (isIOS && !isSecure) {
                updateStatus('⚠️ iOS requires HTTPS for camera access', 'error');
                console.error('iOS device detected but not using HTTPS');
            } else if (!navigator.mediaDevices) {
                updateStatus('⚠️ MediaDevices API not supported', 'error');
                console.error('MediaDevices API not available');
            } else if (!navigator.mediaDevices.getUserMedia) {
                updateStatus('⚠️ getUserMedia not supported', 'error');
                console.error('getUserMedia not available');
            }
        }

        // Start camera with better error handling
        async function startCamera() {
            updateStatus('Starting camera...', 'loading');
            startBtn.disabled = true;

            try {
                // Check compatibility first
                if (!navigator.mediaDevices) {
                    throw new Error('MediaDevices API not supported. Please use a modern browser.');
                }

                if (!navigator.mediaDevices.getUserMedia) {
                    throw new Error('getUserMedia not supported. Please use HTTPS or update your browser.');
                }

                const isSecure = location.protocol === 'https:' || location.hostname === 'localhost';
                if (!isSecure) {
                    throw new Error('Camera access requires HTTPS. Please use https:// or access via localhost.');
                }

                console.log('📷 Requesting camera access...');

                // Very basic constraints
                const constraints = {
                    video: true,
                    audio: false
                };

                stream = await navigator.mediaDevices.getUserMedia(constraints);
                console.log('✅ Camera stream obtained');
                
                // Setup video
                video.srcObject = stream;
                video.setAttribute('playsinline', 'true');
                video.setAttribute('webkit-playsinline', 'true');
                video.muted = true;
                
                video.classList.remove('hidden');
                
                // Play video
                try {
                    await video.play();
                    console.log('✅ Video playing');
                } catch (playError) {
                    console.warn('⚠️ Video play issue:', playError);
                    video.muted = true;
                    video.volume = 0;
                    await video.play();
                }
                
                updateStatus('✅ Camera ready! Click "Scan Frame" to test QR detection', 'success');
                scanBtn.classList.remove('hidden');
                scanBtn.disabled = false;
                
            } catch (error) {
                console.error('❌ Camera error:', error);
                let errorMessage = 'Camera access failed';
                
                if (error.name === 'NotAllowedError') {
                    errorMessage = 'Camera permission denied. Please allow camera access.';
                } else if (error.name === 'NotFoundError') {
                    errorMessage = 'No camera found on this device.';
                } else if (error.name === 'NotReadableError') {
                    errorMessage = 'Camera is being used by another app.';
                } else {
                    errorMessage = `Camera error: ${error.message}`;
                }
                
                updateStatus(errorMessage, 'error');
                startBtn.disabled = false;
            }
        }

        // Scan current frame
        async function scanFrame() {
            if (!video || video.readyState !== video.HAVE_ENOUGH_DATA) {
                updateStatus('Video not ready, please wait...', 'error');
                return;
            }

            updateStatus('Scanning current frame...', 'loading');
            scanBtn.disabled = true;

            try {
                console.log('📷 Capturing frame...');
                
                // Create canvas and capture frame
                const canvas = document.createElement('canvas');
                canvas.width = video.videoWidth || 640;
                canvas.height = video.videoHeight || 480;
                const ctx = canvas.getContext('2d');
                ctx.drawImage(video, 0, 0);
                
                console.log('🖼️ Frame captured, converting to blob...');
                
                // Convert to blob
                canvas.toBlob(async (blob) => {
                    try {
                        console.log('🔍 Scanning blob for QR code...');
                        
                        // Try to import QR scanner
                        const QrScanner = (await import('https://unpkg.com/qr-scanner@1.4.2/qr-scanner.min.js')).default;
                        const result = await QrScanner.scanImage(blob);
                        
                        console.log('🎉 QR Code detected:', result);
                        updateStatus(`🎉 QR Code found: ${result}`, 'success');
                        
                        // Haptic feedback
                        if ('vibrate' in navigator) {
                            navigator.vibrate([100]);
                        }
                        
                    } catch (scanError) {
                        console.log('❌ No QR code found in current frame');
                        updateStatus('No QR code found. Position QR code in view and try again.', 'error');
                    }
                    
                    scanBtn.disabled = false;
                }, 'image/jpeg', 0.9);
                
            } catch (error) {
                console.error('❌ Scan error:', error);
                updateStatus('Scan failed. Please try again.', 'error');
                scanBtn.disabled = false;
            }
        }

        // File upload
        async function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            updateStatus('Scanning uploaded image...', 'loading');
            
            try {
                console.log('📁 Scanning uploaded file...');
                const QrScanner = (await import('https://unpkg.com/qr-scanner@1.4.2/qr-scanner.min.js')).default;
                const result = await QrScanner.scanImage(file);
                
                console.log('🎉 QR Code found in uploaded image:', result);
                updateStatus(`🎉 QR Code found: ${result}`, 'success');
                
            } catch (error) {
                console.error('❌ No QR code in uploaded image:', error);
                updateStatus('No QR code found in uploaded image', 'error');
            }
            
            event.target.value = '';
        }

        // Event listeners
        startBtn.addEventListener('click', startCamera);
        scanBtn.addEventListener('click', scanFrame);
        uploadBtn.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', handleFileUpload);

        // Initialize
        detectDevice();
        updateStatus('Ready to test. Click "Start Camera" to begin.');
        console.log('🎉 Simple QR Scanner test page loaded');
    </script>
</body>
</html>
