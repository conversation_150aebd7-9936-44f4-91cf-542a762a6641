"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Camera, 
  CameraOff, 
  Upload, 
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Hand
} from "lucide-react"

interface UniversalQRScannerProps {
  onScanResult: (data: string) => void
  onScanError: (error: string) => void
}

export function UniversalQRScanner({ onScanResult, onScanError }: UniversalQRScannerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const scanIntervalRef = useRef<NodeJS.Timeout | null>(null)
  
  const [status, setStatus] = useState<'waiting' | 'loading' | 'ready' | 'error'>('waiting')
  const [error, setError] = useState<string | null>(null)
  const [scanCount, setScanCount] = useState(0)
  const [deviceInfo, setDeviceInfo] = useState<string>('')

  // Detect device and browser
  useEffect(() => {
    const userAgent = navigator.userAgent.toLowerCase()
    const isIOS = /iphone|ipad|ipod/.test(userAgent)
    const isAndroid = /android/.test(userAgent)
    const isSafari = /safari/.test(userAgent) && !/chrome/.test(userAgent)
    const isChrome = /chrome/.test(userAgent)
    const isFirefox = /firefox/.test(userAgent)
    
    let info = 'Desktop'
    if (isIOS) info = 'iOS Safari'
    else if (isAndroid) info = 'Android'
    else if (isSafari) info = 'Safari'
    else if (isChrome) info = 'Chrome'
    else if (isFirefox) info = 'Firefox'
    
    setDeviceInfo(info)
    console.log('🔍 Device detected:', info)
  }, [])

  // Check for camera API availability
  const checkCameraSupport = useCallback(() => {
    console.log('🔍 Checking camera API support...')

    // Check if running on HTTPS or localhost
    const isSecure = location.protocol === 'https:' || location.hostname === 'localhost'
    console.log('🔒 Secure context:', isSecure)

    // Check for MediaDevices API
    const hasMediaDevices = !!(navigator.mediaDevices)
    console.log('📱 MediaDevices API:', hasMediaDevices)

    // Check for getUserMedia
    const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
    console.log('📷 getUserMedia API:', hasGetUserMedia)

    // Legacy getUserMedia check
    const hasLegacyGetUserMedia = !!(navigator.getUserMedia || navigator.webkitGetUserMedia || navigator.mozGetUserMedia)
    console.log('📷 Legacy getUserMedia:', hasLegacyGetUserMedia)

    return {
      isSecure,
      hasMediaDevices,
      hasGetUserMedia,
      hasLegacyGetUserMedia,
      isSupported: isSecure && (hasGetUserMedia || hasLegacyGetUserMedia)
    }
  }, [])

  // Universal camera initialization with compatibility checks
  const startCamera = useCallback(async () => {
    if (!videoRef.current) return

    console.log('📷 Universal Scanner: Starting camera...')
    setStatus('loading')
    setError(null)

    try {
      // Step 1: Check camera API support
      const support = checkCameraSupport()
      console.log('🔍 Camera support check:', support)

      if (!support.isSecure) {
        throw new Error('Camera access requires HTTPS. Please use https:// or access via localhost.')
      }

      if (!support.hasMediaDevices) {
        throw new Error('MediaDevices API not supported. Please use a modern browser.')
      }

      if (!support.hasGetUserMedia) {
        throw new Error('getUserMedia not supported. Please update your browser or use HTTPS.')
      }

      // Step 2: Request camera access with error handling
      console.log('📷 Requesting camera access...')

      let stream
      try {
        // Try modern API first
        const constraints = {
          video: {
            facingMode: 'environment'
          },
          audio: false
        }

        stream = await navigator.mediaDevices.getUserMedia(constraints)
        console.log('✅ Camera stream obtained via modern API')

      } catch (modernError) {
        console.warn('⚠️ Modern API failed, trying basic constraints:', modernError)

        // Fallback to very basic constraints
        try {
          stream = await navigator.mediaDevices.getUserMedia({ video: true })
          console.log('✅ Camera stream obtained via basic constraints')
        } catch (basicError) {
          console.error('❌ Both modern and basic constraints failed')
          throw basicError
        }
      }

      streamRef.current = stream

      // Step 2: Setup video element
      const video = videoRef.current
      video.srcObject = stream
      
      // Universal video attributes
      video.setAttribute('playsinline', 'true')
      video.setAttribute('webkit-playsinline', 'true')
      video.muted = true
      video.autoplay = true

      // Wait for video to be ready
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => reject(new Error('Video load timeout')), 15000)
        
        const onReady = async () => {
          clearTimeout(timeout)
          try {
            await video.play()
            console.log('✅ Video playing')
            resolve(true)
          } catch (playError) {
            console.warn('⚠️ Video play issue, trying fix:', playError)
            video.muted = true
            video.volume = 0
            await video.play()
            resolve(true)
          }
        }

        if (video.readyState >= 2) {
          onReady()
        } else {
          video.addEventListener('loadeddata', onReady, { once: true })
          video.addEventListener('canplay', onReady, { once: true })
        }
        
        video.addEventListener('error', (err) => {
          clearTimeout(timeout)
          reject(err)
        }, { once: true })
      })

      setStatus('ready')
      console.log('🎉 Camera ready! Use "Scan Frame" button to scan QR codes.')

    } catch (err) {
      console.error('❌ Camera failed:', err)
      let errorMessage = 'Camera access failed'
      let troubleshooting = []

      if (err instanceof Error) {
        if (err.name === 'NotAllowedError') {
          errorMessage = 'Camera permission denied'
          troubleshooting = [
            'Allow camera access when prompted',
            'Check browser settings for camera permissions',
            'Refresh the page and try again'
          ]
        } else if (err.name === 'NotFoundError') {
          errorMessage = 'No camera found on this device'
          troubleshooting = [
            'Make sure your device has a camera',
            'Check if other apps can access the camera',
            'Try restarting your browser'
          ]
        } else if (err.name === 'NotReadableError') {
          errorMessage = 'Camera is being used by another application'
          troubleshooting = [
            'Close other apps that might be using the camera',
            'Restart your browser',
            'Restart your device if needed'
          ]
        } else if (err.message.includes('HTTPS') || err.message.includes('https')) {
          errorMessage = 'HTTPS required for camera access'
          troubleshooting = [
            'Camera access requires HTTPS on mobile devices',
            'Use ngrok to create HTTPS tunnel: ngrok http 3000',
            'Or access via https://localhost:3000 with proper certificates',
            'HTTP only works on localhost for desktop browsers'
          ]
        } else if (err.message.includes('MediaDevices') || err.message.includes('getUserMedia')) {
          errorMessage = 'Camera API not supported'
          troubleshooting = [
            'Your browser or device doesn\'t support camera access',
            'Try using a modern browser (Chrome, Firefox, Safari)',
            'Make sure you\'re using HTTPS',
            'Update your browser to the latest version'
          ]
        } else if (err.message.includes('timeout')) {
          errorMessage = 'Camera initialization timed out'
          troubleshooting = [
            'Try again - sometimes camera takes time to initialize',
            'Close other apps using the camera',
            'Restart your browser'
          ]
        } else {
          errorMessage = `Camera error: ${err.message}`
          troubleshooting = [
            'Try refreshing the page',
            'Check if you\'re using HTTPS (required for mobile)',
            'Make sure camera permissions are allowed',
            'Try the image upload option instead'
          ]
        }
      }

      // Store troubleshooting info for error display
      setError(errorMessage)
      setStatus('error')
      onScanError(errorMessage)
    }
  }, [onScanError])

  // Manual QR scanning function (moved to button click)

  // Cleanup
  const cleanup = useCallback(() => {
    if (scanIntervalRef.current) {
      clearInterval(scanIntervalRef.current)
      scanIntervalRef.current = null
    }
    
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
  }, [])

  useEffect(() => {
    return cleanup
  }, [cleanup])

  // File upload handler
  const handleFileUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith('image/')) {
      onScanError('Please select an image file')
      return
    }

    try {
      console.log('📁 Scanning uploaded image...')
      
      // Try API first
      const formData = new FormData()
      formData.append('image', file)
      
      const response = await fetch('/api/scan-qr-image', {
        method: 'POST',
        body: formData
      })

      if (response.ok) {
        const result = await response.text()
        if (result && result !== 'No QR code found') {
          onScanResult(result)
          setScanCount(prev => prev + 1)
          console.log('✅ QR found in uploaded image')
          return
        }
      }

      // Fallback to client-side scanning
      const QrScannerModule = await import('qr-scanner')
      const QrScanner = QrScannerModule.default
      const result = await QrScanner.scanImage(file)
      onScanResult(result)
      setScanCount(prev => prev + 1)
      console.log('✅ QR found in uploaded image (fallback)')
      
    } catch (err) {
      console.error('❌ No QR in uploaded image:', err)
      onScanError('No QR code found in the uploaded image')
    }

    e.target.value = ""
  }, [onScanResult, onScanError])

  // Waiting state - Auto-start camera when component mounts
  useEffect(() => {
    if (status === 'waiting') {
      // Auto-start camera after a short delay
      const timer = setTimeout(() => {
        startCamera()
      }, 500)

      return () => clearTimeout(timer)
    }
  }, [status, startCamera])

  // Waiting state - Show start button
  if (status === 'waiting') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Hand className="h-5 w-5" />
            <span>Ready to Start</span>
            <Badge variant="secondary">{deviceInfo}</Badge>
          </CardTitle>
          <CardDescription>
            Camera will start automatically or click to start manually
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <Camera className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <div className="font-medium">📱 Instructions:</div>
                <div className="text-sm space-y-1">
                  <div>• Camera will start automatically</div>
                  <div>• Allow camera access when prompted</div>
                  <div>• Point camera at QR code</div>
                  <div>• Click "Scan Frame" to capture and scan</div>
                </div>
              </div>
            </AlertDescription>
          </Alert>

          <div className="text-center py-8">
            <Camera className="mx-auto h-16 w-16 text-muted-foreground" />
            <div className="mt-4">
              <h3 className="text-lg font-semibold">Starting Camera...</h3>
              <p className="text-muted-foreground mt-2">
                Camera will start automatically. If it doesn't, click the button below.
              </p>
            </div>
            <Button onClick={startCamera} size="lg" className="mt-4 w-full max-w-sm">
              <Camera className="mr-2 h-5 w-5" />
              Start Camera Manually
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Loading state
  if (status === 'loading') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            <span>Starting Camera</span>
            <Badge variant="secondary">{deviceInfo}</Badge>
          </CardTitle>
          <CardDescription>
            Initializing camera and QR scanner...
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center py-8">
          <div className="space-y-4">
            <RefreshCw className="mx-auto h-12 w-12 animate-spin text-muted-foreground" />
            <div>
              <p className="text-muted-foreground">Please wait while we start your camera...</p>
              <p className="text-xs text-muted-foreground mt-2">
                This should take less than 15 seconds
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Error state
  if (status === 'error') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CameraOff className="h-5 w-5" />
            <span>Camera Error</span>
            <Badge variant="secondary">{deviceInfo}</Badge>
          </CardTitle>
          <CardDescription>
            Unable to access camera
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Error:</strong> {error}
            </AlertDescription>
          </Alert>

          <Alert>
            <Camera className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <div className="font-medium">Troubleshooting Steps:</div>
                <div className="text-sm space-y-1">
                  <div>1. Refresh the page and try again</div>
                  <div>2. Check browser permissions for camera access</div>
                  <div>3. Close other apps that might be using the camera</div>
                  <div>4. Try the image upload option below</div>
                </div>
              </div>
            </AlertDescription>
          </Alert>

          <div className="flex flex-col gap-2">
            <Button onClick={() => {
              setStatus('waiting')
              setError(null)
            }}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            
            <Button onClick={() => fileInputRef.current?.click()} variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Upload QR Code Image
            </Button>
            
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>
        </CardContent>
      </Card>
    )
  }

  // Working scanner
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Camera className="h-5 w-5 text-green-600" />
              <span>QR Scanner</span>
              <Badge variant="outline" className="text-green-600 border-green-600">
                <CheckCircle className="h-3 w-3 mr-1" />
                Active
              </Badge>
              <Badge variant="secondary">{deviceInfo}</Badge>
            </CardTitle>
            <CardDescription>
              Point camera at QR code • {scanCount} scans completed
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Video Container */}
        <div className="relative aspect-square bg-black rounded-lg overflow-hidden">
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            playsInline
            muted
            autoPlay
            style={{
              WebkitPlaysinline: true,
              objectFit: 'cover'
            } as any}
          />
          
          {/* Scanning overlay */}
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="w-48 h-48 border-2 border-primary rounded-lg relative">
              <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-primary rounded-tl-lg"></div>
              <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-primary rounded-tr-lg"></div>
              <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-primary rounded-bl-lg"></div>
              <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-primary rounded-br-lg"></div>
            </div>
          </div>

          {/* Status indicator */}
          <div className="absolute top-4 left-4">
            <Badge variant="secondary" className="bg-black bg-opacity-50 text-white border-none">
              <Camera className="h-3 w-3 mr-1" />
              Live
            </Badge>
          </div>
        </div>

        {/* Controls */}
        <div className="flex justify-center space-x-2">
          <Button
            onClick={async () => {
              if (!videoRef.current || !canvasRef.current) return

              try {
                const video = videoRef.current
                const canvas = canvasRef.current

                if (video.readyState !== video.HAVE_ENOUGH_DATA) {
                  onScanError('Video not ready. Please wait a moment and try again.')
                  return
                }

                const context = canvas.getContext('2d')
                if (!context) return

                // Capture frame
                canvas.width = video.videoWidth || 640
                canvas.height = video.videoHeight || 480
                context.drawImage(video, 0, 0, canvas.width, canvas.height)

                // Convert to blob and scan
                canvas.toBlob(async (blob) => {
                  if (!blob) return

                  try {
                    const QrScannerModule = await import('qr-scanner')
                    const QrScanner = QrScannerModule.default
                    const result = await QrScanner.scanImage(blob)

                    console.log('🎉 QR Code found:', result)
                    setScanCount(prev => prev + 1)
                    onScanResult(result)

                    if ('vibrate' in navigator) {
                      navigator.vibrate([100])
                    }
                  } catch (scanError) {
                    onScanError('No QR code found in current frame. Position QR code in view and try again.')
                  }
                }, 'image/jpeg', 0.8)

              } catch (err) {
                onScanError('Failed to capture frame. Please try again.')
              }
            }}
            size="sm"
            className="bg-green-600 hover:bg-green-700"
          >
            <Camera className="h-4 w-4 mr-1" />
            Scan Frame
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            title="Upload QR code image"
          >
            <Upload className="h-4 w-4" />
          </Button>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            className="hidden"
          />
        </div>

        {/* Instructions */}
        <div className="text-center text-sm text-muted-foreground">
          <p>📱 Position QR code within the frame and click "Scan Frame"</p>
          <p className="text-xs mt-1">Manual scanning for better reliability and battery life</p>
        </div>

        {/* Hidden canvas for frame capture */}
        <canvas ref={canvasRef} className="hidden" />
      </CardContent>
    </Card>
  )
}
