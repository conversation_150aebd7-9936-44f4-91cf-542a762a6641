const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function testPrisma() {
  try {
    console.log('🔍 Testing Prisma connection...');
    
    // Test connection
    await prisma.$connect();
    console.log('✅ Prisma connected successfully');
    
    // Find the admin user
    const user = await prisma.user.findUnique({
      where: {
        email: '<EMAIL>'
      }
    });
    
    if (!user) {
      console.log('❌ User not found via Prisma');
      return;
    }
    
    console.log('✅ User found via Prisma:', {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role
    });
    
    // Test password comparison
    const testPassword = 'admin123';
    const isValid = await bcrypt.compare(testPassword, user.password);
    
    console.log('🔑 Password test via Prisma:');
    console.log('  Input password:', testPassword);
    console.log('  Comparison result:', isValid ? '✅ VALID' : '❌ INVALID');
    
    // Test finding all users
    const allUsers = await prisma.user.findMany();
    console.log('👥 Total users found:', allUsers.length);
    
    // Test finding events
    const events = await prisma.event.findMany();
    console.log('🎉 Total events found:', events.length);
    
    // Test finding attendees
    const attendees = await prisma.attendee.findMany();
    console.log('👤 Total attendees found:', attendees.length);
    
  } catch (error) {
    console.error('❌ Error testing Prisma:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testPrisma();
