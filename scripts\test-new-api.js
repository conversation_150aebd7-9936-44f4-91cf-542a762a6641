const { MongoClient, ObjectId } = require('mongodb');

const url = 'mongodb://localhost:27017';
const dbName = 'foodscan';

async function testNewAPI() {
  const client = new MongoClient(url);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db(dbName);
    const eventsCollection = db.collection('events');
    
    // Find an existing event to test update
    const existingEvent = await eventsCollection.findOne({});
    
    if (!existingEvent) {
      console.log('❌ No events found in database');
      return;
    }
    
    console.log('✅ Found event to test:', existingEvent.name);
    console.log('Event ID:', existingEvent._id.toString());
    
    // Test the update data structure that would be sent from the frontend
    const updateData = {
      name: 'API Test Event (Updated)',
      description: 'Updated via new MongoDB API',
      startDate: '2024-03-15T08:00:00.000Z',
      endDate: '2024-03-15T18:00:00.000Z',
      serviceStartTime: '1970-01-01T12:00:00.000Z',
      serviceEndTime: '1970-01-01T14:00:00.000Z',
      primaryColor: '#00ff00',
      secondaryColor: '#00cc00',
      logoUrl: '',
      isActive: true,
      mealTypes: [
        {
          name: 'API Test Main Course',
          description: 'Test main course via API',
          maxQuantityPerAttendee: 1,
          totalQuantity: 100,
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false
        },
        {
          name: 'API Test Vegetarian',
          description: 'Test vegetarian option via API',
          maxQuantityPerAttendee: 1,
          totalQuantity: 50,
          isVegetarian: true,
          isVegan: true,
          isGlutenFree: true
        }
      ]
    };
    
    console.log('\n📝 Testing direct MongoDB update...');
    
    // Test direct MongoDB update (simulating our new API)
    const eventId = existingEvent._id.toString();
    
    // Update event
    const updateResult = await eventsCollection.updateOne(
      { _id: new ObjectId(eventId) },
      {
        $set: {
          name: updateData.name,
          description: updateData.description,
          startDate: new Date(updateData.startDate),
          endDate: new Date(updateData.endDate),
          serviceStartTime: new Date(updateData.serviceStartTime),
          serviceEndTime: new Date(updateData.serviceEndTime),
          primaryColor: updateData.primaryColor,
          secondaryColor: updateData.secondaryColor,
          logoUrl: updateData.logoUrl,
          isActive: updateData.isActive,
          updatedAt: new Date()
        }
      }
    );
    
    if (updateResult.modifiedCount > 0) {
      console.log('✅ Event update successful');
    } else {
      console.log('❌ Event update failed');
      return;
    }
    
    // Update meal types
    const mealTypesCollection = db.collection('meal_types');
    
    // Delete existing meal types
    const deleteResult = await mealTypesCollection.deleteMany({ eventId: eventId });
    console.log(`✅ Deleted ${deleteResult.deletedCount} existing meal types`);
    
    // Create new meal types
    if (updateData.mealTypes.length > 0) {
      const newMealTypes = updateData.mealTypes.map((meal) => ({
        _id: new ObjectId(),
        name: meal.name,
        description: meal.description,
        maxQuantityPerAttendee: meal.maxQuantityPerAttendee,
        totalQuantity: meal.totalQuantity,
        consumedQuantity: 0,
        isVegetarian: meal.isVegetarian,
        isVegan: meal.isVegan,
        isGlutenFree: meal.isGlutenFree,
        isAvailable: true,
        eventId: eventId,
        createdAt: new Date(),
        updatedAt: new Date()
      }));
      
      const insertResult = await mealTypesCollection.insertMany(newMealTypes);
      console.log(`✅ Created ${insertResult.insertedCount} new meal types`);
    }
    
    // Fetch the complete updated event
    const updatedEvent = await eventsCollection.findOne({ _id: new ObjectId(eventId) });
    const eventMealTypes = await mealTypesCollection.find({ eventId: eventId }).toArray();
    
    // Get counts
    const attendeesCount = await db.collection('attendees').countDocuments({ eventId: eventId });
    const scanLogsCount = await db.collection('scan_logs').countDocuments({ eventId: eventId });
    
    console.log('\n📋 Updated event details:');
    console.log('- Name:', updatedEvent.name);
    console.log('- Description:', updatedEvent.description);
    console.log('- Primary Color:', updatedEvent.primaryColor);
    console.log('- Meal Types:', eventMealTypes.length);
    console.log('- Attendees Count:', attendeesCount);
    console.log('- Scan Logs Count:', scanLogsCount);
    
    if (eventMealTypes.length > 0) {
      console.log('\n🍽️ Meal Types:');
      eventMealTypes.forEach((mt, index) => {
        console.log(`  ${index + 1}. ${mt.name} (${mt.isVegetarian ? 'V' : ''}${mt.isVegan ? 'VG' : ''}${mt.isGlutenFree ? 'GF' : ''})`);
      });
    }
    
    console.log('\n✅ MongoDB-based API simulation successful!');
    console.log('🎉 The new API should work correctly');
    
  } catch (error) {
    console.error('❌ Error during test:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testNewAPI().catch(console.error);
