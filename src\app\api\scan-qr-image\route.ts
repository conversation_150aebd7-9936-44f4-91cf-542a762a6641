import { NextRequest, NextResponse } from "next/server"

export async function POST(request: NextRequest) {
  try {
    console.log('📷 QR Image Scan API called')
    
    // Get the image from the request
    const formData = await request.formData()
    const imageFile = formData.get('image') as File
    
    if (!imageFile) {
      // Try to get blob directly
      const blob = await request.blob()
      if (!blob) {
        return NextResponse.json({ error: 'No image provided' }, { status: 400 })
      }
      
      // Try to scan the blob
      try {
        const QrScanner = (await import('qr-scanner')).default
        const result = await QrScanner.scanImage(blob)
        console.log('✅ QR code found in blob:', result)
        return new NextResponse(result, { status: 200 })
      } catch (error) {
        console.log('❌ No QR code found in blob')
        return new NextResponse('No QR code found', { status: 200 })
      }
    }
    
    // Scan the uploaded file
    try {
      const QrScanner = (await import('qr-scanner')).default
      const result = await QrScanner.scanImage(imageFile)
      console.log('✅ QR code found in uploaded file:', result)
      return new NextResponse(result, { status: 200 })
    } catch (error) {
      console.log('❌ No QR code found in uploaded file')
      return new NextResponse('No QR code found', { status: 200 })
    }
    
  } catch (error) {
    console.error('❌ QR scan API error:', error)
    return NextResponse.json({ 
      error: 'Failed to scan image for QR code' 
    }, { status: 500 })
  }
}
