<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>🍎 iOS QR Scanner Test</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f7;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #007AFF, #5856D6);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 14px;
        }
        
        .content {
            padding: 20px;
        }
        
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-size: 14px;
            text-align: center;
        }
        
        .status.waiting {
            background: #E3F2FD;
            color: #1976D2;
            border: 1px solid #BBDEFB;
        }
        
        .status.loading {
            background: #FFF3E0;
            color: #F57C00;
            border: 1px solid #FFCC02;
        }
        
        .status.success {
            background: #E8F5E8;
            color: #2E7D32;
            border: 1px solid #A5D6A7;
        }
        
        .status.error {
            background: #FFEBEE;
            color: #C62828;
            border: 1px solid #FFCDD2;
        }
        
        video {
            width: 100%;
            height: 250px;
            background: #000;
            border-radius: 8px;
            object-fit: cover;
        }
        
        .video-container {
            position: relative;
            margin: 15px 0;
        }
        
        .overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 150px;
            height: 150px;
            border: 2px solid #007AFF;
            border-radius: 8px;
            pointer-events: none;
        }
        
        .overlay::before,
        .overlay::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            border: 3px solid #007AFF;
        }
        
        .overlay::before {
            top: -3px;
            left: -3px;
            border-right: none;
            border-bottom: none;
            border-radius: 8px 0 0 0;
        }
        
        .overlay::after {
            bottom: -3px;
            right: -3px;
            border-left: none;
            border-top: none;
            border-radius: 0 0 8px 0;
        }
        
        .btn {
            width: 100%;
            padding: 16px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            margin: 8px 0;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #007AFF;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056CC;
        }
        
        .btn-secondary {
            background: #F2F2F7;
            color: #007AFF;
            border: 1px solid #D1D1D6;
        }
        
        .btn-secondary:hover {
            background: #E5E5EA;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .device-info {
            background: #F2F2F7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-size: 12px;
            color: #666;
        }
        
        .device-info strong {
            color: #333;
        }
        
        .qr-codes {
            background: #F8F9FA;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .qr-codes h3 {
            margin-bottom: 10px;
            color: #333;
            font-size: 16px;
        }
        
        .qr-code {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 6px;
            border: 1px solid #E1E1E1;
            font-family: monospace;
            font-size: 14px;
        }
        
        .console {
            background: #1C1C1E;
            color: #00FF00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin: 15px 0;
            white-space: pre-wrap;
        }
        
        .hidden {
            display: none;
        }
        
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .container {
                border-radius: 0;
                box-shadow: none;
            }
            
            .header {
                padding: 15px;
            }
            
            .content {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍎 iOS QR Scanner</h1>
            <p>iPhone & Safari Compatibility Test</p>
        </div>
        
        <div class="content">
            <div id="status" class="status waiting">
                Ready to test iOS QR scanning
            </div>
            
            <div class="device-info">
                <div><strong>Device:</strong> <span id="device-type">Detecting...</span></div>
                <div><strong>Browser:</strong> <span id="browser-type">Detecting...</span></div>
                <div><strong>iOS Version:</strong> <span id="ios-version">Detecting...</span></div>
                <div><strong>Screen:</strong> <span id="screen-size">Detecting...</span></div>
            </div>
            
            <div class="video-container hidden" id="video-container">
                <video id="video" playsinline muted autoplay></video>
                <div class="overlay"></div>
            </div>
            
            <button id="start-btn" class="btn btn-primary">
                📷 Start Camera
            </button>
            
            <button id="upload-btn" class="btn btn-secondary">
                📁 Upload QR Image
            </button>
            
            <input type="file" id="file-input" accept="image/*" class="hidden">
            
            <div class="qr-codes">
                <h3>🧪 Test QR Codes:</h3>
                <div class="qr-code">JD-2024-001 (John Doe)</div>
                <div class="qr-code">JS-2024-002 (Jane Smith)</div>
                <div class="qr-code">MJ-2024-003 (Mike Johnson)</div>
            </div>
            
            <div class="console" id="console"></div>
        </div>
    </div>

    <script type="module">
        // Console logging
        const consoleDiv = document.getElementById('console');
        const originalLog = console.log;
        const originalError = console.error;
        
        function addToConsole(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = `[${timestamp}] ${type}: ${args.join(' ')}\n`;
            consoleDiv.textContent += message;
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToConsole('LOG', ...args);
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToConsole('ERROR', ...args);
        };

        // Device detection
        function detectDevice() {
            const userAgent = navigator.userAgent.toLowerCase();
            const isIOS = /iphone|ipad|ipod/.test(userAgent);
            const isSafari = /safari/.test(userAgent) && !/chrome|crios|fxios/.test(userAgent);
            const isIPhone = /iphone/.test(userAgent);
            
            let iosVersion = 'Not iOS';
            if (isIOS) {
                const match = userAgent.match(/os (\d+)_(\d+)_?(\d+)?/);
                if (match) {
                    iosVersion = `${match[1]}.${match[2]}${match[3] ? '.' + match[3] : ''}`;
                }
            }
            
            let deviceType = 'Desktop';
            if (isIPhone) deviceType = 'iPhone';
            else if (userAgent.includes('ipad')) deviceType = 'iPad';
            else if (userAgent.includes('android')) deviceType = 'Android';
            
            let browserType = 'Unknown';
            if (isSafari) browserType = 'Safari';
            else if (userAgent.includes('chrome')) browserType = 'Chrome';
            else if (userAgent.includes('firefox')) browserType = 'Firefox';
            
            document.getElementById('device-type').textContent = deviceType;
            document.getElementById('browser-type').textContent = browserType;
            document.getElementById('ios-version').textContent = iosVersion;
            document.getElementById('screen-size').textContent = `${window.screen.width}x${window.screen.height}`;
            
            console.log('🍎 Device detected:', { deviceType, browserType, iosVersion, isIOS, isSafari });
            
            return { isIOS, isSafari, isIPhone, iosVersion };
        }

        // Initialize
        const deviceInfo = detectDevice();
        const statusDiv = document.getElementById('status');
        const startBtn = document.getElementById('start-btn');
        const uploadBtn = document.getElementById('upload-btn');
        const fileInput = document.getElementById('file-input');
        const video = document.getElementById('video');
        const videoContainer = document.getElementById('video-container');
        
        let scanner = null;
        let stream = null;

        function updateStatus(message, type = 'waiting') {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            console.log('Status:', message);
        }

        // Start camera function
        async function startCamera() {
            updateStatus('Starting camera...', 'loading');
            startBtn.disabled = true;
            
            try {
                console.log('📷 Requesting camera access...');
                
                const constraints = {
                    video: {
                        facingMode: 'environment',
                        width: { ideal: deviceInfo.isIPhone ? 1280 : 1920 },
                        height: { ideal: deviceInfo.isIPhone ? 720 : 1080 },
                        frameRate: { ideal: 30 }
                    }
                };
                
                stream = await navigator.mediaDevices.getUserMedia(constraints);
                console.log('✅ Camera access granted');
                
                video.srcObject = stream;
                videoContainer.classList.remove('hidden');
                
                // iOS requires explicit play
                try {
                    await video.play();
                    console.log('✅ Video playing');
                } catch (playError) {
                    console.warn('⚠️ Video play issue:', playError);
                }
                
                // Initialize QR Scanner
                console.log('🔧 Loading QR Scanner...');
                const QrScanner = (await import('https://unpkg.com/qr-scanner@1.4.2/qr-scanner.min.js')).default;
                
                scanner = new QrScanner(
                    video,
                    result => {
                        console.log('🎉 QR Code detected:', result.data);
                        updateStatus(`QR Code found: ${result.data}`, 'success');
                        
                        // Haptic feedback for iOS
                        if ('vibrate' in navigator) {
                            navigator.vibrate([100]);
                        }
                    },
                    {
                        onDecodeError: error => {
                            console.debug('🔍 Scanning...', error.message);
                        },
                        highlightScanRegion: true,
                        highlightCodeOutline: true,
                        preferredCamera: 'environment',
                        maxScansPerSecond: deviceInfo.isIPhone ? 2 : 3
                    }
                );
                
                console.log('🚀 Starting QR scanner...');
                await scanner.start();
                
                updateStatus('🎉 Scanner ready! Point camera at QR code', 'success');
                console.log('✅ QR Scanner ready!');
                
            } catch (error) {
                console.error('❌ Camera/Scanner error:', error);
                let errorMessage = 'Camera access failed';
                
                if (error.name === 'NotAllowedError') {
                    errorMessage = 'Camera permission denied. Please allow camera access.';
                } else if (error.name === 'NotFoundError') {
                    errorMessage = 'No camera found on this device.';
                } else if (error.name === 'NotReadableError') {
                    errorMessage = 'Camera is being used by another app.';
                }
                
                updateStatus(errorMessage, 'error');
                startBtn.disabled = false;
            }
        }

        // File upload function
        async function handleFileUpload(event) {
            const file = event.target.files[0];
            if (!file) return;
            
            updateStatus('Scanning uploaded image...', 'loading');
            
            try {
                console.log('📁 Scanning uploaded image...');
                const QrScanner = (await import('https://unpkg.com/qr-scanner@1.4.2/qr-scanner.min.js')).default;
                const result = await QrScanner.scanImage(file);
                
                console.log('🎉 QR Code found in image:', result);
                updateStatus(`QR Code found: ${result}`, 'success');
                
            } catch (error) {
                console.error('❌ No QR code in image:', error);
                updateStatus('No QR code found in uploaded image', 'error');
            }
            
            event.target.value = '';
        }

        // Event listeners
        startBtn.addEventListener('click', startCamera);
        uploadBtn.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', handleFileUpload);
        
        // Orientation change handling for iOS
        window.addEventListener('orientationchange', () => {
            console.log('📱 Orientation changed');
            setTimeout(() => {
                if (video.srcObject) {
                    console.log('🔄 Adjusting video for new orientation');
                }
            }, 300);
        });
        
        // Initial status
        if (deviceInfo.isIOS) {
            updateStatus('🍎 iOS device detected. Tap "Start Camera" to begin.', 'waiting');
        } else {
            updateStatus('📱 Ready to test. Tap "Start Camera" to begin.', 'waiting');
        }
        
        console.log('🎉 iOS QR Scanner test page loaded');
    </script>
</body>
</html>
