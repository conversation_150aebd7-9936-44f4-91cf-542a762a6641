"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { AppLayout } from "@/components/layout/app-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Input } from "@/components/ui/input"
import { 
  Plus, 
  Search, 
  Upload, 
  Download, 
  QrCode,
  Users,
  FileText,
  Archive,
  Filter,
  MoreHorizontal
} from "lucide-react"

// Mock data - will be replaced with real data later
const mockAttendees = [
  {
    id: "1",
    name: "<PERSON>",
    email: "<EMAIL>",
    qrCode: "JD-2024-001",
    eventId: "1",
    eventName: "Tech Conference 2024",
    dietaryRestrictions: "Vegetarian",
    specialNotes: "Wheelchair accessible seating",
    scannedAt: null,
    createdAt: "2024-01-15T10:00:00Z"
  },
  {
    id: "2",
    name: "<PERSON>",
    email: "<EMAIL>",
    qrCode: "JS-2024-002",
    eventId: "1",
    eventName: "Tech Conference 2024",
    dietaryRestrictions: "Gluten-free",
    specialNotes: "",
    scannedAt: "2024-01-20T12:30:00Z",
    createdAt: "2024-01-15T10:05:00Z"
  },
  {
    id: "3",
    name: "Mike Johnson",
    email: "<EMAIL>",
    qrCode: "MJ-2024-003",
    eventId: "2",
    eventName: "Company Holiday Party",
    dietaryRestrictions: "",
    specialNotes: "VIP guest",
    scannedAt: null,
    createdAt: "2024-01-16T14:20:00Z"
  }
]

const mockEvents = [
  { id: "1", name: "Tech Conference 2024" },
  { id: "2", name: "Company Holiday Party" },
  { id: "3", name: "Product Launch Event" }
]

export default function AttendeesPage() {
  const { data: session, status } = useSession()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedEvent, setSelectedEvent] = useState<string>("all")
  const [attendees, setAttendees] = useState<any[]>([])
  const [events, setEvents] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [mounted, setMounted] = useState(false)

  // All hooks at the top level - no conditional hooks
  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        const response = await fetch('/api/events')
        if (response.ok) {
          const data = await response.json()
          setEvents(data)
        }
      } catch (error) {
        console.error('Error fetching events:', error)
      }
    }

    const fetchAttendees = async () => {
      try {
        setLoading(true)
        const url = selectedEvent === "all"
          ? '/api/attendees'
          : `/api/attendees?eventId=${selectedEvent}`
        const response = await fetch(url)
        if (response.ok) {
          const data = await response.json()
          setAttendees(data)
        }
      } catch (error) {
        console.error('Error fetching attendees:', error)
      } finally {
        setLoading(false)
      }
    }

    if (mounted && session && session.user.role === "ADMIN") {
      fetchEvents()
      fetchAttendees()
    }
  }, [mounted, session, selectedEvent])

  // Conditional returns after all hooks
  if (status === "loading" || !mounted) {
    return (
      <AppLayout>
        <div className="flex h-screen items-center justify-center">
          <LoadingSpinner size="lg" />
        </div>
      </AppLayout>
    )
  }

  if (!session) {
    redirect("/auth/signin")
    return null
  }

  if (session.user.role !== "ADMIN") {
    redirect("/dashboard")
    return null
  }

  // Show loading state while fetching attendees
  if (loading) {
    return (
      <AppLayout>
        <div className="flex h-screen items-center justify-center">
          <LoadingSpinner size="lg" />
        </div>
      </AppLayout>
    )
  }

  const filteredAttendees = attendees.filter(attendee => {
    const matchesSearch =
      attendee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      attendee.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      attendee.qrCode.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesEvent = selectedEvent === "all" || attendee.eventId === selectedEvent

    return matchesSearch && matchesEvent
  })

  const stats = {
    total: attendees.length,
    scanned: attendees.filter(a => a.scannedAt).length,
    pending: attendees.filter(a => !a.scannedAt).length
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit"
    })
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Attendees</h1>
            <p className="text-muted-foreground">
              Manage attendees and generate QR codes for events
            </p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Import CSV
            </Button>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Attendee
            </Button>
          </div>
        </div>

        {/* Stats */}
        <div className="grid gap-4 md:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Attendees</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">
                Across all events
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Scanned</CardTitle>
              <QrCode className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.scanned}</div>
              <p className="text-xs text-muted-foreground">
                {((stats.scanned / stats.total) * 100).toFixed(1)}% completion
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <FileText className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.pending}</div>
              <p className="text-xs text-muted-foreground">
                Awaiting check-in
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Actions */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search attendees..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <select
            value={selectedEvent}
            onChange={(e) => setSelectedEvent(e.target.value)}
            className="px-3 py-2 border border-input bg-background rounded-md text-sm"
          >
            <option value="all">All Events</option>
            {events.map((event) => (
              <option key={event.id} value={event.id}>
                {event.name}
              </option>
            ))}
          </select>

          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <QrCode className="mr-2 h-4 w-4" />
              Generate QR Codes
            </Button>
          </div>
        </div>

        {/* Attendees Table */}
        <Card>
          <CardHeader>
            <CardTitle>Attendee List</CardTitle>
            <CardDescription>
              {filteredAttendees.length} of {attendees.length} attendees
            </CardDescription>
          </CardHeader>
          <CardContent>
            {filteredAttendees.length === 0 ? (
              <div className="text-center py-8">
                <Users className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-4 text-lg font-semibold">No attendees found</h3>
                <p className="text-muted-foreground">
                  {searchTerm || selectedEvent !== "all" 
                    ? "Try adjusting your search or filter criteria" 
                    : "Get started by adding attendees or importing a CSV file"
                  }
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredAttendees.map((attendee) => (
                  <div key={attendee.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3">
                        <div>
                          <h4 className="font-medium">{attendee.name}</h4>
                          <p className="text-sm text-muted-foreground">{attendee.email}</p>
                        </div>
                        <Badge variant={attendee.scannedAt ? "success" : "secondary"}>
                          {attendee.scannedAt ? "Scanned" : "Pending"}
                        </Badge>
                      </div>
                      
                      <div className="mt-2 flex flex-wrap gap-2 text-xs text-muted-foreground">
                        <span>QR: {attendee.qrCode}</span>
                        <span>•</span>
                        <span>{attendee.eventName}</span>
                        {attendee.dietaryRestrictions && (
                          <>
                            <span>•</span>
                            <span>Diet: {attendee.dietaryRestrictions}</span>
                          </>
                        )}
                        {attendee.scannedAt && (
                          <>
                            <span>•</span>
                            <span>Scanned: {formatDate(attendee.scannedAt)}</span>
                          </>
                        )}
                      </div>
                      
                      {attendee.specialNotes && (
                        <p className="mt-1 text-xs text-muted-foreground">
                          Note: {attendee.specialNotes}
                        </p>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button variant="ghost" size="sm">
                        <QrCode className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  )
}
