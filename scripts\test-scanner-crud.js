const { MongoClient } = require('mongodb');

const MONGODB_URI = "mongodb://localhost:27017";
const DB_NAME = "foodscan";

async function testScannerCRUD() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db(DB_NAME);
    const attendeesCollection = db.collection('attendees');
    const scanLogsCollection = db.collection('scan_logs');
    const eventsCollection = db.collection('events');
    const usersCollection = db.collection('users');
    
    console.log('\n📱 Testing Scanner CRUD Operations...\n');
    
    // Get existing data
    const existingEvent = await eventsCollection.findOne({});
    const existingAttendees = await attendeesCollection.find({}).toArray();
    const scannerUser = await usersCollection.findOne({ role: 'SCANNER' });
    
    if (!existingEvent || existingAttendees.length === 0 || !scannerUser) {
      console.log('❌ Missing required data. Please ensure events, attendees, and scanner user exist.');
      return;
    }
    
    console.log('📅 Using event:', existingEvent.name);
    console.log('👥 Found', existingAttendees.length, 'attendees');
    console.log('🔍 Scanner user:', scannerUser.name);
    
    // 1. Test QR Code Scanning - SUCCESS case
    console.log('\n1️⃣ Testing QR CODE SCANNING - Success Case...');
    const testAttendee = existingAttendees[0];
    console.log('   Testing with attendee:', testAttendee.name);
    console.log('   QR Code:', testAttendee.qrCode);
    
    // Create a scan log entry
    const scanLogEntry = {
      id: 'scan-' + Date.now(),
      attendeeId: testAttendee.id,
      scannedBy: scannerUser.id,
      scannedAt: new Date(),
      isSuccessful: true,
      notes: 'Test scan - successful'
    };
    
    const scanResult = await scanLogsCollection.insertOne(scanLogEntry);
    console.log('✅ Scan log created with ID:', scanResult.insertedId);
    
    // 2. Test QR Code Validation
    console.log('\n2️⃣ Testing QR CODE VALIDATION...');
    
    // Test valid QR code lookup
    const foundAttendee = await attendeesCollection.findOne({ qrCode: testAttendee.qrCode });
    if (foundAttendee) {
      console.log('✅ Valid QR code found attendee:', foundAttendee.name);
      console.log('   Event ID:', foundAttendee.eventId);
      console.log('   Dietary restrictions:', foundAttendee.dietaryRestrictions || 'None');
    }
    
    // Test invalid QR code
    const invalidQR = 'INVALID-QR-CODE-123';
    const invalidAttendee = await attendeesCollection.findOne({ qrCode: invalidQR });
    if (!invalidAttendee) {
      console.log('✅ Invalid QR code correctly rejected');
    }
    
    // 3. Test Scan History
    console.log('\n3️⃣ Testing SCAN HISTORY...');
    
    // Create multiple scan entries for testing
    const multipleScanLogs = [
      {
        id: 'scan-history-1-' + Date.now(),
        attendeeId: testAttendee.id,
        scannedBy: scannerUser.id,
        scannedAt: new Date(Date.now() - 3600000), // 1 hour ago
        isSuccessful: true,
        notes: 'First scan'
      },
      {
        id: 'scan-history-2-' + Date.now(),
        attendeeId: testAttendee.id,
        scannedBy: scannerUser.id,
        scannedAt: new Date(Date.now() - 1800000), // 30 minutes ago
        isSuccessful: true,
        notes: 'Second scan'
      }
    ];
    
    await scanLogsCollection.insertMany(multipleScanLogs);
    console.log('✅ Created multiple scan log entries');
    
    // Retrieve scan history for attendee
    const scanHistory = await scanLogsCollection
      .find({ attendeeId: testAttendee.id })
      .sort({ scannedAt: -1 })
      .toArray();
    
    console.log('✅ Found', scanHistory.length, 'scan entries for attendee');
    scanHistory.forEach((scan, index) => {
      console.log(`   ${index + 1}. ${scan.scannedAt.toLocaleString()} - ${scan.notes}`);
    });
    
    // 4. Test Scan Statistics
    console.log('\n4️⃣ Testing SCAN STATISTICS...');
    
    // Total scans
    const totalScans = await scanLogsCollection.countDocuments({});
    console.log('✅ Total scans in database:', totalScans);
    
    // Scans by scanner user
    const scansByUser = await scanLogsCollection.countDocuments({ scannedBy: scannerUser.id });
    console.log('✅ Scans by scanner user:', scansByUser);
    
    // Successful vs failed scans
    const successfulScans = await scanLogsCollection.countDocuments({ isSuccessful: true });
    const failedScans = await scanLogsCollection.countDocuments({ isSuccessful: false });
    console.log('✅ Successful scans:', successfulScans);
    console.log('✅ Failed scans:', failedScans);
    
    // Scans today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const scansToday = await scanLogsCollection.countDocuments({
      scannedAt: {
        $gte: today,
        $lt: tomorrow
      }
    });
    console.log('✅ Scans today:', scansToday);
    
    // 5. Test Attendee Check-in Status
    console.log('\n5️⃣ Testing ATTENDEE CHECK-IN STATUS...');
    
    // Get all attendees with their latest scan
    const attendeesWithScans = await attendeesCollection.aggregate([
      {
        $lookup: {
          from: 'scan_logs',
          localField: 'id',
          foreignField: 'attendeeId',
          as: 'scans'
        }
      },
      {
        $addFields: {
          lastScan: { $max: '$scans.scannedAt' },
          totalScans: { $size: '$scans' },
          isCheckedIn: { $gt: [{ $size: '$scans' }, 0] }
        }
      }
    ]).toArray();
    
    console.log('✅ Attendee check-in status:');
    attendeesWithScans.forEach(attendee => {
      const status = attendee.isCheckedIn ? '✅ Checked In' : '❌ Not Checked In';
      const lastScan = attendee.lastScan ? attendee.lastScan.toLocaleString() : 'Never';
      console.log(`   ${attendee.name}: ${status} (${attendee.totalScans} scans, last: ${lastScan})`);
    });
    
    // 6. Test QR Code Uniqueness
    console.log('\n6️⃣ Testing QR CODE UNIQUENESS...');
    
    const qrCodes = await attendeesCollection.distinct('qrCode');
    const totalAttendees = await attendeesCollection.countDocuments({});
    
    if (qrCodes.length === totalAttendees) {
      console.log('✅ All QR codes are unique');
      console.log('   Total attendees:', totalAttendees);
      console.log('   Unique QR codes:', qrCodes.length);
    } else {
      console.log('❌ Duplicate QR codes found!');
      console.log('   Total attendees:', totalAttendees);
      console.log('   Unique QR codes:', qrCodes.length);
    }
    
    // Display some QR codes for testing
    console.log('\n📋 Sample QR codes for testing:');
    existingAttendees.slice(0, 3).forEach((attendee, index) => {
      console.log(`   ${index + 1}. ${attendee.name}: ${attendee.qrCode}`);
    });
    
    // 7. Cleanup test data
    console.log('\n🧹 Cleaning up test data...');
    const cleanupResult = await scanLogsCollection.deleteMany({
      id: { $regex: /^(scan-|scan-history-)/ }
    });
    console.log('✅ Cleaned up', cleanupResult.deletedCount, 'test scan logs');
    
    console.log('\n🎉 Scanner CRUD testing completed!');
    
  } catch (error) {
    console.error('❌ Error during CRUD testing:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testScannerCRUD().catch(console.error);
