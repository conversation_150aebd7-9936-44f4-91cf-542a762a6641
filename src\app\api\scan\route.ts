import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { qrCode, eventId, mealTypeId } = body

    // Find the attendee by QR code
    const attendee = await prisma.attendee.findUnique({
      where: { qrCode },
      include: {
        event: true,
        mealConsumptions: {
          include: {
            mealType: true
          }
        }
      }
    })

    if (!attendee) {
      // Log failed scan
      await prisma.scanLog.create({
        data: {
          success: false,
          errorMessage: "QR code not found",
          eventId: eventId || "unknown",
          scannerId: session.user.id,
          deviceInfo: request.headers.get('user-agent') || 'Unknown device'
        }
      })

      return NextResponse.json({ 
        success: false, 
        error: "QR code not found" 
      }, { status: 404 })
    }

    // Check if attendee belongs to the correct event
    if (eventId && attendee.eventId !== eventId) {
      await prisma.scanLog.create({
        data: {
          success: false,
          errorMessage: "QR code not valid for this event",
          attendeeId: attendee.id,
          eventId: eventId,
          scannerId: session.user.id,
          deviceInfo: request.headers.get('user-agent') || 'Unknown device'
        }
      })

      return NextResponse.json({ 
        success: false, 
        error: "QR code not valid for this event" 
      }, { status: 400 })
    }

    // Get meal type if specified
    let mealType = null
    if (mealTypeId) {
      mealType = await prisma.mealType.findUnique({
        where: { id: mealTypeId }
      })

      if (!mealType) {
        return NextResponse.json({ 
          success: false, 
          error: "Meal type not found" 
        }, { status: 404 })
      }

      // Check if attendee already consumed this meal type
      const existingConsumption = await prisma.mealConsumption.findUnique({
        where: {
          attendeeId_mealTypeId: {
            attendeeId: attendee.id,
            mealTypeId: mealTypeId
          }
        }
      })

      if (existingConsumption) {
        await prisma.scanLog.create({
          data: {
            success: false,
            errorMessage: "Meal already consumed",
            attendeeId: attendee.id,
            eventId: attendee.eventId,
            scannerId: session.user.id,
            deviceInfo: request.headers.get('user-agent') || 'Unknown device'
          }
        })

        return NextResponse.json({ 
          success: false, 
          error: "Meal already consumed" 
        }, { status: 400 })
      }

      // Check meal availability
      if (mealType.totalQuantity && mealType.consumedQuantity >= mealType.totalQuantity) {
        await prisma.scanLog.create({
          data: {
            success: false,
            errorMessage: "Meal not available",
            attendeeId: attendee.id,
            eventId: attendee.eventId,
            scannerId: session.user.id,
            deviceInfo: request.headers.get('user-agent') || 'Unknown device'
          }
        })

        return NextResponse.json({ 
          success: false, 
          error: "Meal not available" 
        }, { status: 400 })
      }
    }

    // Create meal consumption record if meal type specified
    if (mealType) {
      await prisma.mealConsumption.create({
        data: {
          attendeeId: attendee.id,
          mealTypeId: mealType.id,
          quantity: 1
        }
      })

      // Update meal type consumed quantity
      await prisma.mealType.update({
        where: { id: mealType.id },
        data: {
          consumedQuantity: {
            increment: 1
          }
        }
      })
    }

    // Update attendee scan timestamp
    await prisma.attendee.update({
      where: { id: attendee.id },
      data: {
        scannedAt: new Date()
      }
    })

    // Log successful scan
    await prisma.scanLog.create({
      data: {
        success: true,
        attendeeId: attendee.id,
        eventId: attendee.eventId,
        scannerId: session.user.id,
        deviceInfo: request.headers.get('user-agent') || 'Unknown device'
      }
    })

    return NextResponse.json({
      success: true,
      attendee: {
        id: attendee.id,
        name: attendee.name,
        email: attendee.email,
        eventName: attendee.event.name,
        dietaryRestrictions: attendee.dietaryRestrictions,
        specialNotes: attendee.specialNotes,
        mealType: mealType?.name
      }
    })

  } catch (error) {
    console.error("Error processing scan:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
