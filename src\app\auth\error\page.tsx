"use client"

import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { AlertCircle, Home, RefreshCw } from "lucide-react"

const errorMessages: Record<string, { title: string; description: string }> = {
  Configuration: {
    title: "Server Configuration Error",
    description: "There is a problem with the server configuration. Please contact the administrator."
  },
  AccessDenied: {
    title: "Access Denied",
    description: "You do not have permission to sign in with this account."
  },
  Verification: {
    title: "Verification Error",
    description: "The verification token has expired or has already been used."
  },
  Default: {
    title: "Authentication Error",
    description: "An error occurred during authentication. Please try again."
  },
  Signin: {
    title: "Sign In Error",
    description: "There was a problem signing you in. Please check your credentials and try again."
  },
  OAuthSignin: {
    title: "OAuth Sign In Error",
    description: "There was a problem with the OAuth provider. Please try again."
  },
  OAuthCallback: {
    title: "OAuth Callback Error",
    description: "There was a problem processing the OAuth callback."
  },
  OAuthCreateAccount: {
    title: "OAuth Account Creation Error",
    description: "Could not create an account with the OAuth provider."
  },
  EmailCreateAccount: {
    title: "Email Account Creation Error",
    description: "Could not create an account with the provided email."
  },
  Callback: {
    title: "Callback Error",
    description: "There was a problem with the authentication callback."
  },
  OAuthAccountNotLinked: {
    title: "Account Not Linked",
    description: "This account is not linked to your profile. Please sign in with your original method."
  },
  EmailSignin: {
    title: "Email Sign In Error",
    description: "There was a problem sending the sign in email."
  },
  CredentialsSignin: {
    title: "Invalid Credentials",
    description: "The email or password you entered is incorrect. Please check your credentials and try again."
  },
  SessionRequired: {
    title: "Session Required",
    description: "You must be signed in to access this page."
  }
}

export default function AuthErrorPage() {
  const searchParams = useSearchParams()
  const error = searchParams.get("error") || "Default"
  
  const errorInfo = errorMessages[error] || errorMessages.Default

  const handleRetry = () => {
    window.location.href = "/auth/signin"
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        <Card>
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <AlertCircle className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl text-red-900">{errorInfo.title}</CardTitle>
            <CardDescription className="text-red-700">
              {errorInfo.description}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Error Details */}
            {error !== "Default" && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-800">
                  <strong>Error Code:</strong> {error}
                </p>
              </div>
            )}

            {/* Troubleshooting Tips */}
            <div className="bg-gray-50 border border-gray-200 rounded-md p-3">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Troubleshooting Tips:</h4>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• Check your internet connection</li>
                <li>• Verify your email and password are correct</li>
                <li>• Clear your browser cache and cookies</li>
                <li>• Try using a different browser</li>
                {error === "CredentialsSignin" && (
                  <li>• Make sure your account exists and is active</li>
                )}
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col space-y-2">
              <Button onClick={handleRetry} className="w-full">
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
              <Button asChild variant="outline" className="w-full">
                <Link href="/dashboard">
                  <Home className="mr-2 h-4 w-4" />
                  Go to Dashboard
                </Link>
              </Button>
            </div>

            {/* Support Link */}
            <div className="text-center text-sm text-gray-600">
              <p>
                Still having trouble?{" "}
                <Link href="/settings" className="text-blue-600 hover:text-blue-800">
                  Contact Support
                </Link>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
