const { MongoClient, ObjectId } = require('mongodb');

const url = 'mongodb://localhost:27017';
const dbName = 'foodscan';

async function testEventUpdate() {
  const client = new MongoClient(url);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db(dbName);
    const eventsCollection = db.collection('events');
    
    // Find an existing event to test update
    const existingEvent = await eventsCollection.findOne({});
    
    if (!existingEvent) {
      console.log('❌ No events found in database');
      return;
    }
    
    console.log('✅ Found event to test:', existingEvent.name);
    console.log('Event ID:', existingEvent._id.toString());
    
    // Test the update data structure that would be sent from the frontend
    const updateData = {
      name: existingEvent.name + ' (Updated)',
      description: existingEvent.description || 'Updated description',
      startDate: new Date('2024-03-15T08:00:00Z').toISOString(),
      endDate: new Date('2024-03-15T18:00:00Z').toISOString(),
      serviceStartTime: new Date('1970-01-01T12:00:00Z').toISOString(),
      serviceEndTime: new Date('1970-01-01T14:00:00Z').toISOString(),
      primaryColor: '#ff6b6b',
      secondaryColor: '#ee5a52',
      logoUrl: existingEvent.logoUrl || '',
      isActive: true,
      mealTypes: [
        {
          name: 'Updated Main Course',
          description: 'Updated main course description',
          maxQuantityPerAttendee: 1,
          totalQuantity: 100,
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false
        },
        {
          name: 'Updated Vegetarian Option',
          description: 'Updated vegetarian option',
          maxQuantityPerAttendee: 1,
          totalQuantity: 50,
          isVegetarian: true,
          isVegan: false,
          isGlutenFree: true
        }
      ]
    };
    
    console.log('\n📝 Testing update with data:');
    console.log('- Name:', updateData.name);
    console.log('- Start Date:', updateData.startDate);
    console.log('- Service Start Time:', updateData.serviceStartTime);
    console.log('- Meal Types:', updateData.mealTypes.length);
    
    // Test date parsing
    console.log('\n🕐 Testing date parsing:');
    try {
      const startDate = new Date(updateData.startDate);
      const endDate = new Date(updateData.endDate);
      const serviceStartTime = new Date(updateData.serviceStartTime);
      const serviceEndTime = new Date(updateData.serviceEndTime);
      
      console.log('✅ Start Date parsed:', startDate.toISOString());
      console.log('✅ End Date parsed:', endDate.toISOString());
      console.log('✅ Service Start Time parsed:', serviceStartTime.toISOString());
      console.log('✅ Service End Time parsed:', serviceEndTime.toISOString());
      
      if (isNaN(startDate.getTime())) {
        console.log('❌ Invalid start date');
        return;
      }
      if (isNaN(endDate.getTime())) {
        console.log('❌ Invalid end date');
        return;
      }
      if (isNaN(serviceStartTime.getTime())) {
        console.log('❌ Invalid service start time');
        return;
      }
      if (isNaN(serviceEndTime.getTime())) {
        console.log('❌ Invalid service end time');
        return;
      }
      
    } catch (error) {
      console.log('❌ Date parsing error:', error.message);
      return;
    }
    
    // Test meal types validation
    console.log('\n🍽️ Testing meal types validation:');
    for (const meal of updateData.mealTypes) {
      if (!meal.name || typeof meal.name !== 'string') {
        console.log('❌ Invalid meal type:', meal);
        return;
      }
      console.log('✅ Valid meal type:', meal.name);
    }
    
    console.log('\n✅ All validation tests passed!');
    console.log('📋 Update data structure is valid');
    
    // Test actual MongoDB update (without Prisma)
    console.log('\n🔄 Testing direct MongoDB update...');
    
    const result = await eventsCollection.updateOne(
      { _id: existingEvent._id },
      {
        $set: {
          name: updateData.name,
          description: updateData.description,
          startDate: new Date(updateData.startDate),
          endDate: new Date(updateData.endDate),
          serviceStartTime: new Date(updateData.serviceStartTime),
          serviceEndTime: new Date(updateData.serviceEndTime),
          primaryColor: updateData.primaryColor,
          secondaryColor: updateData.secondaryColor,
          logoUrl: updateData.logoUrl,
          isActive: updateData.isActive,
          updatedAt: new Date()
        }
      }
    );
    
    if (result.modifiedCount > 0) {
      console.log('✅ Direct MongoDB update successful');
    } else {
      console.log('❌ Direct MongoDB update failed');
    }
    
    // Verify the update
    const updatedEvent = await eventsCollection.findOne({ _id: existingEvent._id });
    console.log('📋 Updated event name:', updatedEvent.name);
    
  } catch (error) {
    console.error('❌ Error during test:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testEventUpdate().catch(console.error);
