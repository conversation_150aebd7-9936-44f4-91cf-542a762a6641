"use client"

import { useEffect, useRef, useState } from "react"

// Accessibility utilities
export class AccessibilityManager {
  private static instance: AccessibilityManager
  private announcements: string[] = []
  private focusHistory: HTMLElement[] = []

  static getInstance(): AccessibilityManager {
    if (!AccessibilityManager.instance) {
      AccessibilityManager.instance = new AccessibilityManager()
    }
    return AccessibilityManager.instance
  }

  constructor() {
    if (typeof window !== 'undefined') {
      this.setupScreenReaderSupport()
      this.setupKeyboardNavigation()
    }
  }

  private setupScreenReaderSupport() {
    // Create live region for announcements
    const liveRegion = document.createElement('div')
    liveRegion.setAttribute('aria-live', 'polite')
    liveRegion.setAttribute('aria-atomic', 'true')
    liveRegion.className = 'sr-only'
    liveRegion.id = 'live-region'
    document.body.appendChild(liveRegion)
  }

  private setupKeyboardNavigation() {
    // Skip to main content link
    const skipLink = document.createElement('a')
    skipLink.href = '#main-content'
    skipLink.textContent = 'Skip to main content'
    skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-50 focus:px-4 focus:py-2 focus:bg-primary focus:text-primary-foreground focus:rounded'
    document.body.insertBefore(skipLink, document.body.firstChild)

    // Keyboard navigation handler
    document.addEventListener('keydown', this.handleKeyboardNavigation.bind(this))
  }

  private handleKeyboardNavigation(event: KeyboardEvent) {
    // Escape key handling
    if (event.key === 'Escape') {
      this.handleEscapeKey()
    }

    // Tab trapping for modals
    if (event.key === 'Tab') {
      this.handleTabKey(event)
    }

    // Arrow key navigation for custom components
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
      this.handleArrowKeys(event)
    }
  }

  private handleEscapeKey() {
    // Close modals, dropdowns, etc.
    const activeModal = document.querySelector('[role="dialog"][aria-hidden="false"]')
    if (activeModal) {
      const closeButton = activeModal.querySelector('[aria-label="Close"]') as HTMLElement
      closeButton?.click()
    }
  }

  private handleTabKey(event: KeyboardEvent) {
    const activeModal = document.querySelector('[role="dialog"][aria-hidden="false"]')
    if (activeModal) {
      this.trapFocus(event, activeModal as HTMLElement)
    }
  }

  private handleArrowKeys(event: KeyboardEvent) {
    const target = event.target as HTMLElement
    const role = target.getAttribute('role')

    if (role === 'menuitem' || role === 'option') {
      event.preventDefault()
      this.navigateMenu(event, target)
    }
  }

  private trapFocus(event: KeyboardEvent, container: HTMLElement) {
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>

    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]

    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        event.preventDefault()
        lastElement.focus()
      }
    } else {
      if (document.activeElement === lastElement) {
        event.preventDefault()
        firstElement.focus()
      }
    }
  }

  private navigateMenu(event: KeyboardEvent, target: HTMLElement) {
    const container = target.closest('[role="menu"], [role="listbox"]')
    if (!container) return

    const items = container.querySelectorAll('[role="menuitem"], [role="option"]') as NodeListOf<HTMLElement>
    const currentIndex = Array.from(items).indexOf(target)

    let nextIndex = currentIndex
    switch (event.key) {
      case 'ArrowDown':
        nextIndex = (currentIndex + 1) % items.length
        break
      case 'ArrowUp':
        nextIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1
        break
      case 'Home':
        nextIndex = 0
        break
      case 'End':
        nextIndex = items.length - 1
        break
    }

    if (nextIndex !== currentIndex) {
      items[nextIndex].focus()
    }
  }

  // Screen reader announcements
  announce(message: string, priority: 'polite' | 'assertive' = 'polite') {
    const liveRegion = document.getElementById('live-region')
    if (liveRegion) {
      liveRegion.setAttribute('aria-live', priority)
      liveRegion.textContent = message
      
      // Clear after announcement
      setTimeout(() => {
        liveRegion.textContent = ''
      }, 1000)
    }

    this.announcements.push(message)
    if (this.announcements.length > 10) {
      this.announcements.shift()
    }
  }

  // Focus management
  saveFocus() {
    const activeElement = document.activeElement as HTMLElement
    if (activeElement) {
      this.focusHistory.push(activeElement)
    }
  }

  restoreFocus() {
    const lastFocused = this.focusHistory.pop()
    if (lastFocused && document.contains(lastFocused)) {
      lastFocused.focus()
    }
  }

  // Color contrast checker
  checkColorContrast(foreground: string, background: string): {
    ratio: number
    wcagAA: boolean
    wcagAAA: boolean
  } {
    const getLuminance = (color: string): number => {
      // Convert hex to RGB
      const hex = color.replace('#', '')
      const r = parseInt(hex.substr(0, 2), 16) / 255
      const g = parseInt(hex.substr(2, 2), 16) / 255
      const b = parseInt(hex.substr(4, 2), 16) / 255

      // Calculate relative luminance
      const sRGB = [r, g, b].map(c => {
        return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4)
      })

      return 0.2126 * sRGB[0] + 0.7152 * sRGB[1] + 0.0722 * sRGB[2]
    }

    const l1 = getLuminance(foreground)
    const l2 = getLuminance(background)
    const ratio = (Math.max(l1, l2) + 0.05) / (Math.min(l1, l2) + 0.05)

    return {
      ratio,
      wcagAA: ratio >= 4.5,
      wcagAAA: ratio >= 7
    }
  }
}

// React hooks for accessibility
export function useAnnouncer() {
  const manager = AccessibilityManager.getInstance()

  return {
    announce: (message: string, priority?: 'polite' | 'assertive') => {
      manager.announce(message, priority)
    }
  }
}

export function useFocusManagement() {
  const manager = AccessibilityManager.getInstance()

  return {
    saveFocus: () => manager.saveFocus(),
    restoreFocus: () => manager.restoreFocus()
  }
}

export function useKeyboardNavigation(
  onEscape?: () => void,
  onEnter?: () => void,
  onArrowKeys?: (direction: 'up' | 'down' | 'left' | 'right') => void
) {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'Escape':
          onEscape?.()
          break
        case 'Enter':
          onEnter?.()
          break
        case 'ArrowUp':
          onArrowKeys?.('up')
          break
        case 'ArrowDown':
          onArrowKeys?.('down')
          break
        case 'ArrowLeft':
          onArrowKeys?.('left')
          break
        case 'ArrowRight':
          onArrowKeys?.('right')
          break
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [onEscape, onEnter, onArrowKeys])
}

export function useAriaLiveRegion() {
  const [message, setMessage] = useState('')
  const regionRef = useRef<HTMLDivElement>(null)

  const announce = (text: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (regionRef.current) {
      regionRef.current.setAttribute('aria-live', priority)
      setMessage(text)
      
      // Clear message after announcement
      setTimeout(() => setMessage(''), 1000)
    }
  }

  return {
    regionRef,
    message,
    announce
  }
}

// Accessibility testing utilities
export function runAccessibilityAudit(): {
  issues: Array<{
    type: string
    element: string
    message: string
    severity: 'error' | 'warning' | 'info'
  }>
  score: number
} {
  const issues: Array<{
    type: string
    element: string
    message: string
    severity: 'error' | 'warning' | 'info'
  }> = []

  // Check for missing alt text
  const images = document.querySelectorAll('img')
  images.forEach((img, index) => {
    if (!img.alt && !img.getAttribute('aria-label')) {
      issues.push({
        type: 'missing-alt-text',
        element: `img[${index}]`,
        message: 'Image missing alt text',
        severity: 'error'
      })
    }
  })

  // Check for missing form labels
  const inputs = document.querySelectorAll('input, select, textarea')
  inputs.forEach((input, index) => {
    const id = input.id
    const hasLabel = id && document.querySelector(`label[for="${id}"]`)
    const hasAriaLabel = input.getAttribute('aria-label')
    const hasAriaLabelledBy = input.getAttribute('aria-labelledby')

    if (!hasLabel && !hasAriaLabel && !hasAriaLabelledBy) {
      issues.push({
        type: 'missing-label',
        element: `${input.tagName.toLowerCase()}[${index}]`,
        message: 'Form control missing label',
        severity: 'error'
      })
    }
  })

  // Check for missing heading hierarchy
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
  let lastLevel = 0
  headings.forEach((heading, index) => {
    const level = parseInt(heading.tagName.charAt(1))
    if (level > lastLevel + 1) {
      issues.push({
        type: 'heading-hierarchy',
        element: `${heading.tagName.toLowerCase()}[${index}]`,
        message: 'Heading level skipped',
        severity: 'warning'
      })
    }
    lastLevel = level
  })

  // Check for missing focus indicators
  const focusableElements = document.querySelectorAll(
    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
  )
  focusableElements.forEach((element, index) => {
    const styles = window.getComputedStyle(element, ':focus')
    if (styles.outline === 'none' && !styles.boxShadow && !styles.border) {
      issues.push({
        type: 'missing-focus-indicator',
        element: `${element.tagName.toLowerCase()}[${index}]`,
        message: 'Element missing focus indicator',
        severity: 'warning'
      })
    }
  })

  // Calculate score
  const errorCount = issues.filter(i => i.severity === 'error').length
  const warningCount = issues.filter(i => i.severity === 'warning').length
  const totalElements = document.querySelectorAll('*').length
  const score = Math.max(0, 100 - (errorCount * 10) - (warningCount * 5))

  return { issues, score }
}

// ARIA utilities
export const AriaUtils = {
  // Generate unique IDs for ARIA relationships
  generateId: (prefix: string = 'aria'): string => {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  },

  // Set up ARIA relationships
  linkElements: (trigger: HTMLElement, target: HTMLElement, relationship: string) => {
    const id = target.id || AriaUtils.generateId()
    target.id = id
    trigger.setAttribute(relationship, id)
  },

  // Update ARIA states
  updateState: (element: HTMLElement, state: string, value: string | boolean) => {
    element.setAttribute(`aria-${state}`, value.toString())
  },

  // Manage ARIA expanded state
  toggleExpanded: (element: HTMLElement) => {
    const expanded = element.getAttribute('aria-expanded') === 'true'
    element.setAttribute('aria-expanded', (!expanded).toString())
    return !expanded
  }
}
