const { MongoClient, ObjectId } = require('mongodb');

const url = 'mongodb://localhost:27017';
const dbName = 'foodscan';

async function testDuplicationFlow() {
  const client = new MongoClient(url);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db(dbName);
    const eventsCollection = db.collection('events');
    const mealTypesCollection = db.collection('meal_types');
    
    // Find an existing event to duplicate
    const originalEvent = await eventsCollection.findOne({});
    
    if (!originalEvent) {
      console.log('❌ No events found in database');
      return;
    }
    
    console.log('✅ Found event to duplicate:', originalEvent.name);
    console.log('Original Event ID:', originalEvent._id.toString());
    
    // Get original meal types
    const originalMealTypes = await mealTypesCollection.find({ 
      eventId: originalEvent._id.toString() 
    }).toArray();
    
    console.log('Original Meal Types:', originalMealTypes.length);
    
    // Simulate the duplication process
    console.log('\n🔄 Simulating duplication process...');
    
    // 1. Create duplicated event data (what handleDuplicateEvent would create)
    const duplicatedEventData = {
      name: `${originalEvent.name} (Copy)`,
      description: originalEvent.description,
      startDate: originalEvent.startDate.toISOString(),
      endDate: originalEvent.endDate.toISOString(),
      serviceStartTime: originalEvent.serviceStartTime ? originalEvent.serviceStartTime.toISOString() : null,
      serviceEndTime: originalEvent.serviceEndTime ? originalEvent.serviceEndTime.toISOString() : null,
      primaryColor: originalEvent.primaryColor,
      secondaryColor: originalEvent.secondaryColor,
      logoUrl: originalEvent.logoUrl,
      isActive: originalEvent.isActive,
      mealTypes: originalMealTypes.map(mt => ({
        name: mt.name,
        description: mt.description,
        maxQuantityPerAttendee: mt.maxQuantityPerAttendee,
        totalQuantity: mt.totalQuantity,
        isVegetarian: mt.isVegetarian,
        isVegan: mt.isVegan,
        isGlutenFree: mt.isGlutenFree
      }))
    };
    
    console.log('📋 Duplicated event data prepared:');
    console.log('- Name:', duplicatedEventData.name);
    console.log('- Has ID:', !!duplicatedEventData.id, '(should be false)');
    console.log('- Meal Types:', duplicatedEventData.mealTypes.length);
    
    // 2. Simulate the API POST request (what would happen when form is submitted)
    console.log('\n📡 Simulating API POST request...');
    
    // Create new event
    const newEventId = new ObjectId();
    const newEvent = {
      _id: newEventId,
      name: duplicatedEventData.name,
      description: duplicatedEventData.description,
      startDate: new Date(duplicatedEventData.startDate),
      endDate: new Date(duplicatedEventData.endDate),
      serviceStartTime: duplicatedEventData.serviceStartTime ? new Date(duplicatedEventData.serviceStartTime) : null,
      serviceEndTime: duplicatedEventData.serviceEndTime ? new Date(duplicatedEventData.serviceEndTime) : null,
      primaryColor: duplicatedEventData.primaryColor,
      secondaryColor: duplicatedEventData.secondaryColor,
      logoUrl: duplicatedEventData.logoUrl,
      isActive: duplicatedEventData.isActive,
      creatorId: 'test-user-id',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    // Insert the new event
    await eventsCollection.insertOne(newEvent);
    console.log('✅ New event created with ID:', newEventId.toString());
    
    // Create new meal types
    if (duplicatedEventData.mealTypes.length > 0) {
      const newMealTypes = duplicatedEventData.mealTypes.map(meal => ({
        _id: new ObjectId(),
        name: meal.name,
        description: meal.description,
        maxQuantityPerAttendee: meal.maxQuantityPerAttendee,
        totalQuantity: meal.totalQuantity,
        consumedQuantity: 0,
        isVegetarian: meal.isVegetarian,
        isVegan: meal.isVegan,
        isGlutenFree: meal.isGlutenFree,
        isAvailable: true,
        eventId: newEventId.toString(),
        createdAt: new Date(),
        updatedAt: new Date()
      }));
      
      await mealTypesCollection.insertMany(newMealTypes);
      console.log('✅ Created', newMealTypes.length, 'new meal types');
    }
    
    // 3. Verify the duplication
    console.log('\n🔍 Verifying duplication...');
    
    const createdEvent = await eventsCollection.findOne({ _id: newEventId });
    const createdMealTypes = await mealTypesCollection.find({ 
      eventId: newEventId.toString() 
    }).toArray();
    
    console.log('📋 Verification Results:');
    console.log('- Original Event Name:', originalEvent.name);
    console.log('- Duplicated Event Name:', createdEvent.name);
    console.log('- Original Meal Types:', originalMealTypes.length);
    console.log('- Duplicated Meal Types:', createdMealTypes.length);
    
    // Check that IDs are different
    console.log('- Different Event IDs:', originalEvent._id.toString() !== createdEvent._id.toString());
    
    // Check meal type names match
    const originalMealNames = originalMealTypes.map(mt => mt.name).sort();
    const duplicatedMealNames = createdMealTypes.map(mt => mt.name).sort();
    const mealNamesMatch = JSON.stringify(originalMealNames) === JSON.stringify(duplicatedMealNames);
    console.log('- Meal Type Names Match:', mealNamesMatch);
    
    // Check that meal type IDs are different
    const originalMealIds = originalMealTypes.map(mt => mt._id.toString()).sort();
    const duplicatedMealIds = createdMealTypes.map(mt => mt._id.toString()).sort();
    const mealIdsAreDifferent = JSON.stringify(originalMealIds) !== JSON.stringify(duplicatedMealIds);
    console.log('- Different Meal Type IDs:', mealIdsAreDifferent);
    
    // Validation
    const validationTests = [
      {
        name: 'Event name has (Copy) suffix',
        condition: createdEvent.name.includes('(Copy)'),
        expected: true
      },
      {
        name: 'Different event IDs',
        condition: originalEvent._id.toString() !== createdEvent._id.toString(),
        expected: true
      },
      {
        name: 'Same number of meal types',
        condition: originalMealTypes.length === createdMealTypes.length,
        expected: true
      },
      {
        name: 'Meal type names match',
        condition: mealNamesMatch,
        expected: true
      },
      {
        name: 'Different meal type IDs',
        condition: mealIdsAreDifferent,
        expected: true
      },
      {
        name: 'Event is active',
        condition: createdEvent.isActive,
        expected: true
      }
    ];
    
    console.log('\n✅ Validation Results:');
    validationTests.forEach(test => {
      const passed = test.condition === test.expected;
      console.log(`   ${passed ? '✅' : '❌'} ${test.name}: ${test.condition}`);
    });
    
    const allPassed = validationTests.every(test => test.condition === test.expected);
    console.log(`\n${allPassed ? '🎉' : '❌'} Overall Result: ${allPassed ? 'Duplication flow works correctly!' : 'Some issues found!'}`);
    
    // Cleanup - remove the test event
    console.log('\n🧹 Cleaning up test data...');
    await eventsCollection.deleteOne({ _id: newEventId });
    await mealTypesCollection.deleteMany({ eventId: newEventId.toString() });
    console.log('✅ Test data cleaned up');
    
  } catch (error) {
    console.error('❌ Error during duplication test:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testDuplicationFlow().catch(console.error);
