// Test QR Scanner library functionality
async function testQRLibrary() {
  console.log('🔍 Testing QR Scanner Library...\n');
  
  try {
    // Test 1: Check if we can import the library
    console.log('1️⃣ Testing library import...');
    const QrScanner = (await import('qr-scanner')).default;
    console.log('✅ QR Scanner library imported successfully');
    
    // Test 2: Check camera listing
    console.log('\n2️⃣ Testing camera detection...');
    try {
      const cameras = await QrScanner.listCameras(true);
      console.log(`✅ Found ${cameras.length} cameras:`);
      cameras.forEach((camera, index) => {
        console.log(`   ${index + 1}. ${camera.label || 'Unknown Camera'} (${camera.id})`);
      });
    } catch (error) {
      console.log('❌ Camera detection failed:', error.message);
    }
    
    // Test 3: Check if we can create a scanner instance (without starting)
    console.log('\n3️⃣ Testing scanner creation...');
    try {
      // Create a dummy video element
      const video = document.createElement('video');
      const scanner = new QrScanner(
        video,
        (result) => console.log('QR detected:', result.data),
        {
          onDecodeError: () => {}, // Silent
          highlightScanRegion: true,
          preferredCamera: 'environment'
        }
      );
      console.log('✅ Scanner instance created successfully');
      
      // Clean up
      scanner.destroy();
    } catch (error) {
      console.log('❌ Scanner creation failed:', error.message);
    }
    
    // Test 4: Test image scanning capability
    console.log('\n4️⃣ Testing image scanning capability...');
    try {
      // Create a simple test canvas with text (not a real QR code, just testing the function)
      const canvas = document.createElement('canvas');
      canvas.width = 100;
      canvas.height = 100;
      const ctx = canvas.getContext('2d');
      ctx.fillStyle = 'white';
      ctx.fillRect(0, 0, 100, 100);
      ctx.fillStyle = 'black';
      ctx.fillText('TEST', 10, 50);
      
      // Convert to blob
      canvas.toBlob(async (blob) => {
        try {
          await QrScanner.scanImage(blob);
          console.log('✅ Image scanning function works (no QR found as expected)');
        } catch (error) {
          if (error.message.includes('No QR code found')) {
            console.log('✅ Image scanning function works (correctly detected no QR code)');
          } else {
            console.log('❌ Image scanning failed:', error.message);
          }
        }
      });
    } catch (error) {
      console.log('❌ Image scanning test failed:', error.message);
    }
    
    console.log('\n🎉 QR Scanner library testing completed!');
    
  } catch (error) {
    console.error('❌ Critical error testing QR library:', error);
  }
}

// Run the test when DOM is ready
if (typeof window !== 'undefined') {
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', testQRLibrary);
  } else {
    testQRLibrary();
  }
} else {
  console.log('❌ This test must be run in a browser environment');
}
