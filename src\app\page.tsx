import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Logo/Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
            FoodScan
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Multi-Event Food Management System
          </p>
        </div>

        {/* Login Card */}
        <Card>
          <CardHeader className="text-center">
            <CardTitle>Welcome Back</CardTitle>
            <CardDescription>
              Choose your login type to continue
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Link href="/auth/signin" className="block">
              <Button className="w-full" size="lg">
                Admin Login
              </Button>
            </Link>

            <Link href="/auth/signin?role=scanner" className="block">
              <Button variant="outline" className="w-full" size="lg">
                Scanner Login
              </Button>
            </Link>

            <div className="text-center text-sm text-gray-500 dark:text-gray-400 mt-6">
              <p className="font-medium mb-2">Demo Credentials:</p>
              <div className="space-y-1 text-xs">
                <p><strong>Admin:</strong> <EMAIL> / admin123</p>
                <p><strong>Scanner:</strong> <EMAIL> / scanner123</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Features */}
        <div className="grid grid-cols-2 gap-4 text-center">
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
            <div className="text-2xl mb-2">📱</div>
            <h3 className="font-semibold text-sm">QR Scanning</h3>
            <p className="text-xs text-gray-500">Real-time scanning</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
            <div className="text-2xl mb-2">📊</div>
            <h3 className="font-semibold text-sm">Analytics</h3>
            <p className="text-xs text-gray-500">Live dashboard</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
            <div className="text-2xl mb-2">🔄</div>
            <h3 className="font-semibold text-sm">Offline Mode</h3>
            <p className="text-xs text-gray-500">Works offline</p>
          </div>
          <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
            <div className="text-2xl mb-2">📤</div>
            <h3 className="font-semibold text-sm">Export</h3>
            <p className="text-xs text-gray-500">Multiple formats</p>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-xs text-gray-500 dark:text-gray-400">
          <p>Built with Next.js, TypeScript & Tailwind CSS</p>
        </div>
      </div>
    </div>
  )
}
