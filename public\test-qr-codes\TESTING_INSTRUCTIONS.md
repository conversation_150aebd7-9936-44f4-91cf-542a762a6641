
# QR Scanner Testing Instructions

## Generated Test Files
The following QR code images have been generated in /public/test-qr-codes/:

### Valid Attendee QR Codes:
- JD-2024-001.png (<PERSON>)
- JS-2024-002.png (<PERSON>)
- MJ-2024-003.png (<PERSON>)

### Test Format QR Codes:
- test-standard-format.png
- test-url-format.png  
- test-json-format.png
- test-vcard-format.png

### Test Size QR Codes:
- test-small-100px.png
- test-medium-200px.png
- test-large-400px.png
- test-extra-large-600px.png

### Error Correction Level QR Codes:
- test-error-level-L.png (Low)
- test-error-level-M.png (Medium)
- test-error-level-Q.png (Quartile)
- test-error-level-H.png (High)

## Testing Steps:

1. **Camera Access Testing:**
   - Open /scanner page in different browsers
   - Test on desktop and mobile devices
   - Verify camera permission prompts work correctly

2. **QR Code Scanning Testing:**
   - Use the generated QR code images above
   - Test scanning from screen and printed copies
   - Verify different sizes and formats work
   - Test in various lighting conditions

3. **Mobile Compatibility Testing:**
   - Test on iOS Safari, Chrome on Android
   - Verify camera switching works (front/back)
   - Test device orientation changes
   - Verify touch controls work properly

4. **Error Handling Testing:**
   - Deny camera permissions and verify fallback
   - Test with invalid QR codes
   - Test image upload functionality
   - Verify error messages are user-friendly

5. **Performance Testing:**
   - Measure scan speed and accuracy
   - Test with multiple QR codes quickly
   - Verify no memory leaks during extended use

## Expected Results:
- Valid attendee QR codes should return attendee information
- Invalid QR codes should show appropriate error messages
- Camera should initialize within 5-10 seconds
- QR code detection should be near-instantaneous
- All fallback mechanisms should work smoothly
