// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

// User model with role-based access control
model User {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  email       String   @unique
  name        String?
  password    String
  role        String   @default("SCANNER")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lastLoginAt DateTime?

  // Relations
  createdEvents Event[] @relation("EventCreator")
  auditLogs     AuditLog[]
  scanLogs      ScanLog[]
  preferences   UserPreferences?

  @@map("users")
}

// SQLite doesn't support enums, so we'll use String with validation in the app

// Event model for multi-tenant architecture
model Event {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  startDate   DateTime
  endDate     DateTime
  isActive    Boolean   @default(true)

  // Branding and customization
  logoUrl     String?
  primaryColor String?
  secondaryColor String?

  // Service time windows
  serviceStartTime DateTime?
  serviceEndTime   DateTime?

  // Relations
  creatorId   String @db.ObjectId
  creator     User       @relation("EventCreator", fields: [creatorId], references: [id])
  attendees   Attendee[]
  mealTypes   MealType[]
  scanLogs    ScanLog[]
  auditLogs   AuditLog[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("events")
}

// Attendee model with QR code support
model Attendee {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  email       String?
  qrCode      String  @unique
  scannedAt   DateTime?

  // Dietary restrictions and notes
  dietaryRestrictions String?
  specialNotes        String?

  // Relations
  eventId     String @db.ObjectId
  event       Event        @relation(fields: [eventId], references: [id], onDelete: Cascade)
  mealConsumptions MealConsumption[]
  scanLogs    ScanLog[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("attendees")
}

// Meal type configuration per event
model MealType {
  id          String @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?

  // Quantity limits per attendee
  maxQuantityPerAttendee Int @default(1)

  // Availability settings
  isAvailable Boolean @default(true)
  availableFrom DateTime?
  availableTo   DateTime?

  // Dietary flags
  isVegetarian Boolean @default(false)
  isVegan      Boolean @default(false)
  isGlutenFree Boolean @default(false)

  // Inventory tracking
  totalQuantity     Int?
  consumedQuantity  Int @default(0)

  // Relations
  eventId     String @db.ObjectId
  event       Event        @relation(fields: [eventId], references: [id], onDelete: Cascade)
  mealConsumptions MealConsumption[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("meal_types")
}

// Meal consumption tracking
model MealConsumption {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  quantity  Int      @default(1)

  // Relations
  attendeeId String @db.ObjectId
  attendee   Attendee @relation(fields: [attendeeId], references: [id], onDelete: Cascade)

  mealTypeId String @db.ObjectId
  mealType   MealType @relation(fields: [mealTypeId], references: [id], onDelete: Cascade)

  consumedAt DateTime @default(now())

  @@unique([attendeeId, mealTypeId])
  @@map("meal_consumptions")
}

// Scan log for tracking all QR code scans
model ScanLog {
  id        String    @id @default(auto()) @map("_id") @db.ObjectId
  success   Boolean
  errorMessage String?

  // Device/location info
  deviceInfo String?
  ipAddress  String?

  // Relations
  attendeeId String? @db.ObjectId
  attendee   Attendee? @relation(fields: [attendeeId], references: [id])

  eventId    String @db.ObjectId
  event      Event     @relation(fields: [eventId], references: [id], onDelete: Cascade)

  scannerId  String @db.ObjectId
  scanner    User      @relation(fields: [scannerId], references: [id])

  scannedAt DateTime @default(now())

  @@map("scan_logs")
}

// Audit log for administrative actions
model AuditLog {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  action    String
  details   String?

  // Relations
  userId    String @db.ObjectId
  user      User     @relation(fields: [userId], references: [id])

  eventId   String? @db.ObjectId
  event     Event?   @relation(fields: [eventId], references: [id])

  createdAt DateTime @default(now())

  @@map("audit_logs")
}

// User preferences for customization
model UserPreferences {
  id                  String  @id @default(auto()) @map("_id") @db.ObjectId
  theme               String  @default("light")
  notifications       Boolean @default(true)
  emailNotifications  Boolean @default(true)
  autoLogout          Int     @default(30)
  language            String  @default("en")
  timezone            String  @default("UTC")

  // Relations
  userId String @unique @db.ObjectId
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("user_preferences")
}
