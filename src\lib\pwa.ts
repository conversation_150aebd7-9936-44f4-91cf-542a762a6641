"use client"

import { useState, useEffect } from "react"

// PWA utilities for service worker registration and offline functionality

export interface OfflineScan {
  id: string
  qrCode: string
  attendeeId?: string
  eventId: string
  scannedAt: string
  deviceInfo?: string
  synced: boolean
}

export interface PWAInstallPrompt {
  prompt: () => Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

// Service Worker Registration
export async function registerServiceWorker(): Promise<ServiceWorkerRegistration | null> {
  if (typeof window === 'undefined' || !('serviceWorker' in navigator)) {
    console.log('Service Worker not supported')
    return null
  }

  try {
    const registration = await navigator.serviceWorker.register('/sw.js', {
      scope: '/'
    })

    console.log('Service Worker registered successfully:', registration)

    // Handle updates
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing
      if (newWorker) {
        newWorker.addEventListener('statechange', () => {
          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
            // New version available
            console.log('New version available')
            notifyUpdate()
          }
        })
      }
    })

    return registration
  } catch (error) {
    console.error('Service Worker registration failed:', error)
    return null
  }
}

// Check if app is running as PWA
export function isPWA(): boolean {
  if (typeof window === 'undefined') return false
  
  return (
    window.matchMedia('(display-mode: standalone)').matches ||
    (window.navigator as any).standalone === true ||
    document.referrer.includes('android-app://')
  )
}

// Check if device supports PWA installation
export function canInstallPWA(): boolean {
  if (typeof window === 'undefined') return false
  
  return 'serviceWorker' in navigator && 'PushManager' in window
}

// PWA Installation
let deferredPrompt: PWAInstallPrompt | null = null

export function setupPWAInstall(): void {
  if (typeof window === 'undefined') return

  window.addEventListener('beforeinstallprompt', (e) => {
    e.preventDefault()
    deferredPrompt = e as any
    console.log('PWA install prompt available')
    
    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('pwa-install-available'))
  })

  window.addEventListener('appinstalled', () => {
    console.log('PWA installed successfully')
    deferredPrompt = null
    
    // Dispatch custom event
    window.dispatchEvent(new CustomEvent('pwa-installed'))
  })
}

export async function installPWA(): Promise<boolean> {
  if (!deferredPrompt) {
    console.log('PWA install prompt not available')
    return false
  }

  try {
    await deferredPrompt.prompt()
    const choiceResult = await deferredPrompt.userChoice
    
    console.log('PWA install choice:', choiceResult.outcome)
    deferredPrompt = null
    
    return choiceResult.outcome === 'accepted'
  } catch (error) {
    console.error('PWA installation failed:', error)
    return false
  }
}

// Offline Storage using IndexedDB
export class OfflineStorage {
  private dbName = 'FoodScanOffline'
  private version = 1
  private db: IDBDatabase | null = null

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version)

      request.onerror = () => reject(request.error)
      request.onsuccess = () => {
        this.db = request.result
        resolve()
      }

      request.onupgradeneeded = () => {
        const db = request.result
        
        // Create scans store
        if (!db.objectStoreNames.contains('scans')) {
          const scansStore = db.createObjectStore('scans', { keyPath: 'id' })
          scansStore.createIndex('eventId', 'eventId', { unique: false })
          scansStore.createIndex('synced', 'synced', { unique: false })
        }

        // Create events store for offline access
        if (!db.objectStoreNames.contains('events')) {
          const eventsStore = db.createObjectStore('events', { keyPath: 'id' })
        }

        // Create attendees store for offline access
        if (!db.objectStoreNames.contains('attendees')) {
          const attendeesStore = db.createObjectStore('attendees', { keyPath: 'id' })
          attendeesStore.createIndex('eventId', 'eventId', { unique: false })
          attendeesStore.createIndex('qrCode', 'qrCode', { unique: true })
        }
      }
    })
  }

  async saveOfflineScan(scan: OfflineScan): Promise<void> {
    if (!this.db) await this.init()

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['scans'], 'readwrite')
      const store = transaction.objectStore('scans')
      const request = store.put(scan)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(request.error)
    })
  }

  async getOfflineScans(): Promise<OfflineScan[]> {
    if (!this.db) await this.init()

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['scans'], 'readonly')
      const store = transaction.objectStore('scans')
      const request = store.getAll()

      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  async getUnsyncedScans(): Promise<OfflineScan[]> {
    if (!this.db) await this.init()

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['scans'], 'readonly')
      const store = transaction.objectStore('scans')
      const index = store.index('synced')
      const request = index.getAll(false)

      request.onsuccess = () => resolve(request.result)
      request.onerror = () => reject(request.error)
    })
  }

  async markScanAsSynced(scanId: string): Promise<void> {
    if (!this.db) await this.init()

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['scans'], 'readwrite')
      const store = transaction.objectStore('scans')
      const getRequest = store.get(scanId)

      getRequest.onsuccess = () => {
        const scan = getRequest.result
        if (scan) {
          scan.synced = true
          const putRequest = store.put(scan)
          putRequest.onsuccess = () => resolve()
          putRequest.onerror = () => reject(putRequest.error)
        } else {
          resolve()
        }
      }
      getRequest.onerror = () => reject(getRequest.error)
    })
  }

  async clearSyncedScans(): Promise<void> {
    if (!this.db) await this.init()

    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction(['scans'], 'readwrite')
      const store = transaction.objectStore('scans')
      const index = store.index('synced')
      const request = index.openCursor(true)

      request.onsuccess = () => {
        const cursor = request.result
        if (cursor) {
          cursor.delete()
          cursor.continue()
        } else {
          resolve()
        }
      }
      request.onerror = () => reject(request.error)
    })
  }
}

// Network Status
export function useNetworkStatus() {
  if (typeof window === 'undefined') {
    return { isOnline: true, isOffline: false }
  }

  const [isOnline, setIsOnline] = useState(navigator.onLine)

  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  return {
    isOnline,
    isOffline: !isOnline
  }
}

// Background Sync
export async function requestBackgroundSync(tag: string): Promise<void> {
  if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
    try {
      const registration = await navigator.serviceWorker.ready
      await registration.sync.register(tag)
      console.log('Background sync registered:', tag)
    } catch (error) {
      console.error('Background sync registration failed:', error)
    }
  }
}

// Notification helpers
function notifyUpdate(): void {
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('sw-update-available'))
  }
}

// Initialize PWA features
export function initializePWA(): void {
  if (typeof window === 'undefined') return

  // Register service worker
  registerServiceWorker()

  // Setup PWA install prompt
  setupPWAInstall()

  // Listen for service worker messages
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.addEventListener('message', (event) => {
      console.log('Message from service worker:', event.data)
      
      if (event.data.type === 'SYNC_COMPLETE') {
        window.dispatchEvent(new CustomEvent('offline-sync-complete', {
          detail: { syncedCount: event.data.syncedCount }
        }))
      }
    })
  }
}


