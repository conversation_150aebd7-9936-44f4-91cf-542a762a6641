"use client"

import { io, Socket } from 'socket.io-client'

export interface RealtimeEvent {
  type: 'scan' | 'attendee_update' | 'event_update' | 'system_status'
  data: any
  timestamp: string
  userId?: string
  eventId?: string
}

export interface ScanEvent {
  type: 'scan'
  data: {
    id: string
    attendeeId: string
    attendeeName: string
    eventId: string
    success: boolean
    mealType?: string
    error?: string
    scannedAt: string
    scannerId: string
  }
}

export interface AttendeeUpdateEvent {
  type: 'attendee_update'
  data: {
    id: string
    eventId: string
    action: 'created' | 'updated' | 'deleted'
    attendee?: any
  }
}

export interface EventUpdateEvent {
  type: 'event_update'
  data: {
    id: string
    action: 'created' | 'updated' | 'deleted'
    event?: any
  }
}

export interface SystemStatusEvent {
  type: 'system_status'
  data: {
    activeScans: number
    connectedDevices: number
    systemHealth: 'healthy' | 'warning' | 'error'
    lastUpdate: string
  }
}

class WebSocketManager {
  private socket: Socket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private eventListeners: Map<string, Set<Function>> = new Map()
  private isConnected = false

  constructor() {
    if (typeof window !== 'undefined') {
      this.connect()
    }
  }

  private connect() {
    try {
      this.socket = io(process.env.NEXT_PUBLIC_WS_URL || window.location.origin, {
        transports: ['websocket', 'polling'],
        timeout: 20000,
        forceNew: true,
      })

      this.setupEventListeners()
    } catch (error) {
      console.error('WebSocket connection failed:', error)
      this.handleReconnect()
    }
  }

  private setupEventListeners() {
    if (!this.socket) return

    this.socket.on('connect', () => {
      console.log('WebSocket connected')
      this.isConnected = true
      this.reconnectAttempts = 0
      this.emit('connection_status', { connected: true })
    })

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason)
      this.isConnected = false
      this.emit('connection_status', { connected: false, reason })
      
      if (reason === 'io server disconnect') {
        // Server initiated disconnect, don't reconnect automatically
        return
      }
      
      this.handleReconnect()
    })

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error)
      this.isConnected = false
      this.emit('connection_error', { error: error.message })
      this.handleReconnect()
    })

    // Handle real-time events
    this.socket.on('realtime_event', (event: RealtimeEvent) => {
      console.log('Received realtime event:', event)
      this.emit(event.type, event.data)
      this.emit('realtime_event', event)
    })

    // Handle specific event types
    this.socket.on('scan_event', (data: ScanEvent['data']) => {
      this.emit('scan', data)
    })

    this.socket.on('attendee_update', (data: AttendeeUpdateEvent['data']) => {
      this.emit('attendee_update', data)
    })

    this.socket.on('event_update', (data: EventUpdateEvent['data']) => {
      this.emit('event_update', data)
    })

    this.socket.on('system_status', (data: SystemStatusEvent['data']) => {
      this.emit('system_status', data)
    })
  }

  private handleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached')
      this.emit('max_reconnect_attempts', { attempts: this.reconnectAttempts })
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`)
    
    setTimeout(() => {
      if (!this.isConnected) {
        this.connect()
      }
    }, delay)
  }

  // Join a room (e.g., event-specific room)
  joinRoom(room: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('join_room', room)
      console.log(`Joined room: ${room}`)
    }
  }

  // Leave a room
  leaveRoom(room: string) {
    if (this.socket && this.isConnected) {
      this.socket.emit('leave_room', room)
      console.log(`Left room: ${room}`)
    }
  }

  // Send a real-time event
  sendEvent(event: Omit<RealtimeEvent, 'timestamp'>) {
    if (this.socket && this.isConnected) {
      const eventWithTimestamp: RealtimeEvent = {
        ...event,
        timestamp: new Date().toISOString()
      }
      this.socket.emit('realtime_event', eventWithTimestamp)
    } else {
      console.warn('Cannot send event: WebSocket not connected')
    }
  }

  // Subscribe to events
  on(eventType: string, callback: Function) {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set())
    }
    this.eventListeners.get(eventType)!.add(callback)
  }

  // Unsubscribe from events
  off(eventType: string, callback: Function) {
    const listeners = this.eventListeners.get(eventType)
    if (listeners) {
      listeners.delete(callback)
    }
  }

  // Emit events to listeners
  private emit(eventType: string, data: any) {
    const listeners = this.eventListeners.get(eventType)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`Error in event listener for ${eventType}:`, error)
        }
      })
    }
  }

  // Get connection status
  getConnectionStatus() {
    return {
      connected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts
    }
  }

  // Manually reconnect
  reconnect() {
    if (this.socket) {
      this.socket.disconnect()
    }
    this.reconnectAttempts = 0
    this.connect()
  }

  // Disconnect
  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
    }
    this.isConnected = false
    this.eventListeners.clear()
  }
}

// Singleton instance
let wsManager: WebSocketManager | null = null

export function getWebSocketManager(): WebSocketManager {
  if (!wsManager && typeof window !== 'undefined') {
    wsManager = new WebSocketManager()
  }
  return wsManager!
}

// React hook for WebSocket
export function useWebSocket() {
  const ws = getWebSocketManager()
  
  return {
    socket: ws,
    isConnected: ws?.getConnectionStatus().connected || false,
    reconnectAttempts: ws?.getConnectionStatus().reconnectAttempts || 0,
    joinRoom: (room: string) => ws?.joinRoom(room),
    leaveRoom: (room: string) => ws?.leaveRoom(room),
    sendEvent: (event: Omit<RealtimeEvent, 'timestamp'>) => ws?.sendEvent(event),
    on: (eventType: string, callback: Function) => ws?.on(eventType, callback),
    off: (eventType: string, callback: Function) => ws?.off(eventType, callback),
    reconnect: () => ws?.reconnect(),
    disconnect: () => ws?.disconnect()
  }
}

// Utility functions for specific event types
export function broadcastScanEvent(scanData: ScanEvent['data']) {
  const ws = getWebSocketManager()
  ws?.sendEvent({
    type: 'scan',
    data: scanData,
    eventId: scanData.eventId,
    userId: scanData.scannerId
  })
}

export function broadcastAttendeeUpdate(updateData: AttendeeUpdateEvent['data']) {
  const ws = getWebSocketManager()
  ws?.sendEvent({
    type: 'attendee_update',
    data: updateData,
    eventId: updateData.eventId
  })
}

export function broadcastEventUpdate(updateData: EventUpdateEvent['data']) {
  const ws = getWebSocketManager()
  ws?.sendEvent({
    type: 'event_update',
    data: updateData
  })
}

export function broadcastSystemStatus(statusData: SystemStatusEvent['data']) {
  const ws = getWebSocketManager()
  ws?.sendEvent({
    type: 'system_status',
    data: statusData
  })
}
