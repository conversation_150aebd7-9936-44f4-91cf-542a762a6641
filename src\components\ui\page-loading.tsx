"use client"

import { AppLayout } from "@/components/layout/app-layout"
import { LoadingSpinner } from "@/components/ui/loading-spinner"

interface PageLoadingProps {
  message?: string
  withLayout?: boolean
}

export function PageLoading({ message = "Loading...", withLayout = true }: PageLoadingProps) {
  const content = (
    <div className="flex h-screen items-center justify-center">
      <div className="text-center space-y-4">
        <LoadingSpinner size="lg" />
        <p className="text-muted-foreground">{message}</p>
      </div>
    </div>
  )

  if (withLayout) {
    return <AppLayout>{content}</AppLayout>
  }

  return content
}

export function InlineLoading({ message = "Loading..." }: { message?: string }) {
  return (
    <div className="flex items-center justify-center py-8">
      <div className="text-center space-y-2">
        <LoadingSpinner size="md" />
        <p className="text-sm text-muted-foreground">{message}</p>
      </div>
    </div>
  )
}
