import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { attendeesToCSV } from "@/lib/csv-utils"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const eventId = searchParams.get('eventId')
    const format = searchParams.get('format') || 'csv'

    const where = eventId ? { eventId } : {}

    const attendees = await prisma.attendee.findMany({
      where,
      include: {
        event: {
          select: {
            name: true
          }
        },
        mealConsumptions: {
          include: {
            mealType: true
          }
        },
        scanLogs: {
          where: {
            success: true
          },
          orderBy: {
            scannedAt: 'desc'
          },
          take: 1
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    if (format === 'csv') {
      const csvData = attendees.map(attendee => ({
        name: attendee.name,
        email: attendee.email || '',
        eventName: attendee.event.name,
        dietaryRestrictions: attendee.dietaryRestrictions || '',
        specialNotes: attendee.specialNotes || '',
        qrCode: attendee.qrCode,
        scannedAt: attendee.scannedAt?.toISOString() || '',
        createdAt: attendee.createdAt.toISOString(),
        mealConsumptions: attendee.mealConsumptions.map(mc => mc.mealType.name).join('; ')
      }))

      const csvContent = attendeesToCSV(csvData)
      
      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="attendees-${eventId || 'all'}-${new Date().toISOString().split('T')[0]}.csv"`
        }
      })
    }

    if (format === 'json') {
      return NextResponse.json(attendees, {
        headers: {
          'Content-Disposition': `attachment; filename="attendees-${eventId || 'all'}-${new Date().toISOString().split('T')[0]}.json"`
        }
      })
    }

    return NextResponse.json({ error: "Unsupported format" }, { status: 400 })

  } catch (error) {
    console.error("Error exporting attendees:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
