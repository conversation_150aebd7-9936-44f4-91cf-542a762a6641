import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { MongoClient, ObjectId } from "mongodb"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const events = await prisma.event.findMany({
      include: {
        _count: {
          select: {
            attendees: true,
            scanLogs: true,
          }
        },
        mealTypes: true,
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(events)
  } catch (error) {
    console.error("Error fetching events:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  const client = new MongoClient(process.env.DATABASE_URL!)

  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      description,
      startDate,
      endDate,
      serviceStartTime,
      serviceEndTime,
      primaryColor,
      secondaryColor,
      logoUrl,
      isActive,
      mealTypes
    } = body

    // Validate required fields
    if (!name || typeof name !== 'string' || !name.trim()) {
      return NextResponse.json({ error: "Event name is required" }, { status: 400 })
    }

    if (!startDate || !endDate) {
      return NextResponse.json({ error: "Start date and end date are required" }, { status: 400 })
    }

    // Connect to MongoDB
    await client.connect()
    const db = client.db('foodscan')
    const eventsCollection = db.collection('events')
    const mealTypesCollection = db.collection('meal_types')

    // Parse dates
    const parsedStartDate = new Date(startDate)
    const parsedEndDate = new Date(endDate)
    const parsedServiceStartTime = serviceStartTime ? new Date(serviceStartTime) : null
    const parsedServiceEndTime = serviceEndTime ? new Date(serviceEndTime) : null

    if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {
      return NextResponse.json({ error: "Invalid date format" }, { status: 400 })
    }

    // Create event
    const eventId = new ObjectId()
    const newEvent = {
      _id: eventId,
      name: name.trim(),
      description: description?.trim() || null,
      startDate: parsedStartDate,
      endDate: parsedEndDate,
      serviceStartTime: parsedServiceStartTime,
      serviceEndTime: parsedServiceEndTime,
      primaryColor: primaryColor || "#3b82f6",
      secondaryColor: secondaryColor || "#1e40af",
      logoUrl: logoUrl?.trim() || null,
      isActive: isActive ?? true,
      creatorId: session.user.id,
      createdAt: new Date(),
      updatedAt: new Date()
    }

    await eventsCollection.insertOne(newEvent)

    // Create meal types if provided
    if (mealTypes && Array.isArray(mealTypes) && mealTypes.length > 0) {
      const newMealTypes = mealTypes.map((meal: any) => ({
        _id: new ObjectId(),
        name: meal.name?.trim() || '',
        description: meal.description?.trim() || null,
        maxQuantityPerAttendee: Math.max(1, parseInt(meal.maxQuantityPerAttendee) || 1),
        totalQuantity: meal.totalQuantity ? Math.max(0, parseInt(meal.totalQuantity)) : null,
        consumedQuantity: 0,
        isVegetarian: Boolean(meal.isVegetarian),
        isVegan: Boolean(meal.isVegan),
        isGlutenFree: Boolean(meal.isGlutenFree),
        isAvailable: true,
        eventId: eventId.toString(),
        createdAt: new Date(),
        updatedAt: new Date()
      }))

      await mealTypesCollection.insertMany(newMealTypes)
    }

    // Fetch the complete event with meal types and counts
    const createdEvent = await eventsCollection.findOne({ _id: eventId })
    const eventMealTypes = await mealTypesCollection.find({ eventId: eventId.toString() }).toArray()

    // Format response to match Prisma structure
    const response = {
      ...createdEvent,
      id: createdEvent._id.toString(),
      mealTypes: eventMealTypes.map(mt => ({
        ...mt,
        id: mt._id.toString()
      })),
      _count: {
        attendees: 0,
        scanLogs: 0
      }
    }

    // Remove MongoDB _id field
    delete response._id

    return NextResponse.json(response, { status: 201 })
  } catch (error) {
    console.error("Error creating event:", error)
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  } finally {
    await client.close()
  }
}
