import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const events = await prisma.event.findMany({
      include: {
        _count: {
          select: {
            attendees: true,
            scanLogs: true,
          }
        },
        mealTypes: true,
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(events)
  } catch (error) {
    console.error("Error fetching events:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      description,
      startDate,
      endDate,
      serviceStartTime,
      serviceEndTime,
      primaryColor,
      secondaryColor,
      logoUrl,
      isActive,
      mealTypes
    } = body

    // Create event first
    const event = await prisma.event.create({
      data: {
        name,
        description,
        startDate: new Date(startDate),
        endDate: new Date(endDate),
        serviceStartTime: serviceStartTime ? new Date(serviceStartTime) : null,
        serviceEndTime: serviceEndTime ? new Date(serviceEndTime) : null,
        primaryColor: primaryColor || "#3b82f6",
        secondaryColor: secondaryColor || "#1e40af",
        logoUrl,
        isActive: isActive ?? true,
        creatorId: session.user.id,
      }
    })

    // Create meal types separately to avoid transaction requirement
    if (mealTypes && mealTypes.length > 0) {
      await Promise.all(
        mealTypes.map((meal: any) =>
          prisma.mealType.create({
            data: {
              name: meal.name,
              description: meal.description,
              maxQuantityPerAttendee: meal.maxQuantityPerAttendee || 1,
              totalQuantity: meal.totalQuantity || null,
              isVegetarian: meal.isVegetarian || false,
              isVegan: meal.isVegan || false,
              isGlutenFree: meal.isGlutenFree || false,
              eventId: event.id,
            }
          })
        )
      )
    }

    // Fetch the complete event with meal types and counts
    const completeEvent = await prisma.event.findUnique({
      where: { id: event.id },
      include: {
        mealTypes: true,
        _count: {
          select: {
            attendees: true,
            scanLogs: true,
          }
        }
      }
    })

    return NextResponse.json(completeEvent, { status: 201 })
  } catch (error) {
    console.error("Error creating event:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
