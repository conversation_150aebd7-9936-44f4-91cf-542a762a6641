import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { generateQRCode } from "@/lib/qr-code"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const eventId = searchParams.get('eventId')

    const where = eventId ? { eventId } : {}

    const attendees = await prisma.attendee.findMany({
      where,
      include: {
        event: {
          select: {
            name: true,
            id: true
          }
        },
        mealConsumptions: {
          include: {
            mealType: true
          }
        },
        scanLogs: {
          orderBy: {
            scannedAt: 'desc'
          },
          take: 1
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return NextResponse.json(attendees)
  } catch (error) {
    console.error("Error fetching attendees:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { attendees: attendeeList, eventId } = body

    // Verify event exists
    const event = await prisma.event.findUnique({
      where: { id: eventId }
    })

    if (!event) {
      return NextResponse.json({ error: "Event not found" }, { status: 404 })
    }

    const createdAttendees = []

    for (const attendeeData of attendeeList) {
      // Generate unique QR code
      const qrCode = `${event.name.replace(/\s+/g, '').toUpperCase()}-${Date.now()}-${Math.random().toString(36).substr(2, 6)}`

      const attendee = await prisma.attendee.create({
        data: {
          name: attendeeData.name,
          email: attendeeData.email,
          qrCode,
          dietaryRestrictions: attendeeData.dietaryRestrictions,
          specialNotes: attendeeData.specialNotes,
          eventId,
        },
        include: {
          event: {
            select: {
              name: true,
              id: true
            }
          }
        }
      })

      createdAttendees.push(attendee)
    }

    return NextResponse.json(createdAttendees, { status: 201 })
  } catch (error) {
    console.error("Error creating attendees:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
