import JSZ<PERSON> from 'jszip'
import { generateQRCodeDataURL, generateBatchQRCodes, createPrintableQRCode } from './qr-code'
import { attendeesToCSV, type AttendeeExportRow } from './csv-utils'

export interface ExportOptions {
  format: 'pdf' | 'csv' | 'zip' | 'json'
  includeQRCodes: boolean
  includeImages: boolean
  customFields?: string[]
  dateRange?: {
    from: Date
    to: Date
  }
}

export interface AttendeeExportData {
  id: string
  name: string
  email?: string
  eventId: string
  eventName: string
  qrCode: string
  dietaryRestrictions?: string
  specialNotes?: string
  scannedAt?: string
  createdAt: string
  mealConsumptions?: Array<{
    mealType: string
    quantity: number
    consumedAt: string
  }>
}

export interface EventExportData {
  id: string
  name: string
  description?: string
  startDate: string
  endDate: string
  attendeeCount: number
  scannedCount: number
  completionRate: number
  mealTypes: Array<{
    name: string
    totalQuantity?: number
    consumedQuantity: number
  }>
}

export interface AnalyticsExportData {
  eventId: string
  eventName: string
  totalAttendees: number
  totalScans: number
  completionRate: number
  scansByHour: Array<{
    hour: string
    scans: number
  }>
  mealTypeDistribution: Array<{
    mealType: string
    count: number
    percentage: number
  }>
  dailyTrends: Array<{
    date: string
    scans: number
    completion: number
  }>
}

// PDF Generation utilities
export async function generateAttendeePDF(
  attendees: AttendeeExportData[],
  options: ExportOptions
): Promise<Blob> {
  // This would use a library like jsPDF or Puppeteer
  // For now, we'll create a simple HTML-based PDF
  
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Attendee Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .attendee-card { 
          border: 1px solid #ccc; 
          margin: 10px 0; 
          padding: 15px; 
          page-break-inside: avoid;
          display: flex;
          align-items: center;
        }
        .qr-code { margin-right: 20px; }
        .attendee-info { flex: 1; }
        .attendee-name { font-weight: bold; font-size: 16px; }
        .attendee-details { color: #666; font-size: 12px; margin-top: 5px; }
        @media print {
          .attendee-card { page-break-inside: avoid; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Attendee Report</h1>
        <p>Generated on ${new Date().toLocaleDateString()}</p>
        <p>Total Attendees: ${attendees.length}</p>
      </div>
      
      ${await Promise.all(attendees.map(async (attendee) => {
        let qrCodeHtml = ''
        if (options.includeQRCodes) {
          try {
            const qrDataURL = await generateQRCodeDataURL(attendee.qrCode, { size: 100 })
            qrCodeHtml = `<div class="qr-code"><img src="${qrDataURL}" alt="QR Code" /></div>`
          } catch (error) {
            console.error('Error generating QR code for PDF:', error)
          }
        }
        
        return `
          <div class="attendee-card">
            ${qrCodeHtml}
            <div class="attendee-info">
              <div class="attendee-name">${attendee.name}</div>
              <div class="attendee-details">
                ${attendee.email ? `Email: ${attendee.email}<br>` : ''}
                Event: ${attendee.eventName}<br>
                QR Code: ${attendee.qrCode}<br>
                ${attendee.dietaryRestrictions ? `Dietary: ${attendee.dietaryRestrictions}<br>` : ''}
                ${attendee.specialNotes ? `Notes: ${attendee.specialNotes}<br>` : ''}
                ${attendee.scannedAt ? `Scanned: ${new Date(attendee.scannedAt).toLocaleString()}` : 'Not scanned'}
              </div>
            </div>
          </div>
        `
      })).then(cards => cards.join(''))}
    </body>
    </html>
  `

  // Convert HTML to PDF (in a real implementation, you'd use Puppeteer or similar)
  const blob = new Blob([htmlContent], { type: 'text/html' })
  return blob
}

// ZIP Generation with QR codes
export async function generateAttendeeZIP(
  attendees: AttendeeExportData[],
  options: ExportOptions
): Promise<Blob> {
  const zip = new JSZip()

  // Add CSV file
  const csvContent = attendeesToCSV(attendees.map(a => ({
    name: a.name,
    email: a.email,
    eventName: a.eventName,
    dietaryRestrictions: a.dietaryRestrictions,
    specialNotes: a.specialNotes,
    qrCode: a.qrCode,
    scannedAt: a.scannedAt,
    createdAt: a.createdAt
  })))
  zip.file('attendees.csv', csvContent)

  // Add JSON file
  zip.file('attendees.json', JSON.stringify(attendees, null, 2))

  // Generate and add QR codes
  if (options.includeQRCodes) {
    const qrFolder = zip.folder('qr-codes')
    const badgesFolder = zip.folder('printable-badges')

    for (const attendee of attendees) {
      try {
        // Individual QR code image
        const qrDataURL = await generateQRCodeDataURL(attendee.qrCode, { size: 300 })
        const qrImageData = qrDataURL.split(',')[1] // Remove data:image/png;base64, prefix
        qrFolder?.file(`${attendee.qrCode}.png`, qrImageData, { base64: true })

        // Printable badge HTML
        const badgeHtml = createPrintableQRCode(
          {
            name: attendee.name,
            email: attendee.email,
            qrCode: attendee.qrCode,
            eventName: attendee.eventName
          },
          qrDataURL
        )
        badgesFolder?.file(`${attendee.qrCode}.html`, badgeHtml)
      } catch (error) {
        console.error(`Error generating QR code for ${attendee.name}:`, error)
      }
    }
  }

  // Add summary report
  const summary = generateSummaryReport(attendees)
  zip.file('summary.txt', summary)

  return await zip.generateAsync({ type: 'blob' })
}

// Analytics Export
export async function generateAnalyticsExport(
  data: AnalyticsExportData,
  format: 'csv' | 'json' | 'pdf'
): Promise<Blob> {
  switch (format) {
    case 'json':
      return new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    
    case 'csv':
      const csvContent = generateAnalyticsCSV(data)
      return new Blob([csvContent], { type: 'text/csv' })
    
    case 'pdf':
      const pdfContent = await generateAnalyticsPDF(data)
      return pdfContent
    
    default:
      throw new Error(`Unsupported format: ${format}`)
  }
}

// Helper functions
function generateSummaryReport(attendees: AttendeeExportData[]): string {
  const scannedCount = attendees.filter(a => a.scannedAt).length
  const completionRate = ((scannedCount / attendees.length) * 100).toFixed(1)
  
  const eventGroups = attendees.reduce((groups, attendee) => {
    const eventName = attendee.eventName
    if (!groups[eventName]) {
      groups[eventName] = { total: 0, scanned: 0 }
    }
    groups[eventName].total++
    if (attendee.scannedAt) {
      groups[eventName].scanned++
    }
    return groups
  }, {} as Record<string, { total: number; scanned: number }>)

  let summary = `ATTENDEE SUMMARY REPORT
Generated: ${new Date().toLocaleString()}

OVERALL STATISTICS:
- Total Attendees: ${attendees.length}
- Scanned Attendees: ${scannedCount}
- Completion Rate: ${completionRate}%

EVENT BREAKDOWN:
`

  Object.entries(eventGroups).forEach(([eventName, stats]) => {
    const eventCompletion = ((stats.scanned / stats.total) * 100).toFixed(1)
    summary += `- ${eventName}: ${stats.scanned}/${stats.total} (${eventCompletion}%)\n`
  })

  return summary
}

function generateAnalyticsCSV(data: AnalyticsExportData): string {
  let csv = `Event Analytics Report\n`
  csv += `Event Name,${data.eventName}\n`
  csv += `Total Attendees,${data.totalAttendees}\n`
  csv += `Total Scans,${data.totalScans}\n`
  csv += `Completion Rate,${data.completionRate}%\n\n`

  csv += `Hourly Scan Data\n`
  csv += `Hour,Scans\n`
  data.scansByHour.forEach(item => {
    csv += `${item.hour},${item.scans}\n`
  })

  csv += `\nMeal Type Distribution\n`
  csv += `Meal Type,Count,Percentage\n`
  data.mealTypeDistribution.forEach(item => {
    csv += `${item.mealType},${item.count},${item.percentage}%\n`
  })

  csv += `\nDaily Trends\n`
  csv += `Date,Scans,Completion Rate\n`
  data.dailyTrends.forEach(item => {
    csv += `${item.date},${item.scans},${item.completion}%\n`
  })

  return csv
}

async function generateAnalyticsPDF(data: AnalyticsExportData): Promise<Blob> {
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>Analytics Report - ${data.eventName}</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .metric { display: inline-block; margin: 10px; padding: 15px; border: 1px solid #ccc; text-align: center; }
        .metric-value { font-size: 24px; font-weight: bold; color: #2563eb; }
        .metric-label { font-size: 12px; color: #666; }
        .section { margin: 30px 0; }
        .section h2 { border-bottom: 2px solid #2563eb; padding-bottom: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
        th { background-color: #f5f5f5; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>Analytics Report</h1>
        <h2>${data.eventName}</h2>
        <p>Generated on ${new Date().toLocaleDateString()}</p>
      </div>

      <div class="section">
        <h2>Key Metrics</h2>
        <div class="metric">
          <div class="metric-value">${data.totalAttendees}</div>
          <div class="metric-label">Total Attendees</div>
        </div>
        <div class="metric">
          <div class="metric-value">${data.totalScans}</div>
          <div class="metric-label">Total Scans</div>
        </div>
        <div class="metric">
          <div class="metric-value">${data.completionRate}%</div>
          <div class="metric-label">Completion Rate</div>
        </div>
      </div>

      <div class="section">
        <h2>Hourly Scan Activity</h2>
        <table>
          <thead>
            <tr><th>Hour</th><th>Scans</th></tr>
          </thead>
          <tbody>
            ${data.scansByHour.map(item => 
              `<tr><td>${item.hour}</td><td>${item.scans}</td></tr>`
            ).join('')}
          </tbody>
        </table>
      </div>

      <div class="section">
        <h2>Meal Type Distribution</h2>
        <table>
          <thead>
            <tr><th>Meal Type</th><th>Count</th><th>Percentage</th></tr>
          </thead>
          <tbody>
            ${data.mealTypeDistribution.map(item => 
              `<tr><td>${item.mealType}</td><td>${item.count}</td><td>${item.percentage}%</td></tr>`
            ).join('')}
          </tbody>
        </table>
      </div>
    </body>
    </html>
  `

  return new Blob([htmlContent], { type: 'text/html' })
}

// Download utilities
export function downloadBlob(blob: Blob, filename: string): void {
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

export function getExportFilename(
  baseName: string,
  format: string,
  eventName?: string
): string {
  const timestamp = new Date().toISOString().split('T')[0]
  const eventPrefix = eventName ? `${eventName.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_` : ''
  return `${eventPrefix}${baseName}_${timestamp}.${format}`
}
