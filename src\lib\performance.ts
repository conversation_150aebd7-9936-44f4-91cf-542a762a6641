"use client"

import { useCallback, useEffect, useRef, useState } from "react"

// Performance monitoring utilities
export class PerformanceMonitor {
  private static instance: PerformanceMonitor
  private metrics: Map<string, number[]> = new Map()
  private observers: PerformanceObserver[] = []

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor()
    }
    return PerformanceMonitor.instance
  }

  constructor() {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      this.initializeObservers()
    }
  }

  private initializeObservers() {
    // Observe navigation timing
    const navObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'navigation') {
          const navEntry = entry as PerformanceNavigationTiming
          this.recordMetric('page_load_time', navEntry.loadEventEnd - navEntry.navigationStart)
          this.recordMetric('dom_content_loaded', navEntry.domContentLoadedEventEnd - navEntry.navigationStart)
          this.recordMetric('first_paint', navEntry.responseStart - navEntry.navigationStart)
        }
      }
    })
    navObserver.observe({ entryTypes: ['navigation'] })
    this.observers.push(navObserver)

    // Observe resource timing
    const resourceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'resource') {
          const resourceEntry = entry as PerformanceResourceTiming
          this.recordMetric('resource_load_time', resourceEntry.responseEnd - resourceEntry.startTime)
        }
      }
    })
    resourceObserver.observe({ entryTypes: ['resource'] })
    this.observers.push(resourceObserver)

    // Observe largest contentful paint
    const lcpObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'largest-contentful-paint') {
          this.recordMetric('largest_contentful_paint', entry.startTime)
        }
      }
    })
    lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
    this.observers.push(lcpObserver)

    // Observe first input delay
    const fidObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'first-input') {
          const fidEntry = entry as PerformanceEventTiming
          this.recordMetric('first_input_delay', fidEntry.processingStart - fidEntry.startTime)
        }
      }
    })
    fidObserver.observe({ entryTypes: ['first-input'] })
    this.observers.push(fidObserver)
  }

  recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, [])
    }
    this.metrics.get(name)!.push(value)
    
    // Keep only last 100 measurements
    const values = this.metrics.get(name)!
    if (values.length > 100) {
      values.shift()
    }
  }

  getMetric(name: string): { avg: number; min: number; max: number; count: number } | null {
    const values = this.metrics.get(name)
    if (!values || values.length === 0) return null

    const avg = values.reduce((sum, val) => sum + val, 0) / values.length
    const min = Math.min(...values)
    const max = Math.max(...values)

    return { avg, min, max, count: values.length }
  }

  getAllMetrics(): Record<string, { avg: number; min: number; max: number; count: number }> {
    const result: Record<string, any> = {}
    for (const [name] of this.metrics) {
      const metric = this.getMetric(name)
      if (metric) {
        result[name] = metric
      }
    }
    return result
  }

  startTimer(name: string): () => void {
    const startTime = performance.now()
    return () => {
      const endTime = performance.now()
      this.recordMetric(name, endTime - startTime)
    }
  }

  cleanup() {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
    this.metrics.clear()
  }
}

// React hooks for performance monitoring
export function usePerformanceTimer(name: string) {
  const monitor = PerformanceMonitor.getInstance()
  
  return useCallback(() => {
    return monitor.startTimer(name)
  }, [monitor, name])
}

export function usePerformanceMetric(name: string) {
  const [metric, setMetric] = useState<{ avg: number; min: number; max: number; count: number } | null>(null)
  const monitor = PerformanceMonitor.getInstance()

  useEffect(() => {
    const interval = setInterval(() => {
      const currentMetric = monitor.getMetric(name)
      setMetric(currentMetric)
    }, 1000)

    return () => clearInterval(interval)
  }, [monitor, name])

  return metric
}

// Virtual scrolling for large lists
export function useVirtualScroll<T>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5
}: {
  items: T[]
  itemHeight: number
  containerHeight: number
  overscan?: number
}) {
  const [scrollTop, setScrollTop] = useState(0)

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  )

  const visibleItems = items.slice(startIndex, endIndex + 1)
  const totalHeight = items.length * itemHeight
  const offsetY = startIndex * itemHeight

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }, [])

  return {
    visibleItems,
    totalHeight,
    offsetY,
    startIndex,
    endIndex,
    handleScroll
  }
}

// Debounced search hook
export function useDebouncedSearch(
  searchFunction: (query: string) => Promise<any[]>,
  delay: number = 300
) {
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const timeoutRef = useRef<NodeJS.Timeout>()

  useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    if (!query.trim()) {
      setResults([])
      setIsLoading(false)
      return
    }

    setIsLoading(true)
    setError(null)

    timeoutRef.current = setTimeout(async () => {
      try {
        const searchResults = await searchFunction(query)
        setResults(searchResults)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Search failed')
        setResults([])
      } finally {
        setIsLoading(false)
      }
    }, delay)

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [query, searchFunction, delay])

  return {
    query,
    setQuery,
    results,
    isLoading,
    error
  }
}

// Intersection Observer hook for lazy loading
export function useIntersectionObserver(
  callback: (entries: IntersectionObserverEntry[]) => void,
  options?: IntersectionObserverInit
) {
  const targetRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const target = targetRef.current
    if (!target) return

    const observer = new IntersectionObserver(callback, options)
    observer.observe(target)

    return () => {
      observer.unobserve(target)
      observer.disconnect()
    }
  }, [callback, options])

  return targetRef
}

// Memory usage monitoring
export function useMemoryMonitor() {
  const [memoryInfo, setMemoryInfo] = useState<{
    usedJSHeapSize: number
    totalJSHeapSize: number
    jsHeapSizeLimit: number
  } | null>(null)

  useEffect(() => {
    if (!('memory' in performance)) return

    const updateMemoryInfo = () => {
      const memory = (performance as any).memory
      setMemoryInfo({
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      })
    }

    updateMemoryInfo()
    const interval = setInterval(updateMemoryInfo, 5000)

    return () => clearInterval(interval)
  }, [])

  return memoryInfo
}

// Bundle size analyzer
export function analyzeBundleSize() {
  if (typeof window === 'undefined') return null

  const scripts = Array.from(document.querySelectorAll('script[src]'))
  const styles = Array.from(document.querySelectorAll('link[rel="stylesheet"]'))

  const scriptSizes = scripts.map(script => ({
    src: (script as HTMLScriptElement).src,
    size: 'Unknown' // Would need server-side analysis
  }))

  const styleSizes = styles.map(style => ({
    href: (style as HTMLLinkElement).href,
    size: 'Unknown' // Would need server-side analysis
  }))

  return {
    scripts: scriptSizes,
    styles: styleSizes,
    totalScripts: scripts.length,
    totalStyles: styles.length
  }
}

// Performance budget checker
export class PerformanceBudget {
  private budgets: Map<string, number> = new Map()

  setBudget(metric: string, limit: number) {
    this.budgets.set(metric, limit)
  }

  checkBudget(metric: string, value: number): { passed: boolean; budget: number; actual: number } {
    const budget = this.budgets.get(metric)
    if (!budget) {
      return { passed: true, budget: 0, actual: value }
    }

    return {
      passed: value <= budget,
      budget,
      actual: value
    }
  }

  checkAllBudgets(): Array<{ metric: string; passed: boolean; budget: number; actual: number }> {
    const monitor = PerformanceMonitor.getInstance()
    const results: Array<{ metric: string; passed: boolean; budget: number; actual: number }> = []

    for (const [metric, budget] of this.budgets) {
      const metricData = monitor.getMetric(metric)
      if (metricData) {
        results.push({
          metric,
          passed: metricData.avg <= budget,
          budget,
          actual: metricData.avg
        })
      }
    }

    return results
  }
}

// Default performance budgets
export const defaultPerformanceBudgets = new PerformanceBudget()
defaultPerformanceBudgets.setBudget('page_load_time', 3000) // 3 seconds
defaultPerformanceBudgets.setBudget('largest_contentful_paint', 2500) // 2.5 seconds
defaultPerformanceBudgets.setBudget('first_input_delay', 100) // 100ms
defaultPerformanceBudgets.setBudget('resource_load_time', 1000) // 1 second
