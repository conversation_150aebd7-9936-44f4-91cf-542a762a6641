"use client"

import { useState, useEffect, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { 
  QrCode, 
  Users, 
  Calendar, 
  Activity,
  CheckCircle,
  AlertCircle,
  Clock,
  Pause,
  Play,
  Trash2
} from "lucide-react"
import { useWebSocket, type RealtimeEvent, type ScanEvent } from "@/lib/websocket"
import { formatDateTime } from "@/lib/utils"

interface FeedEvent extends RealtimeEvent {
  id: string
}

export function RealtimeFeed() {
  const { on, off } = useWebSocket()
  const [events, setEvents] = useState<FeedEvent[]>([])
  const [isPaused, setIsPaused] = useState(false)
  const [maxEvents] = useState(50)
  const feedRef = useRef<HTMLDivElement>(null)
  const [autoScroll, setAutoScroll] = useState(true)

  useEffect(() => {
    const handleRealtimeEvent = (event: RealtimeEvent) => {
      if (isPaused) return

      const feedEvent: FeedEvent = {
        ...event,
        id: `${event.type}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      }

      setEvents(prev => {
        const newEvents = [feedEvent, ...prev].slice(0, maxEvents)
        return newEvents
      })

      // Auto-scroll to top if enabled
      if (autoScroll && feedRef.current) {
        setTimeout(() => {
          feedRef.current?.scrollTo({ top: 0, behavior: 'smooth' })
        }, 100)
      }
    }

    on('realtime_event', handleRealtimeEvent)

    return () => {
      off('realtime_event', handleRealtimeEvent)
    }
  }, [on, off, isPaused, maxEvents, autoScroll])

  const clearEvents = () => {
    setEvents([])
  }

  const togglePause = () => {
    setIsPaused(!isPaused)
  }

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'scan': return <QrCode className="h-4 w-4" />
      case 'attendee_update': return <Users className="h-4 w-4" />
      case 'event_update': return <Calendar className="h-4 w-4" />
      case 'system_status': return <Activity className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const getEventColor = (type: string, data: any) => {
    switch (type) {
      case 'scan':
        return data.success ? 'success' : 'destructive'
      case 'attendee_update':
        return data.action === 'created' ? 'success' : 
               data.action === 'deleted' ? 'destructive' : 'info'
      case 'event_update':
        return data.action === 'created' ? 'success' : 
               data.action === 'deleted' ? 'destructive' : 'info'
      case 'system_status':
        return data.systemHealth === 'healthy' ? 'success' :
               data.systemHealth === 'warning' ? 'warning' : 'destructive'
      default:
        return 'secondary'
    }
  }

  const formatEventMessage = (event: FeedEvent) => {
    switch (event.type) {
      case 'scan':
        const scanData = event.data as ScanEvent['data']
        return scanData.success 
          ? `${scanData.attendeeName} successfully scanned for ${scanData.mealType || 'meal'}`
          : `Failed scan attempt: ${scanData.error || 'Unknown error'}`
      
      case 'attendee_update':
        const attendeeData = event.data
        return `Attendee ${attendeeData.action}: ${attendeeData.attendee?.name || 'Unknown'}`
      
      case 'event_update':
        const eventData = event.data
        return `Event ${eventData.action}: ${eventData.event?.name || 'Unknown'}`
      
      case 'system_status':
        const statusData = event.data
        return `System status: ${statusData.systemHealth} (${statusData.connectedDevices} devices, ${statusData.activeScans} active scans)`
      
      default:
        return `${event.type} event received`
    }
  }

  const getEventDetails = (event: FeedEvent) => {
    const details = []
    
    if (event.eventId) {
      details.push(`Event: ${event.eventId}`)
    }
    
    if (event.userId) {
      details.push(`User: ${event.userId}`)
    }
    
    details.push(formatDateTime(event.timestamp))
    
    return details.join(' • ')
  }

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Live Activity Feed</CardTitle>
            <CardDescription>
              Real-time events across all connected devices
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary">
              {events.length} events
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={togglePause}
            >
              {isPaused ? (
                <>
                  <Play className="h-3 w-3 mr-1" />
                  Resume
                </>
              ) : (
                <>
                  <Pause className="h-3 w-3 mr-1" />
                  Pause
                </>
              )}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearEvents}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        {isPaused && (
          <div className="px-6 py-2 bg-yellow-50 border-b border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800">
            <div className="flex items-center space-x-2 text-sm text-yellow-800 dark:text-yellow-200">
              <Pause className="h-4 w-4" />
              <span>Feed paused - new events are not being displayed</span>
            </div>
          </div>
        )}
        
        <div 
          ref={feedRef}
          className="max-h-96 overflow-auto"
        >
          {events.length === 0 ? (
            <div className="text-center py-8 px-6">
              <Activity className="mx-auto h-12 w-12 text-muted-foreground" />
              <h3 className="mt-4 text-lg font-semibold">No activity yet</h3>
              <p className="text-muted-foreground">
                Real-time events will appear here as they happen
              </p>
            </div>
          ) : (
            <div className="space-y-1">
              {events.map((event, index) => (
                <div
                  key={event.id}
                  className={`px-6 py-3 border-b last:border-b-0 hover:bg-muted/50 transition-colors ${
                    index === 0 ? 'bg-muted/30' : ''
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-0.5">
                      <div className={`p-1 rounded-full ${
                        getEventColor(event.type, event.data) === 'success' ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-400' :
                        getEventColor(event.type, event.data) === 'destructive' ? 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-400' :
                        getEventColor(event.type, event.data) === 'warning' ? 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-400' :
                        'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                      }`}>
                        {getEventIcon(event.type)}
                      </div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <p className="text-sm font-medium text-foreground">
                          {formatEventMessage(event)}
                        </p>
                        <Badge 
                          variant={getEventColor(event.type, event.data)}
                          className="text-xs"
                        >
                          {event.type.replace('_', ' ')}
                        </Badge>
                      </div>
                      
                      <p className="text-xs text-muted-foreground mt-1">
                        {getEventDetails(event)}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
        
        {events.length >= maxEvents && (
          <div className="px-6 py-2 bg-muted/50 border-t text-center text-xs text-muted-foreground">
            Showing latest {maxEvents} events
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export function RealtimeEventCounter() {
  const { on, off } = useWebSocket()
  const [eventCounts, setEventCounts] = useState({
    scan: 0,
    attendee_update: 0,
    event_update: 0,
    system_status: 0
  })

  useEffect(() => {
    const handleRealtimeEvent = (event: RealtimeEvent) => {
      setEventCounts(prev => ({
        ...prev,
        [event.type]: (prev[event.type as keyof typeof prev] || 0) + 1
      }))
    }

    on('realtime_event', handleRealtimeEvent)

    return () => {
      off('realtime_event', handleRealtimeEvent)
    }
  }, [on, off])

  const totalEvents = Object.values(eventCounts).reduce((sum, count) => sum + count, 0)

  return (
    <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
      <div className="text-center p-2 bg-muted/50 rounded">
        <div className="text-lg font-bold">{totalEvents}</div>
        <div className="text-xs text-muted-foreground">Total</div>
      </div>
      <div className="text-center p-2 bg-muted/50 rounded">
        <div className="text-lg font-bold">{eventCounts.scan}</div>
        <div className="text-xs text-muted-foreground">Scans</div>
      </div>
      <div className="text-center p-2 bg-muted/50 rounded">
        <div className="text-lg font-bold">{eventCounts.attendee_update}</div>
        <div className="text-xs text-muted-foreground">Attendees</div>
      </div>
      <div className="text-center p-2 bg-muted/50 rounded">
        <div className="text-lg font-bold">{eventCounts.event_update}</div>
        <div className="text-xs text-muted-foreground">Events</div>
      </div>
      <div className="text-center p-2 bg-muted/50 rounded">
        <div className="text-lg font-bold">{eventCounts.system_status}</div>
        <div className="text-xs text-muted-foreground">System</div>
      </div>
    </div>
  )
}
