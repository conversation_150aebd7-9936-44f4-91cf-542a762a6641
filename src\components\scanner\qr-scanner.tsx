"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import QrScanner from "qr-scanner"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import {
  Camera,
  CameraOff,
  Upload,
  Flashlight,
  FlashlightOff,
  RotateCcw,
  AlertTriangle,
  RefreshCw,
  Settings,
  Info,
  Zap
} from "lucide-react"
import { SimpleCamera } from "./simple-camera"

interface QRScannerProps {
  onScanResult: (data: string) => void
  onScanError: (error: string) => void
  isActive: boolean
}

type CameraState = 'loading' | 'ready' | 'error' | 'permission-denied' | 'not-found' | 'not-supported'
type ErrorType = 'permission' | 'not-found' | 'not-supported' | 'initialization' | 'unknown'

interface CameraError {
  type: ErrorType
  message: string
  userMessage: string
  troubleshooting: string[]
}

export function QRScanner({ onScanResult, onScanError, isActive }: QRScannerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [qrScanner, setQrScanner] = useState<QrScanner | null>(null)
  const [cameraState, setCameraState] = useState<CameraState>('loading')
  const [cameraError, setCameraError] = useState<CameraError | null>(null)
  const [isFlashlightOn, setIsFlashlightOn] = useState(false)
  const [cameras, setCameras] = useState<QrScanner.Camera[]>([])
  const [selectedCamera, setSelectedCamera] = useState<string>("")
  const [scanCount, setScanCount] = useState(0)
  const [isInitializing, setIsInitializing] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const [useFallbackMode, setUseFallbackMode] = useState(false)

  // Enhanced error classification
  const classifyError = useCallback((error: any): CameraError => {
    const errorMessage = error?.message?.toLowerCase() || error?.toString()?.toLowerCase() || ''

    if (errorMessage.includes('permission') || errorMessage.includes('denied') || errorMessage.includes('notallowed')) {
      return {
        type: 'permission',
        message: error.message || 'Camera permission denied',
        userMessage: 'Camera access was denied',
        troubleshooting: [
          'Click the camera icon in your browser\'s address bar',
          'Select "Allow" for camera access',
          'Refresh the page and try again',
          'Check your browser settings for camera permissions'
        ]
      }
    }

    if (errorMessage.includes('notfound') || errorMessage.includes('no camera') || errorMessage.includes('devicenotfound')) {
      return {
        type: 'not-found',
        message: error.message || 'No camera found',
        userMessage: 'No camera detected on this device',
        troubleshooting: [
          'Make sure your device has a camera',
          'Check if other apps can access the camera',
          'Try connecting an external camera (for desktop)',
          'Use the image upload option instead'
        ]
      }
    }

    if (errorMessage.includes('notsupported') || errorMessage.includes('not supported')) {
      return {
        type: 'not-supported',
        message: error.message || 'Camera not supported',
        userMessage: 'Camera access is not supported in this browser',
        troubleshooting: [
          'Try using Chrome, Firefox, or Safari',
          'Make sure you\'re using HTTPS (required for camera access)',
          'Update your browser to the latest version',
          'Use the image upload option instead'
        ]
      }
    }

    return {
      type: 'unknown',
      message: error.message || 'Unknown camera error',
      userMessage: 'Failed to initialize camera',
      troubleshooting: [
        'Refresh the page and try again',
        'Check if other apps are using the camera',
        'Restart your browser',
        'Use the image upload option instead'
      ]
    }
  }, [])

  // Check browser compatibility
  const checkBrowserSupport = useCallback(() => {
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      setCameraState('not-supported')
      setCameraError({
        type: 'not-supported',
        message: 'MediaDevices API not supported',
        userMessage: 'Your browser doesn\'t support camera access',
        troubleshooting: [
          'Use a modern browser like Chrome, Firefox, or Safari',
          'Make sure you\'re using HTTPS',
          'Update your browser to the latest version'
        ]
      })
      return false
    }
    return true
  }, [])

  // Enhanced camera initialization with better error handling
  const initScanner = useCallback(async () => {
    if (!isActive || !videoRef.current) return

    // Prevent multiple simultaneous initializations
    if (isInitializing) {
      console.log('⏳ Scanner initialization already in progress...')
      return
    }

    setIsInitializing(true)
    setCameraState('loading')
    setCameraError(null)

    try {
      console.log('🎥 Starting QR scanner initialization...')

      // Check browser support first
      if (!checkBrowserSupport()) {
        console.error('❌ Browser not supported')
        return
      }

      // Step 1: Check basic camera access first
      console.log('📷 Checking basic camera access...')
      try {
        const stream = await navigator.mediaDevices.getUserMedia({
          video: {
            facingMode: 'environment',
            width: { ideal: 1280 },
            height: { ideal: 720 }
          }
        })
        console.log('✅ Basic camera access granted')

        // Stop the test stream
        stream.getTracks().forEach(track => track.stop())
      } catch (permissionError) {
        console.error('❌ Camera permission denied:', permissionError)
        setCameraState('permission-denied')
        setCameraError(classifyError(permissionError))
        return
      }

      // Step 2: List available cameras with shorter timeout
      console.log('📋 Listing available cameras...')
      const cameraCheckPromise = QrScanner.listCameras(true)
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Camera detection timeout after 5 seconds')), 5000)
      )

      const availableCameras = await Promise.race([cameraCheckPromise, timeoutPromise]) as QrScanner.Camera[]
      setCameras(availableCameras)

      console.log(`📱 Found ${availableCameras.length} cameras:`, availableCameras.map(c => c.label))

      if (availableCameras.length === 0) {
        console.error('❌ No cameras found')
        setCameraState('not-found')
        setCameraError({
          type: 'not-found',
          message: 'No cameras found',
          userMessage: 'No camera detected on this device',
          troubleshooting: [
            'Make sure your device has a camera',
            'Check camera permissions in browser settings',
            'Try connecting an external camera',
            'Use the image upload option instead'
          ]
        })
        return
      }

      // Step 3: Create scanner instance with enhanced options
      console.log('🔧 Creating QR scanner instance...')
      const scanner = new QrScanner(
        videoRef.current!,
        (result) => {
          console.log('✅ QR code detected:', result.data)
          setScanCount(prev => prev + 1)
          onScanResult(result.data)

          // Provide haptic feedback if available
          if ('vibrate' in navigator) {
            navigator.vibrate(100)
          }
        },
        {
          onDecodeError: (error) => {
            // Don't report decode errors as they're normal when no QR code is visible
            console.debug('🔍 QR decode attempt:', error.message)
          },
          highlightScanRegion: true,
          highlightCodeOutline: true,
          preferredCamera: selectedCamera || 'environment',
          maxScansPerSecond: 3, // Reduced for better performance
          calculateScanRegion: (video) => {
            // Calculate optimal scan region for better performance
            const smallerDimension = Math.min(video.videoWidth, video.videoHeight)
            const scanRegionSize = Math.round(0.6 * smallerDimension) // Smaller region for better focus
            return {
              x: Math.round((video.videoWidth - scanRegionSize) / 2),
              y: Math.round((video.videoHeight - scanRegionSize) / 2),
              width: scanRegionSize,
              height: scanRegionSize,
            }
          }
        }
      )

      console.log('📹 Scanner instance created, starting camera...')
      setQrScanner(scanner)

      // Step 4: Start scanner with shorter timeout and better error handling
      const startPromise = scanner.start()
      const startTimeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Camera start timeout after 8 seconds')), 8000)
      )

      await Promise.race([startPromise, startTimeoutPromise])

      const activeCamera = scanner.getCamera()
      setSelectedCamera(activeCamera?.id || "")
      setCameraState('ready')
      setRetryCount(0)

      console.log('🎉 QR scanner initialized successfully!')
      console.log('📷 Active camera:', activeCamera?.label || 'Default')

    } catch (error) {
      console.error('❌ Scanner initialization failed:', error)
      const classifiedError = classifyError(error)
      setCameraError(classifiedError)

      if (classifiedError.type === 'permission') {
        setCameraState('permission-denied')
      } else {
        setCameraState('error')
      }

      onScanError(classifiedError.userMessage)

      // Auto-retry for certain types of errors
      if (retryCount < 2 && (error.message.includes('timeout') || error.message.includes('NotReadableError'))) {
        console.log(`🔄 Auto-retrying in 2 seconds (attempt ${retryCount + 1}/2)...`)
        setTimeout(() => {
          setRetryCount(prev => prev + 1)
          initScanner()
        }, 2000)
      }
    } finally {
      setIsInitializing(false)
    }
  }, [isActive, selectedCamera, checkBrowserSupport, classifyError, onScanError, retryCount])

  // Add cleanup effect
  useEffect(() => {
    return () => {
      if (qrScanner) {
        console.log('🧹 Cleaning up QR scanner...')
        qrScanner.stop()
        qrScanner.destroy()
      }
    }
  }, [])

  useEffect(() => {
    initScanner()

    return () => {
      if (qrScanner) {
        qrScanner.stop()
        qrScanner.destroy()
      }
    }
  }, [initScanner])

  // Retry mechanism
  const retryInitialization = useCallback(() => {
    if (retryCount < 3) {
      setRetryCount(prev => prev + 1)
      console.log(`Retrying camera initialization (attempt ${retryCount + 1}/3)`)
      initScanner()
    }
  }, [retryCount, initScanner])

  const toggleFlashlight = async () => {
    if (!qrScanner) return

    try {
      if (isFlashlightOn) {
        await qrScanner.turnFlashlightOff()
        setIsFlashlightOn(false)
      } else {
        await qrScanner.turnFlashlightOn()
        setIsFlashlightOn(true)
      }
    } catch (error) {
      onScanError("Flashlight not supported on this device")
    }
  }

  const switchCamera = async () => {
    if (!qrScanner || cameras.length <= 1) return

    try {
      const currentCameraIndex = cameras.findIndex(cam => cam.id === selectedCamera)
      const nextCameraIndex = (currentCameraIndex + 1) % cameras.length
      const nextCamera = cameras[nextCameraIndex]

      console.log(`Switching from ${cameras[currentCameraIndex]?.label} to ${nextCamera.label}`)
      await qrScanner.setCamera(nextCamera.id)
      setSelectedCamera(nextCamera.id)
    } catch (error) {
      console.error('Camera switch error:', error)
      onScanError("Failed to switch camera")
    }
  }

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      onScanError("Please select an image file")
      return
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      onScanError("Image file is too large (max 10MB)")
      return
    }

    try {
      console.log('Scanning uploaded image:', file.name)
      const result = await QrScanner.scanImage(file, {
        returnDetailedScanResult: true,
      })
      onScanResult(result.data)
      setScanCount(prev => prev + 1)
      console.log('QR code found in uploaded image:', result.data)
    } catch (error) {
      console.error('Image scan error:', error)
      onScanError("No QR code found in the uploaded image. Please try a clearer image.")
    }

    // Reset file input
    e.target.value = ""
  }

  // Get browser-specific permission instructions
  const getBrowserInstructions = () => {
    const userAgent = navigator.userAgent.toLowerCase()
    if (userAgent.includes('chrome')) {
      return "In Chrome: Click the camera icon in the address bar and select 'Allow'"
    } else if (userAgent.includes('firefox')) {
      return "In Firefox: Click 'Allow' when prompted for camera access"
    } else if (userAgent.includes('safari')) {
      return "In Safari: Go to Settings > Websites > Camera and allow access"
    } else if (userAgent.includes('edge')) {
      return "In Edge: Click the camera icon in the address bar and select 'Allow'"
    }
    return "Allow camera access when prompted by your browser"
  }

  // Handle fallback camera image capture
  const handleFallbackImageCapture = useCallback(async (imageData: string) => {
    try {
      console.log('🖼️ Processing captured image for QR codes...')

      // Convert base64 to blob for QrScanner.scanImage
      const response = await fetch(imageData)
      const blob = await response.blob()

      const result = await QrScanner.scanImage(blob, {
        returnDetailedScanResult: true,
      })

      onScanResult(result.data)
      setScanCount(prev => prev + 1)
      console.log('✅ QR code found in captured image:', result.data)
    } catch (error) {
      console.error('❌ No QR code found in captured image:', error)
      onScanError("No QR code found in the captured image. Please try again with better lighting.")
    }
  }, [onScanResult, onScanError])

  // Switch to fallback mode
  const switchToFallbackMode = useCallback(() => {
    console.log('🔄 Switching to fallback camera mode...')
    setUseFallbackMode(true)
    setCameraState('ready')
    setCameraError(null)
  }, [])

  // Render error states
  if (cameraState === 'not-supported' || cameraState === 'permission-denied' || cameraState === 'not-found' || cameraState === 'error') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CameraOff className="h-5 w-5" />
            <span>Camera Issue Detected</span>
          </CardTitle>
          <CardDescription>
            {cameraError?.userMessage || "Camera access is required for QR code scanning"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Error Alert */}
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>{cameraError?.userMessage}</strong>
              <br />
              <span className="text-sm text-muted-foreground">
                {getBrowserInstructions()}
              </span>
            </AlertDescription>
          </Alert>

          {/* Troubleshooting Steps */}
          {cameraError?.troubleshooting && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium flex items-center">
                <Settings className="h-4 w-4 mr-2" />
                Troubleshooting Steps:
              </h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                {cameraError.troubleshooting.map((step, index) => (
                  <li key={index} className="flex items-start">
                    <span className="mr-2">{index + 1}.</span>
                    <span>{step}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-2">
            {cameraState !== 'not-supported' && retryCount < 3 && (
              <Button
                onClick={retryInitialization}
                variant="outline"
                disabled={isInitializing}
              >
                <RefreshCw className={`mr-2 h-4 w-4 ${isInitializing ? 'animate-spin' : ''}`} />
                {isInitializing ? 'Retrying...' : `Retry (${retryCount}/3)`}
              </Button>
            )}

            {cameraState !== 'not-supported' && (
              <Button
                onClick={switchToFallbackMode}
                variant="outline"
              >
                <Zap className="mr-2 h-4 w-4" />
                Simple Camera Mode
              </Button>
            )}

            <Button onClick={() => fileInputRef.current?.click()}>
              <Upload className="mr-2 h-4 w-4" />
              Upload QR Code Image
            </Button>

            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>

          {/* Additional Info */}
          <div className="text-xs text-muted-foreground bg-muted p-3 rounded">
            <Info className="h-4 w-4 inline mr-1" />
            <strong>Note:</strong> Camera access requires HTTPS and a modern browser.
            If you continue having issues, try using the image upload option above.
          </div>
        </CardContent>
      </Card>
    )
  }

  // Loading state
  if (cameraState === 'loading' || isInitializing) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Camera className="h-5 w-5" />
            <span>Initializing Camera</span>
          </CardTitle>
          <CardDescription>
            Setting up QR code scanner...
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-center py-8">
            <RefreshCw className="mx-auto h-16 w-16 text-muted-foreground animate-spin" />
            <h3 className="mt-4 text-lg font-semibold">Loading Camera</h3>
            <p className="text-muted-foreground">
              Please wait while we initialize your camera...
            </p>
            {retryCount > 0 && (
              <p className="text-sm text-muted-foreground mt-2">
                Retry attempt {retryCount}/3
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  // Fallback camera mode
  if (useFallbackMode) {
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-blue-600 border-blue-600">
              <Zap className="h-3 w-3 mr-1" />
              Simple Mode
            </Badge>
            <span className="text-sm text-muted-foreground">
              Manual capture mode for better compatibility
            </span>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setUseFallbackMode(false)
              setCameraState('loading')
              initScanner()
            }}
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Try Auto Mode
          </Button>
        </div>

        <SimpleCamera
          onImageCapture={handleFallbackImageCapture}
          onError={onScanError}
          isActive={isActive}
        />
      </div>
    )
  }

  // Working camera UI
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Camera className="h-5 w-5 text-green-600" />
              <span>QR Code Scanner</span>
              <Badge variant="outline" className="text-green-600 border-green-600">
                Ready
              </Badge>
            </CardTitle>
            <CardDescription>
              Point camera at QR code to scan • {scanCount} scans completed
            </CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary">
              {cameras.length} camera{cameras.length !== 1 ? 's' : ''}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Video Container with Enhanced Overlay */}
        <div className="relative aspect-square bg-black rounded-lg overflow-hidden">
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            playsInline
            muted
            autoPlay
          />

          {/* Enhanced Scanning Overlay */}
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="relative">
              {/* Main scanning frame */}
              <div className="w-48 h-48 border-2 border-primary rounded-lg relative">
                {/* Corner indicators */}
                <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-primary rounded-tl-lg"></div>
                <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-primary rounded-tr-lg"></div>
                <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-primary rounded-bl-lg"></div>
                <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-primary rounded-br-lg"></div>

                {/* Scanning line animation */}
                <div className="absolute inset-0 overflow-hidden rounded-lg">
                  <div className="absolute top-0 left-0 right-0 h-0.5 bg-primary animate-pulse"></div>
                </div>
              </div>

              {/* Instructions overlay */}
              <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-white text-sm bg-black bg-opacity-50 px-3 py-1 rounded">
                Position QR code here
              </div>
            </div>
          </div>

          {/* Status indicators */}
          <div className="absolute top-4 left-4 flex space-x-2">
            <Badge variant="secondary" className="bg-black bg-opacity-50 text-white border-none">
              <Camera className="h-3 w-3 mr-1" />
              Live
            </Badge>
            {isFlashlightOn && (
              <Badge variant="secondary" className="bg-yellow-500 bg-opacity-80 text-black border-none">
                <Flashlight className="h-3 w-3 mr-1" />
                Flash
              </Badge>
            )}
          </div>
        </div>

        {/* Enhanced Controls */}
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={toggleFlashlight}
            disabled={!qrScanner}
            title={isFlashlightOn ? "Turn off flashlight" : "Turn on flashlight"}
          >
            {isFlashlightOn ? (
              <FlashlightOff className="h-4 w-4" />
            ) : (
              <Flashlight className="h-4 w-4" />
            )}
          </Button>

          {cameras.length > 1 && (
            <Button
              variant="outline"
              size="sm"
              onClick={switchCamera}
              disabled={!qrScanner}
              title="Switch camera"
            >
              <RotateCcw className="h-4 w-4" />
            </Button>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            title="Upload QR code image"
          >
            <Upload className="h-4 w-4" />
          </Button>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            className="hidden"
          />
        </div>

        {/* Enhanced Instructions */}
        <div className="text-center space-y-2">
          <div className="text-sm text-muted-foreground">
            <p className="font-medium">📱 Position the QR code within the scanning frame</p>
            <p>The scanner will automatically detect and process codes</p>
          </div>

          {/* Tips */}
          <div className="text-xs text-muted-foreground bg-muted p-2 rounded">
            <strong>Tips:</strong> Ensure good lighting • Hold steady • Keep QR code flat
          </div>
        </div>

        {/* Camera Information */}
        <div className="flex justify-between items-center text-xs text-muted-foreground">
          <div>
            📹 {cameras.find(cam => cam.id === selectedCamera)?.label || "Default Camera"}
            {cameras.length > 1 && (
              <span className="ml-2">({cameras.length} available)</span>
            )}
          </div>
          <div>
            ⚡ {scanCount} scans
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
