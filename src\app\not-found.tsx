"use client"

import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Home, ArrowLeft, Search } from "lucide-react"

export default function NotFound() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center space-y-8">
        {/* 404 Illustration */}
        <div className="space-y-4">
          <div className="text-8xl font-bold text-blue-600">404</div>
          <h1 className="text-2xl font-bold text-gray-900">Page Not Found</h1>
          <p className="text-gray-600">
            Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.
          </p>
        </div>

        {/* Search Suggestion */}
        <div className="bg-white rounded-lg p-6 shadow-sm border">
          <div className="flex items-center space-x-2 text-gray-500 mb-2">
            <Search className="h-4 w-4" />
            <span className="text-sm">Looking for something specific?</span>
          </div>
          <div className="text-left space-y-2 text-sm">
            <Link href="/dashboard" className="block text-blue-600 hover:text-blue-800">
              → Dashboard - View your overview
            </Link>
            <Link href="/events" className="block text-blue-600 hover:text-blue-800">
              → Events - Manage your events
            </Link>
            <Link href="/attendees" className="block text-blue-600 hover:text-blue-800">
              → Attendees - Manage attendees
            </Link>
            <Link href="/scanner" className="block text-blue-600 hover:text-blue-800">
              → Scanner - QR code scanning
            </Link>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Button asChild variant="default">
            <Link href="/dashboard">
              <Home className="mr-2 h-4 w-4" />
              Go to Dashboard
            </Link>
          </Button>
          <Button
            variant="outline"
            onClick={() => window.history.back()}
            className="cursor-pointer"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Go Back
          </Button>
        </div>

        {/* Help Text */}
        <div className="text-sm text-gray-500">
          <p>
            If you believe this is an error, please{" "}
            <Link href="/settings" className="text-blue-600 hover:text-blue-800">
              contact support
            </Link>{" "}
            or try refreshing the page.
          </p>
        </div>
      </div>
    </div>
  )
}
