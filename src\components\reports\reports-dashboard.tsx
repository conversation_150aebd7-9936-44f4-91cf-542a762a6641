"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { QuickExportButtons } from "@/components/export/export-dialog"
import { 
  FileText, 
  Calendar, 
  Users, 
  BarChart3,
  Download,
  Filter,
  Search,
  TrendingUp,
  Clock,
  CheckCircle
} from "lucide-react"

// Mock data for demonstration
const mockReports = [
  {
    id: '1',
    name: 'Daily Attendance Report',
    description: 'Comprehensive daily attendance and scanning statistics',
    type: 'attendance',
    lastGenerated: '2024-01-20T14:30:00Z',
    recordCount: 847,
    eventName: 'Tech Conference 2024',
    status: 'ready'
  },
  {
    id: '2',
    name: 'Meal Consumption Analysis',
    description: 'Detailed breakdown of meal type consumption and preferences',
    type: 'analytics',
    lastGenerated: '2024-01-20T12:15:00Z',
    recordCount: 1250,
    eventName: 'Tech Conference 2024',
    status: 'ready'
  },
  {
    id: '3',
    name: 'Event Performance Summary',
    description: 'Overall event metrics and performance indicators',
    type: 'summary',
    lastGenerated: '2024-01-19T18:45:00Z',
    recordCount: 3,
    eventName: 'All Events',
    status: 'ready'
  },
  {
    id: '4',
    name: 'QR Code Usage Report',
    description: 'QR code scanning patterns and device usage statistics',
    type: 'technical',
    lastGenerated: '2024-01-20T10:20:00Z',
    recordCount: 2341,
    eventName: 'Tech Conference 2024',
    status: 'generating'
  }
]

const mockAnalyticsData = {
  eventId: '1',
  eventName: 'Tech Conference 2024',
  totalAttendees: 1250,
  totalScans: 847,
  completionRate: 67.8,
  scansByHour: [
    { hour: '08:00', scans: 12 },
    { hour: '09:00', scans: 28 },
    { hour: '10:00', scans: 45 },
    { hour: '11:00', scans: 67 },
    { hour: '12:00', scans: 134 },
    { hour: '13:00', scans: 156 },
    { hour: '14:00', scans: 98 },
    { hour: '15:00', scans: 76 },
    { hour: '16:00', scans: 54 },
    { hour: '17:00', scans: 32 }
  ],
  mealTypeDistribution: [
    { mealType: 'Main Course', count: 450, percentage: 53.1 },
    { mealType: 'Appetizer', count: 320, percentage: 37.8 },
    { mealType: 'Dessert', count: 280, percentage: 33.1 },
    { mealType: 'Beverage', count: 520, percentage: 61.4 }
  ],
  dailyTrends: [
    { date: '2024-01-15', scans: 234, completion: 78 },
    { date: '2024-01-16', scans: 267, completion: 82 },
    { date: '2024-01-17', scans: 298, completion: 85 },
    { date: '2024-01-18', scans: 312, completion: 89 },
    { date: '2024-01-19', scans: 345, completion: 92 },
    { date: '2024-01-20', scans: 378, completion: 95 }
  ]
}

export function ReportsDashboard() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedType, setSelectedType] = useState<string>("all")
  const [reports] = useState(mockReports)

  const filteredReports = reports.filter(report => {
    const matchesSearch = report.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         report.description.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = selectedType === "all" || report.type === selectedType
    return matchesSearch && matchesType
  })

  const reportTypes = [
    { value: 'all', label: 'All Reports' },
    { value: 'attendance', label: 'Attendance' },
    { value: 'analytics', label: 'Analytics' },
    { value: 'summary', label: 'Summary' },
    { value: 'technical', label: 'Technical' }
  ]

  const getReportIcon = (type: string) => {
    switch (type) {
      case 'attendance': return <Users className="h-4 w-4" />
      case 'analytics': return <BarChart3 className="h-4 w-4" />
      case 'summary': return <FileText className="h-4 w-4" />
      case 'technical': return <Clock className="h-4 w-4" />
      default: return <FileText className="h-4 w-4" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ready':
        return <Badge variant="success">Ready</Badge>
      case 'generating':
        return <Badge variant="warning">Generating</Badge>
      case 'error':
        return <Badge variant="destructive">Error</Badge>
      default:
        return <Badge variant="secondary">Unknown</Badge>
    }
  }

  const formatLastGenerated = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    return date.toLocaleDateString()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Reports & Analytics</h2>
          <p className="text-muted-foreground">
            Generate and export comprehensive reports for your events
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Calendar className="mr-2 h-4 w-4" />
            Schedule Report
          </Button>
          <Button>
            <FileText className="mr-2 h-4 w-4" />
            Create Custom Report
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Available Reports</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{reports.length}</div>
            <p className="text-xs text-muted-foreground">
              {reports.filter(r => r.status === 'ready').length} ready
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Records</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {reports.reduce((sum, r) => sum + r.recordCount, 0).toLocaleString()}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all reports
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Updated</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">2h ago</div>
            <p className="text-xs text-muted-foreground">
              Auto-refresh enabled
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Export Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">98.5%</div>
            <p className="text-xs text-muted-foreground">
              Success rate
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search reports..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <select
          value={selectedType}
          onChange={(e) => setSelectedType(e.target.value)}
          className="px-3 py-2 border border-input bg-background rounded-md text-sm"
        >
          {reportTypes.map((type) => (
            <option key={type.value} value={type.value}>
              {type.label}
            </option>
          ))}
        </select>

        <Button variant="outline" size="sm">
          <Filter className="mr-2 h-4 w-4" />
          More Filters
        </Button>
      </div>

      {/* Reports Grid */}
      <div className="grid gap-6 md:grid-cols-2">
        {filteredReports.map((report) => (
          <Card key={report.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-1">
                  <div className="flex items-center space-x-2">
                    {getReportIcon(report.type)}
                    <CardTitle className="text-lg">{report.name}</CardTitle>
                  </div>
                  <CardDescription className="line-clamp-2">
                    {report.description}
                  </CardDescription>
                </div>
                {getStatusBadge(report.status)}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <div className="flex items-center space-x-4">
                  <span>{report.recordCount.toLocaleString()} records</span>
                  <span>•</span>
                  <span>{report.eventName}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <span>Last generated: {formatLastGenerated(report.lastGenerated)}</span>
                <div className="flex items-center space-x-1">
                  <CheckCircle className="h-3 w-3 text-green-500" />
                  <span>Up to date</span>
                </div>
              </div>

              {/* Quick Export Buttons */}
              <div className="pt-4 border-t">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Quick Export:</span>
                  <QuickExportButtons
                    type={report.type === 'attendance' ? 'attendees' : 'analytics'}
                    data={report.type === 'attendance' ? [] : mockAnalyticsData}
                    eventName={report.eventName}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredReports.length === 0 && (
        <div className="text-center py-12">
          <FileText className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-semibold">No reports found</h3>
          <p className="text-muted-foreground">
            {searchTerm || selectedType !== "all" 
              ? "Try adjusting your search or filter criteria" 
              : "Create your first report to get started"
            }
          </p>
        </div>
      )}
    </div>
  )
}
