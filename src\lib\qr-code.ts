import QRCode from 'qrcode'

export interface QRCodeOptions {
  size?: number
  margin?: number
  color?: {
    dark?: string
    light?: string
  }
  errorCorrectionLevel?: 'L' | 'M' | 'Q' | 'H'
}

export interface AttendeeQRData {
  attendeeId: string
  eventId: string
  qrCode: string
  name: string
  email?: string
}

/**
 * Generate a unique QR code string for an attendee
 */
export function generateQRCodeString(eventId: string, attendeeId: string): string {
  const timestamp = Date.now().toString(36)
  const random = Math.random().toString(36).substring(2, 8)
  return `${eventId}-${attendeeId}-${timestamp}-${random}`.toUpperCase()
}

/**
 * Generate QR code as data URL (base64 image)
 */
export async function generateQRCodeDataURL(
  data: string | AttendeeQRData,
  options: QRCodeOptions = {}
): Promise<string> {
  const qrData = typeof data === 'string' ? data : JSON.stringify(data)
  
  const qrOptions = {
    width: options.size || 200,
    margin: options.margin || 2,
    color: {
      dark: options.color?.dark || '#000000',
      light: options.color?.light || '#FFFFFF'
    },
    errorCorrectionLevel: options.errorCorrectionLevel || 'M' as const
  }

  try {
    return await QRCode.toDataURL(qrData, qrOptions)
  } catch (error) {
    console.error('Error generating QR code:', error)
    throw new Error('Failed to generate QR code')
  }
}

/**
 * Generate QR code as SVG string
 */
export async function generateQRCodeSVG(
  data: string | AttendeeQRData,
  options: QRCodeOptions = {}
): Promise<string> {
  const qrData = typeof data === 'string' ? data : JSON.stringify(data)
  
  const qrOptions = {
    width: options.size || 200,
    margin: options.margin || 2,
    color: {
      dark: options.color?.dark || '#000000',
      light: options.color?.light || '#FFFFFF'
    },
    errorCorrectionLevel: options.errorCorrectionLevel || 'M' as const
  }

  try {
    return await QRCode.toString(qrData, { 
      ...qrOptions, 
      type: 'svg' 
    })
  } catch (error) {
    console.error('Error generating QR code SVG:', error)
    throw new Error('Failed to generate QR code SVG')
  }
}

/**
 * Generate QR code as buffer for server-side processing
 */
export async function generateQRCodeBuffer(
  data: string | AttendeeQRData,
  options: QRCodeOptions = {}
): Promise<Buffer> {
  const qrData = typeof data === 'string' ? data : JSON.stringify(data)
  
  const qrOptions = {
    width: options.size || 200,
    margin: options.margin || 2,
    color: {
      dark: options.color?.dark || '#000000',
      light: options.color?.light || '#FFFFFF'
    },
    errorCorrectionLevel: options.errorCorrectionLevel || 'M' as const
  }

  try {
    return await QRCode.toBuffer(qrData, qrOptions)
  } catch (error) {
    console.error('Error generating QR code buffer:', error)
    throw new Error('Failed to generate QR code buffer')
  }
}

/**
 * Validate QR code data format
 */
export function validateQRCodeData(data: string): boolean {
  try {
    // Try to parse as JSON first (for structured data)
    JSON.parse(data)
    return true
  } catch {
    // If not JSON, check if it's a valid QR code string format
    const qrCodePattern = /^[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+$/
    return qrCodePattern.test(data)
  }
}

/**
 * Parse QR code data to extract attendee information
 */
export function parseQRCodeData(data: string): AttendeeQRData | string {
  try {
    // Try to parse as JSON first
    const parsed = JSON.parse(data)
    if (parsed.attendeeId && parsed.eventId && parsed.qrCode) {
      return parsed as AttendeeQRData
    }
    return data
  } catch {
    // Return as string if not JSON
    return data
  }
}

/**
 * Generate batch QR codes for multiple attendees
 */
export async function generateBatchQRCodes(
  attendees: Array<{
    id: string
    name: string
    email?: string
    eventId: string
  }>,
  options: QRCodeOptions = {}
): Promise<Array<{
  attendeeId: string
  qrCode: string
  dataURL: string
  svg: string
}>> {
  const results = []

  for (const attendee of attendees) {
    const qrCodeString = generateQRCodeString(attendee.eventId, attendee.id)
    const qrData: AttendeeQRData = {
      attendeeId: attendee.id,
      eventId: attendee.eventId,
      qrCode: qrCodeString,
      name: attendee.name,
      email: attendee.email
    }

    try {
      const [dataURL, svg] = await Promise.all([
        generateQRCodeDataURL(qrData, options),
        generateQRCodeSVG(qrData, options)
      ])

      results.push({
        attendeeId: attendee.id,
        qrCode: qrCodeString,
        dataURL,
        svg
      })
    } catch (error) {
      console.error(`Error generating QR code for attendee ${attendee.id}:`, error)
      // Continue with other attendees even if one fails
    }
  }

  return results
}

/**
 * Create printable QR code with attendee information
 */
export function createPrintableQRCode(
  attendee: {
    name: string
    email?: string
    qrCode: string
    eventName: string
  },
  qrCodeDataURL: string
): string {
  return `
    <div style="
      width: 300px; 
      padding: 20px; 
      border: 2px solid #000; 
      text-align: center; 
      font-family: Arial, sans-serif;
      page-break-inside: avoid;
      margin: 10px;
    ">
      <h3 style="margin: 0 0 10px 0; font-size: 18px;">${attendee.eventName}</h3>
      <img src="${qrCodeDataURL}" alt="QR Code" style="width: 150px; height: 150px; margin: 10px 0;" />
      <div style="margin-top: 10px;">
        <div style="font-weight: bold; font-size: 16px;">${attendee.name}</div>
        ${attendee.email ? `<div style="font-size: 12px; color: #666;">${attendee.email}</div>` : ''}
        <div style="font-size: 10px; color: #999; margin-top: 5px;">${attendee.qrCode}</div>
      </div>
    </div>
  `
}
