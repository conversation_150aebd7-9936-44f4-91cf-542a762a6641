const BASE_URL = 'http://localhost:3000';

// Test credentials
const ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123'
};

const SCANNER_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'scanner123'
};

async function testAPIEndpoints() {
  const { default: fetch } = await import('node-fetch');
  console.log('🌐 Testing API Endpoints with MongoDB Integration...\n');
  
  try {
    // 1. Test Authentication
    console.log('1️⃣ Testing AUTHENTICATION...');
    
    // Test admin login
    const adminLoginResponse = await fetch(`${BASE_URL}/api/auth/signin`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(ADMIN_CREDENTIALS)
    });
    
    if (adminLoginResponse.ok) {
      console.log('✅ Admin login successful');
    } else {
      console.log('❌ Admin login failed:', adminLoginResponse.status);
    }
    
    // Test scanner login
    const scannerLoginResponse = await fetch(`${BASE_URL}/api/auth/signin`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(SCANNER_CREDENTIALS)
    });
    
    if (scannerLoginResponse.ok) {
      console.log('✅ Scanner login successful');
    } else {
      console.log('❌ Scanner login failed:', scannerLoginResponse.status);
    }
    
    // 2. Test Events API (without authentication for now - would need session handling)
    console.log('\n2️⃣ Testing EVENTS API...');
    
    // Test GET events
    const eventsResponse = await fetch(`${BASE_URL}/api/events`);
    if (eventsResponse.ok) {
      const events = await eventsResponse.json();
      console.log(`✅ GET /api/events successful - Found ${events.length} events`);
      events.forEach(event => {
        console.log(`   - ${event.name} (${event.attendees?.length || 0} attendees)`);
      });
    } else {
      console.log('❌ GET /api/events failed:', eventsResponse.status);
    }
    
    // 3. Test Attendees API
    console.log('\n3️⃣ Testing ATTENDEES API...');
    
    const attendeesResponse = await fetch(`${BASE_URL}/api/attendees`);
    if (attendeesResponse.ok) {
      const attendees = await attendeesResponse.json();
      console.log(`✅ GET /api/attendees successful - Found ${attendees.length} attendees`);
      attendees.forEach(attendee => {
        console.log(`   - ${attendee.name} (${attendee.email}) - QR: ${attendee.qrCode}`);
      });
    } else {
      console.log('❌ GET /api/attendees failed:', attendeesResponse.status);
    }
    
    // 4. Test Analytics API
    console.log('\n4️⃣ Testing ANALYTICS API...');
    
    const analyticsResponse = await fetch(`${BASE_URL}/api/analytics`);
    if (analyticsResponse.ok) {
      const analytics = await analyticsResponse.json();
      console.log('✅ GET /api/analytics successful');
      console.log('   Analytics data:', JSON.stringify(analytics, null, 2));
    } else {
      console.log('❌ GET /api/analytics failed:', analyticsResponse.status);
    }
    
    // 5. Test Scanner API (simulate QR scan)
    console.log('\n5️⃣ Testing SCANNER API...');
    
    // First get a valid QR code from attendees
    if (attendeesResponse.ok) {
      const attendees = await attendeesResponse.json();
      if (attendees.length > 0) {
        const testQRCode = attendees[0].qrCode;
        console.log(`   Testing with QR code: ${testQRCode}`);
        
        const scanResponse = await fetch(`${BASE_URL}/api/scanner/scan`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ qrCode: testQRCode })
        });
        
        if (scanResponse.ok) {
          const scanResult = await scanResponse.json();
          console.log('✅ POST /api/scanner/scan successful');
          console.log(`   Scanned: ${scanResult.attendee?.name}`);
          console.log(`   Success: ${scanResult.success}`);
          console.log(`   Message: ${scanResult.message}`);
        } else {
          const errorResult = await scanResponse.json();
          console.log('❌ POST /api/scanner/scan failed:', scanResponse.status);
          console.log('   Error:', errorResult.error);
        }
      }
    }
    
    // 6. Test Health Check
    console.log('\n6️⃣ Testing HEALTH CHECK...');
    
    const healthResponse = await fetch(`${BASE_URL}/api/health`);
    if (healthResponse.ok) {
      const health = await healthResponse.json();
      console.log('✅ GET /api/health successful');
      console.log('   Status:', health.status);
      console.log('   Database:', health.database);
    } else {
      console.log('❌ GET /api/health failed:', healthResponse.status);
    }
    
    // 7. Test Page Accessibility
    console.log('\n7️⃣ Testing PAGE ACCESSIBILITY...');
    
    const pages = [
      '/',
      '/auth/signin',
      '/dashboard',
      '/events',
      '/attendees',
      '/settings',
      '/scanner'
    ];
    
    for (const page of pages) {
      const pageResponse = await fetch(`${BASE_URL}${page}`);
      if (pageResponse.ok) {
        console.log(`✅ GET ${page} - Status: ${pageResponse.status}`);
      } else {
        console.log(`❌ GET ${page} - Status: ${pageResponse.status}`);
      }
    }
    
    // 8. Test Mobile Compatibility Headers
    console.log('\n8️⃣ Testing MOBILE COMPATIBILITY...');
    
    const mobileHeaders = {
      'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1'
    };
    
    const mobileResponse = await fetch(`${BASE_URL}/scanner`, {
      headers: mobileHeaders
    });
    
    if (mobileResponse.ok) {
      console.log('✅ Mobile scanner page accessible');
      const contentType = mobileResponse.headers.get('content-type');
      console.log('   Content-Type:', contentType);
    } else {
      console.log('❌ Mobile scanner page failed:', mobileResponse.status);
    }
    
    // 9. Test API Response Times
    console.log('\n9️⃣ Testing API RESPONSE TIMES...');
    
    const apiEndpoints = [
      '/api/events',
      '/api/attendees',
      '/api/analytics'
    ];
    
    for (const endpoint of apiEndpoints) {
      const startTime = Date.now();
      const response = await fetch(`${BASE_URL}${endpoint}`);
      const endTime = Date.now();
      const responseTime = endTime - startTime;
      
      if (response.ok) {
        console.log(`✅ ${endpoint} - Response time: ${responseTime}ms`);
      } else {
        console.log(`❌ ${endpoint} - Failed (${response.status}) - Response time: ${responseTime}ms`);
      }
    }
    
    console.log('\n🎉 API endpoint testing completed!');
    
  } catch (error) {
    console.error('❌ Error during API testing:', error);
  }
}

// Run the test
testAPIEndpoints().catch(console.error);
