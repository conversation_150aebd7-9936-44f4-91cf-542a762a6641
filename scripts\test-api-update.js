const { MongoClient } = require('mongodb');

const url = 'mongodb://localhost:27017';
const dbName = 'foodscan';

async function testAPIUpdate() {
  const client = new MongoClient(url);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db(dbName);
    const eventsCollection = db.collection('events');
    
    // Find an existing event to test update
    const existingEvent = await eventsCollection.findOne({});
    
    if (!existingEvent) {
      console.log('❌ No events found in database');
      return;
    }
    
    console.log('✅ Found event to test:', existingEvent.name);
    console.log('Event ID:', existingEvent._id.toString());
    
    // Test the exact data structure that would be sent from the frontend
    const updateData = {
      name: 'Updated Event Name',
      description: 'Updated description',
      startDate: '2024-03-15T08:00:00.000Z',
      endDate: '2024-03-15T18:00:00.000Z',
      serviceStartTime: '1970-01-01T12:00:00.000Z',
      serviceEndTime: '1970-01-01T14:00:00.000Z',
      primaryColor: '#ff6b6b',
      secondaryColor: '#ee5a52',
      logoUrl: '',
      isActive: true,
      mealTypes: [
        {
          name: 'Test Main Course',
          description: 'Test description',
          maxQuantityPerAttendee: 1,
          totalQuantity: 100,
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false
        }
      ]
    };
    
    console.log('\n📡 Testing API endpoint...');
    console.log('URL:', `http://localhost:3000/api/events/${existingEvent._id.toString()}`);
    
    // Test with fetch (simulating frontend request)
    try {
      const response = await fetch(`http://localhost:3000/api/events/${existingEvent._id.toString()}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData)
      });
      
      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));
      
      const responseText = await response.text();
      console.log('Response body:', responseText);
      
      if (response.ok) {
        console.log('✅ API update successful');
        try {
          const responseData = JSON.parse(responseText);
          console.log('Updated event name:', responseData.name);
        } catch (parseError) {
          console.log('⚠️ Could not parse response as JSON');
        }
      } else {
        console.log('❌ API update failed');
        try {
          const errorData = JSON.parse(responseText);
          console.log('Error details:', errorData);
        } catch (parseError) {
          console.log('Raw error response:', responseText);
        }
      }
      
    } catch (fetchError) {
      console.log('❌ Fetch error:', fetchError.message);
      console.log('This might mean the server is not running on localhost:3000');
    }
    
  } catch (error) {
    console.error('❌ Error during test:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testAPIUpdate().catch(console.error);
