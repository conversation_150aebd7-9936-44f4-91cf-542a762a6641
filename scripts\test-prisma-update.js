const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testPrismaUpdate() {
  try {
    console.log('🔗 Testing Prisma connection...');
    
    // Test basic connection
    await prisma.$connect();
    console.log('✅ Prisma connected successfully');
    
    // Find an existing event
    console.log('\n📋 Finding existing event...');
    const existingEvent = await prisma.event.findFirst();
    
    if (!existingEvent) {
      console.log('❌ No events found in database');
      return;
    }
    
    console.log('✅ Found event:', existingEvent.name);
    console.log('Event ID:', existingEvent.id);
    console.log('Event ID type:', typeof existingEvent.id);
    console.log('Event ID length:', existingEvent.id.length);
    
    // Test findUnique with the ID
    console.log('\n🔍 Testing findUnique...');
    try {
      const foundEvent = await prisma.event.findUnique({
        where: { id: existingEvent.id }
      });
      
      if (foundEvent) {
        console.log('✅ findUnique successful:', foundEvent.name);
      } else {
        console.log('❌ findUnique returned null');
      }
    } catch (findError) {
      console.log('❌ findUnique error:', findError.message);
      console.log('Error code:', findError.code);
    }
    
    // Test update operation
    console.log('\n🔄 Testing update operation...');
    try {
      const updatedEvent = await prisma.event.update({
        where: { id: existingEvent.id },
        data: {
          name: existingEvent.name + ' (Prisma Test)',
          description: 'Updated via Prisma test',
          primaryColor: '#ff0000',
        }
      });
      
      console.log('✅ Update successful:', updatedEvent.name);
      
      // Revert the change
      await prisma.event.update({
        where: { id: existingEvent.id },
        data: {
          name: existingEvent.name,
          description: existingEvent.description,
          primaryColor: existingEvent.primaryColor,
        }
      });
      console.log('✅ Reverted changes');
      
    } catch (updateError) {
      console.log('❌ Update error:', updateError.message);
      console.log('Error code:', updateError.code);
      console.log('Error meta:', updateError.meta);
    }
    
    // Test meal types operations
    console.log('\n🍽️ Testing meal types operations...');
    try {
      const mealTypes = await prisma.mealType.findMany({
        where: { eventId: existingEvent.id }
      });
      
      console.log('✅ Found meal types:', mealTypes.length);
      
      if (mealTypes.length > 0) {
        console.log('First meal type:', mealTypes[0].name);
      }
      
    } catch (mealTypesError) {
      console.log('❌ Meal types error:', mealTypesError.message);
    }
    
    console.log('\n✅ All Prisma tests completed');
    
  } catch (error) {
    console.error('❌ Prisma test error:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code,
      meta: error.meta,
      stack: error.stack
    });
  } finally {
    await prisma.$disconnect();
    console.log('🔌 Prisma disconnected');
  }
}

// Run the test
testPrismaUpdate().catch(console.error);
