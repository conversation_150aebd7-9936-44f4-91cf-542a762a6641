import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { generateQRCodeDataURL } from "@/lib/qr-code"

export async function GET(
  request: NextRequest,
  { params }: { params: { attendeeId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const attendee = await prisma.attendee.findUnique({
      where: { id: params.attendeeId },
      include: {
        event: {
          select: {
            name: true,
            primaryColor: true
          }
        }
      }
    })

    if (!attendee) {
      return NextResponse.json({ error: "Attendee not found" }, { status: 404 })
    }

    // Generate QR code
    const qrCodeDataURL = await generateQRCodeDataURL(attendee.qrCode, {
      size: 300,
      margin: 2,
      color: {
        dark: attendee.event.primaryColor || '#000000',
        light: '#FFFFFF'
      }
    })

    return NextResponse.json({
      qrCode: qrCodeDataURL,
      attendee: {
        id: attendee.id,
        name: attendee.name,
        email: attendee.email,
        qrCodeValue: attendee.qrCode,
        eventName: attendee.event.name
      }
    })

  } catch (error) {
    console.error("Error generating QR code:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
