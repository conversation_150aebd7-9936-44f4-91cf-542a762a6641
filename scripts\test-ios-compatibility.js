const { MongoClient } = require('mongodb');

const MONGODB_URI = "mongodb://localhost:27017";
const DB_NAME = "foodscan";

async function testIOSCompatibility() {
  console.log('🍎 Testing iOS & iPhone Compatibility...\n');
  
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db(DB_NAME);
    const attendeesCollection = db.collection('attendees');
    
    // 1. Verify QR codes for iOS testing
    console.log('1️⃣ Verifying QR CODES for iOS testing...');
    const attendees = await attendeesCollection.find({}).toArray();
    
    if (attendees.length === 0) {
      console.log('❌ No attendees found. Please run the seed script first.');
      return;
    }
    
    console.log(`✅ Found ${attendees.length} attendees with QR codes for iOS testing:`);
    attendees.forEach(attendee => {
      console.log(`   - ${attendee.name}: ${attendee.qrCode}`);
    });
    
    // 2. Test QR code validation performance (important for mobile)
    console.log('\n2️⃣ Testing QR CODE LOOKUP PERFORMANCE (Mobile Optimized)...');
    
    const mobilePerformanceTests = 25; // Reduced for mobile testing
    const startTime = Date.now();
    
    for (let i = 0; i < mobilePerformanceTests; i++) {
      const randomAttendee = attendees[Math.floor(Math.random() * attendees.length)];
      await attendeesCollection.findOne({ qrCode: randomAttendee.qrCode });
    }
    
    const endTime = Date.now();
    const avgTime = (endTime - startTime) / mobilePerformanceTests;
    
    console.log(`✅ Performed ${mobilePerformanceTests} QR code lookups`);
    console.log(`   Average time: ${avgTime.toFixed(2)}ms`);
    
    if (avgTime < 15) {
      console.log('   🚀 Excellent performance for mobile devices!');
    } else if (avgTime < 30) {
      console.log('   ✅ Good performance for mobile scanning');
    } else {
      console.log('   ⚠️ Performance may be slow on older mobile devices');
    }
    
    // 3. Generate iOS testing instructions
    console.log('\n3️⃣ Generating iOS TESTING INSTRUCTIONS...');
    
    const iosInstructions = `
# 🍎 iOS & iPhone Compatibility Testing Guide

## 📱 Enhanced iOS Features Implemented:

### 1. iOS Safari Compatibility
- ✅ User gesture requirement handling
- ✅ iOS Safari video element optimizations
- ✅ Proper playsInline attribute for iOS
- ✅ iOS-specific camera constraints
- ✅ Safari permission handling

### 2. iPhone-Specific Optimizations
- ✅ Optimized camera resolution for iPhone
- ✅ Reduced scan rate for better performance
- ✅ iOS haptic feedback integration
- ✅ iPhone screen size optimizations
- ✅ Memory usage optimizations for iOS

### 3. Mobile Touch Controls
- ✅ Touch-friendly button sizes
- ✅ Orientation change handling
- ✅ Camera switching for front/back cameras
- ✅ iOS-specific error messages
- ✅ File upload with iOS size limits

## 🧪 iOS Testing Steps:

### Step 1: iPhone Device Testing
1. Open Safari on iPhone (iOS 15+ recommended)
2. Navigate to: https://your-domain.com/scanner
3. Tap "Start Camera" button (required for iOS)
4. Allow camera access when prompted
5. Test QR code scanning with these codes:
   - JD-2024-001 (John Doe)
   - JS-2024-002 (Jane Smith)
   - MJ-2024-003 (Mike Johnson)

### Step 2: iOS Safari Specific Tests
- ✅ Test in portrait orientation
- ✅ Test in landscape orientation
- ✅ Test camera switching (if multiple cameras)
- ✅ Test flashlight toggle
- ✅ Test image upload fallback
- ✅ Test with different lighting conditions

### Step 3: iPhone Model Testing
Test on various iPhone models:
- ✅ iPhone 12 series (iOS 15+)
- ✅ iPhone 13 series (iOS 16+)
- ✅ iPhone 14 series (iOS 16+)
- ✅ iPhone 15 series (iOS 17+)

### Step 4: iOS Version Testing
- ✅ iOS 15.x (minimum supported)
- ✅ iOS 16.x (recommended)
- ✅ iOS 17.x (latest features)

## 📋 Expected iOS Behaviors:

### Success Cases:
- "Start Camera" button appears on page load
- Camera initializes within 5-10 seconds after tap
- QR codes detected within 1-2 seconds
- Smooth orientation changes
- Haptic feedback on successful scans

### iOS-Specific Features:
- User gesture required before camera access
- iOS version detection and display
- iPhone-optimized scan region
- iOS Safari permission guidance
- Memory-optimized image processing

## 🚨 iOS Troubleshooting:

### If Camera Doesn't Start:
1. Check Safari Settings → Camera → Allow
2. Ensure no other apps are using camera
3. Try refreshing the page
4. Use image upload as fallback

### If QR Codes Not Detected:
1. Ensure good lighting
2. Hold iPhone steady
3. Try different distances from QR code
4. Test in both orientations
5. Use flashlight if available

### If Performance Issues:
1. Close other Safari tabs
2. Restart Safari app
3. Check available device storage
4. Try airplane mode toggle

## 📊 Performance Expectations (iOS):

- Camera initialization: 5-10 seconds
- QR code detection: 1-2 seconds
- Database lookup: < 30ms
- Memory usage: < 50MB
- Battery impact: Minimal

## 🔧 iOS-Specific Error Messages:

The scanner now provides iOS-specific guidance:
- Camera permission denied → Safari settings instructions
- Camera not found → Device compatibility check
- Camera in use → Other app conflict resolution
- Memory issues → iOS-specific optimization tips

## 📱 Mobile UI Optimizations:

- Touch-friendly button sizes (44px minimum)
- Optimized for iPhone screen sizes
- Proper viewport meta tag handling
- iOS-safe area considerations
- Landscape/portrait adaptations

## 🎯 Integration Testing:

Verify these work on iOS:
- ✅ QR code scanning → Database lookup
- ✅ Scan result display
- ✅ Error handling and fallbacks
- ✅ Scan history logging
- ✅ Backend API integration

## 📝 Testing Checklist:

### Pre-Testing:
- [ ] iPhone device available (iOS 15+)
- [ ] Safari browser (not Chrome/Firefox)
- [ ] Good lighting conditions
- [ ] Test QR codes ready
- [ ] Backend server running

### During Testing:
- [ ] "Start Camera" button works
- [ ] Camera permission granted
- [ ] Video feed appears
- [ ] QR codes detected quickly
- [ ] Scan results displayed correctly
- [ ] Error handling works properly
- [ ] Fallback options available

### Post-Testing:
- [ ] Check browser console for errors
- [ ] Verify database scan logs
- [ ] Test different iPhone orientations
- [ ] Confirm memory usage is reasonable
- [ ] Document any issues found

## 🎉 Success Criteria:

The iOS scanner is successful if:
1. ✅ Works on iPhone Safari without issues
2. ✅ Detects QR codes reliably
3. ✅ Handles iOS-specific limitations gracefully
4. ✅ Provides clear error messages and fallbacks
5. ✅ Maintains good performance on mobile devices
6. ✅ Integrates properly with existing backend
`;

    console.log(iosInstructions);
    
    // 4. Test scanner API endpoint for mobile
    console.log('\n4️⃣ Testing SCANNER API for mobile integration...');
    
    try {
      const { default: fetch } = await import('node-fetch');
      
      // Test with valid QR code
      const testQRCode = attendees[0].qrCode;
      console.log(`Testing mobile API with QR code: ${testQRCode}`);
      
      const scanResponse = await fetch('http://localhost:3000/api/scanner/scan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ qrCode: testQRCode })
      });
      
      if (scanResponse.ok) {
        const result = await scanResponse.json();
        console.log('✅ Mobile API integration working:');
        console.log(`   Success: ${result.success}`);
        console.log(`   Message: ${result.message}`);
        console.log(`   Attendee: ${result.attendee?.name}`);
      } else {
        const error = await scanResponse.json();
        console.log('❌ Mobile API failed:', error.error);
      }
      
    } catch (error) {
      console.log('❌ Could not test mobile API:', error.message);
    }
    
    console.log('\n🎉 iOS compatibility testing setup completed!');
    console.log('📱 Test on iPhone Safari: http://localhost:3000/scanner');
    console.log('🔍 Check browser console for detailed iOS logs');
    
  } catch (error) {
    console.error('❌ Error during iOS testing:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testIOSCompatibility().catch(console.error);
