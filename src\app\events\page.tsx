"use client"

import { useState, useEffect } from "react"
import { useSession } from "next-auth/react"
import { redirect } from "next/navigation"
import { AppLayout } from "@/components/layout/app-layout"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { Input } from "@/components/ui/input"
import { EventFormDialog } from "@/components/events/event-form-dialog"
import { useToast } from "@/hooks/use-toast"
import {
  Plus,
  Search,
  Calendar,
  Users,
  Settings,
  Eye,
  Edit,
  Trash2,
  Copy,
  AlertCircle
} from "lucide-react"

// Mock data - will be replaced with real data later
const mockEvents = [
  {
    id: "1",
    name: "Tech Conference 2024",
    description: "Annual technology conference with industry leaders",
    startDate: "2024-03-15",
    endDate: "2024-03-17",
    isActive: true,
    attendeeCount: 850,
    logoUrl: null,
    primaryColor: "#3b82f6",
    secondaryColor: "#1e40af"
  },
  {
    id: "2",
    name: "Company Holiday Party",
    description: "End of year celebration for all employees",
    startDate: "2024-12-20",
    endDate: "2024-12-20",
    isActive: false,
    attendeeCount: 200,
    logoUrl: null,
    primaryColor: "#10b981",
    secondaryColor: "#059669"
  },
  {
    id: "3",
    name: "Product Launch Event",
    description: "Launch event for our new product line",
    startDate: "2024-02-10",
    endDate: "2024-02-10",
    isActive: false,
    attendeeCount: 300,
    logoUrl: null,
    primaryColor: "#f59e0b",
    secondaryColor: "#d97706"
  }
]

export default function EventsPage() {
  const { data: session, status } = useSession()
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [events, setEvents] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showEventDialog, setShowEventDialog] = useState(false)
  const [editingEvent, setEditingEvent] = useState<any>(null)
  const [mounted, setMounted] = useState(false)

  // Ensure component is mounted before rendering
  useEffect(() => {
    setMounted(true)
  }, [])

  // Fetch events when component mounts and user is authenticated
  useEffect(() => {
    if (mounted && session && session.user.role === "ADMIN") {
      fetchEvents()
    }
  }, [mounted, session])

  // Handle loading and authentication states
  if (status === "loading" || !mounted) {
    return (
      <AppLayout>
        <div className="flex h-screen items-center justify-center">
          <LoadingSpinner size="lg" />
        </div>
      </AppLayout>
    )
  }

  if (!session) {
    redirect("/auth/signin")
    return null
  }

  if (session.user.role !== "ADMIN") {
    redirect("/dashboard")
    return null
  }

  const fetchEvents = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await fetch('/api/events')

      if (response.ok) {
        const data = await response.json()
        setEvents(data)
      } else {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || `Failed to fetch events (${response.status})`
        setError(errorMessage)
        console.error('Failed to fetch events:', errorMessage)
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Network error occurred'
      setError(errorMessage)
      console.error('Error fetching events:', error)
    } finally {
      setLoading(false)
    }
  }

  const filteredEvents = events.filter(event =>
    event.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.description.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    })
  }

  const handleEventSaved = (savedEvent: any) => {
    if (editingEvent && editingEvent.id) {
      // Update existing event
      setEvents(prev => prev.map(event =>
        event.id === savedEvent.id ? savedEvent : event
      ))
      toast({
        title: "Event updated",
        description: `${savedEvent.name} has been updated successfully.`,
        variant: "default",
      })
    } else {
      // Add new event (either created from scratch or duplicated)
      setEvents(prev => [savedEvent, ...prev])
      const isDuplicate = editingEvent && editingEvent.name.includes('(Copy)')
      toast({
        title: isDuplicate ? "Event duplicated" : "Event created",
        description: `${savedEvent.name} has been ${isDuplicate ? 'duplicated' : 'created'} successfully.`,
        variant: "default",
      })
    }
    setEditingEvent(null)
  }

  const handleEditEvent = (event: any) => {
    setEditingEvent(event)
    setShowEventDialog(true)
  }

  const handleDeleteEvent = async (eventId: string) => {
    if (!confirm('Are you sure you want to delete this event? This action cannot be undone.')) {
      return
    }

    try {
      const response = await fetch(`/api/events/${eventId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        const eventToDelete = events.find(e => e.id === eventId)
        setEvents(prev => prev.filter(event => event.id !== eventId))
        toast({
          title: "Event deleted",
          description: `${eventToDelete?.name || 'Event'} has been deleted successfully.`,
          variant: "default",
        })
      } else {
        const errorData = await response.json().catch(() => ({}))
        const errorMessage = errorData.error || 'Failed to delete event'
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Network error occurred while deleting event",
        variant: "destructive",
      })
    }
  }

  const handleCreateEvent = () => {
    setEditingEvent(null)
    setShowEventDialog(true)
  }

  const handleDuplicateEvent = (event: any) => {
    // Create a clean copy of the event for duplication
    // Important: Don't include 'id' field so the form dialog treats this as a new event
    const duplicatedEvent = {
      name: `${event.name} (Copy)`,
      description: event.description,
      startDate: event.startDate,
      endDate: event.endDate,
      serviceStartTime: event.serviceStartTime,
      serviceEndTime: event.serviceEndTime,
      primaryColor: event.primaryColor,
      secondaryColor: event.secondaryColor,
      logoUrl: event.logoUrl,
      isActive: event.isActive,
      mealTypes: event.mealTypes?.map((mt: any) => ({
        // Don't include meal type IDs either
        name: mt.name,
        description: mt.description,
        maxQuantityPerAttendee: mt.maxQuantityPerAttendee,
        totalQuantity: mt.totalQuantity,
        isVegetarian: mt.isVegetarian,
        isVegan: mt.isVegan,
        isGlutenFree: mt.isGlutenFree
      })) || []
    }

    // Set as editing event (but without ID, so it will be treated as new)
    setEditingEvent(duplicatedEvent)
    setShowEventDialog(true)

    // Show feedback to user
    toast({
      title: "Event ready to duplicate",
      description: `"${event.name}" has been copied. Make any changes and save to create the duplicate.`,
      variant: "default",
    })
  }

  // Show loading state while fetching events
  if (loading) {
    return (
      <AppLayout>
        <div className="flex h-screen items-center justify-center">
          <LoadingSpinner size="lg" />
        </div>
      </AppLayout>
    )
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Events</h1>
            <p className="text-muted-foreground">
              Manage your events and their configurations
            </p>
          </div>
          <Button onClick={handleCreateEvent}>
            <Plus className="mr-2 h-4 w-4" />
            Create Event
          </Button>
        </div>

        {/* Search */}
        <div className="flex items-center space-x-2">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search events..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-destructive/15 border border-destructive/20 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-destructive" />
                <p className="text-sm font-medium text-destructive">Error loading events</p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={fetchEvents}
                disabled={loading}
              >
                {loading ? "Retrying..." : "Retry"}
              </Button>
            </div>
            <p className="text-sm text-destructive/80 mt-2">{error}</p>
          </div>
        )}

        {/* Loading State */}
        {loading && !error && (
          <div className="flex items-center justify-center py-12">
            <LoadingSpinner size="lg" />
            <span className="ml-2 text-muted-foreground">Loading events...</span>
          </div>
        )}

        {/* Events Grid */}
        {!loading && !error && (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {filteredEvents.map((event) => (
            <Card key={event.id} className="relative">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-1">
                    <CardTitle className="text-lg">{event.name}</CardTitle>
                    <CardDescription className="line-clamp-2">
                      {event.description}
                    </CardDescription>
                  </div>
                  <Badge variant={event.isActive ? "success" : "secondary"}>
                    {event.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center">
                    <Calendar className="mr-1 h-4 w-4" />
                    {formatDate(event.startDate)}
                    {event.startDate !== event.endDate && (
                      <span> - {formatDate(event.endDate)}</span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                  <div className="flex items-center">
                    <Users className="mr-1 h-4 w-4" />
                    {event.attendeeCount} attendees
                  </div>
                </div>

                {/* Color indicators */}
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-muted-foreground">Theme:</span>
                  <div 
                    className="w-4 h-4 rounded-full border"
                    style={{ backgroundColor: event.primaryColor }}
                  />
                  <div 
                    className="w-4 h-4 rounded-full border"
                    style={{ backgroundColor: event.secondaryColor }}
                  />
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(`/events/${event.id}`, '_blank')}
                      title="View Event"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditEvent(event)}
                      title="Edit Event"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDuplicateEvent(event)}
                      title="Duplicate Event"
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => window.open(`/attendees?eventId=${event.id}`, '_blank')}
                      title="Manage Attendees"
                    >
                      <Settings className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-destructive hover:text-destructive"
                      onClick={() => handleDeleteEvent(event.id)}
                      title="Delete Event"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          </div>
        )}

        {/* Empty State */}
        {!loading && !error && filteredEvents.length === 0 && (
          <div className="text-center py-12">
            <Calendar className="mx-auto h-12 w-12 text-muted-foreground" />
            <h3 className="mt-4 text-lg font-semibold">No events found</h3>
            <p className="text-muted-foreground">
              {searchTerm ? "Try adjusting your search terms" : "Get started by creating your first event"}
            </p>
            {!searchTerm && (
              <Button className="mt-4" onClick={handleCreateEvent}>
                <Plus className="mr-2 h-4 w-4" />
                Create Event
              </Button>
            )}
          </div>
        )}

        {/* Event Form Dialog */}
        <EventFormDialog
          open={showEventDialog}
          onOpenChange={setShowEventDialog}
          event={editingEvent}
          onEventSaved={handleEventSaved}
        />
      </div>
    </AppLayout>
  )
}
