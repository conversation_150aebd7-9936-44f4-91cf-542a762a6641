const { MongoClient } = require('mongodb');
const bcrypt = require('bcryptjs');

const url = 'mongodb://localhost:27017';
const dbName = 'foodscan';

async function testAuth() {
  const client = new MongoClient(url);
  
  try {
    await client.connect();
    console.log('🔍 Testing authentication...');
    
    const db = client.db(dbName);
    
    // Find the admin user
    const user = await db.collection('users').findOne({
      email: '<EMAIL>'
    });
    
    if (!user) {
      console.log('❌ User not found');
      return;
    }
    
    console.log('✅ User found:', {
      id: user._id,
      email: user.email,
      name: user.name,
      role: user.role
    });
    
    // Test password comparison
    const testPassword = 'admin123';
    const isValid = await bcrypt.compare(testPassword, user.password);
    
    console.log('🔑 Password test:');
    console.log('  Input password:', testPassword);
    console.log('  Stored hash:', user.password);
    console.log('  Comparison result:', isValid ? '✅ VALID' : '❌ INVALID');
    
    // Test with wrong password
    const wrongPassword = 'wrongpassword';
    const isWrongValid = await bcrypt.compare(wrongPassword, user.password);
    console.log('  Wrong password test:', isWrongValid ? '❌ SHOULD BE INVALID' : '✅ CORRECTLY INVALID');
    
    // Test hash generation
    const newHash = await bcrypt.hash(testPassword, 10);
    const newHashValid = await bcrypt.compare(testPassword, newHash);
    console.log('🔄 New hash test:', newHashValid ? '✅ VALID' : '❌ INVALID');
    
  } catch (error) {
    console.error('❌ Error testing auth:', error);
  } finally {
    await client.close();
  }
}

testAuth();
