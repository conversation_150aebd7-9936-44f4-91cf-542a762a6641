import {
  parseCSV,
  attendeesToCSV,
  validateCSVFile,
  generateCSVTemplate,
  type AttendeeCSVRow,
  type AttendeeExportRow
} from '@/lib/csv-utils'

describe('CSV Utils', () => {
  describe('parseCSV', () => {
    it('parses valid CSV with all fields', () => {
      const csvContent = `Name,Email,Dietary Restrictions,Special Notes
<PERSON>,<EMAIL>,Vegetarian,Wheelchair access
<PERSON>,<EMAIL>,Gluten-free,VIP guest`

      const result = parseCSV(csvContent)

      expect(result).toHaveLength(2)
      expect(result[0]).toEqual({
        name: '<PERSON>',
        email: '<EMAIL>',
        dietaryRestrictions: 'Vegetarian',
        specialNotes: 'Wheelchair access'
      })
      expect(result[1]).toEqual({
        name: '<PERSON>',
        email: '<EMAIL>',
        dietaryRestrictions: 'Gluten-free',
        specialNotes: 'VIP guest'
      })
    })

    it('parses CSV with only required fields', () => {
      const csvContent = `Name
<PERSON>`

      const result = parseCSV(csvContent)

      expect(result).toHaveLength(2)
      expect(result[0]).toEqual({
        name: '<PERSON> Doe',
        email: undefined,
        dietaryRestrictions: undefined,
        specialNotes: undefined
      })
    })

    it('handles quoted fields with commas', () => {
      const csvContent = `Name,Email,Special Notes
"Doe, John",<EMAIL>,"Needs help with registration, VIP"`

      const result = parseCSV(csvContent)

      expect(result).toHaveLength(1)
      expect(result[0]).toEqual({
        name: 'Doe, John',
        email: '<EMAIL>',
        dietaryRestrictions: undefined,
        specialNotes: 'Needs help with registration, VIP'
      })
    })

    it('handles escaped quotes in fields', () => {
      const csvContent = `Name,Special Notes
John Doe,"He said ""Hello"" to everyone"`

      const result = parseCSV(csvContent)

      expect(result).toHaveLength(1)
      expect(result[0].specialNotes).toBe('He said "Hello" to everyone')
    })

    it('skips empty lines', () => {
      const csvContent = `Name,Email
John Doe,<EMAIL>

Jane Smith,<EMAIL>`

      const result = parseCSV(csvContent)

      expect(result).toHaveLength(2)
    })

    it('throws error for missing required headers', () => {
      const csvContent = `Email,Phone
<EMAIL>,************`

      expect(() => parseCSV(csvContent)).toThrow('Missing required headers: name')
    })

    it('throws error for invalid email format', () => {
      const csvContent = `Name,Email
John Doe,invalid-email`

      expect(() => parseCSV(csvContent)).toThrow('CSV parsing errors')
    })

    it('throws error for empty name field', () => {
      const csvContent = `Name,Email
,<EMAIL>`

      expect(() => parseCSV(csvContent)).toThrow('CSV parsing errors')
    })

    it('throws error for file with only headers', () => {
      const csvContent = `Name,Email`

      expect(() => parseCSV(csvContent)).toThrow('CSV must contain at least a header row and one data row')
    })
  })

  describe('attendeesToCSV', () => {
    it('converts attendees to CSV format', () => {
      const attendees: AttendeeExportRow[] = [
        {
          name: 'John Doe',
          email: '<EMAIL>',
          eventName: 'Tech Conference',
          dietaryRestrictions: 'Vegetarian',
          specialNotes: 'Wheelchair access',
          qrCode: 'JD-001',
          scannedAt: '2024-01-20T12:00:00Z',
          createdAt: '2024-01-15T10:00:00Z'
        },
        {
          name: 'Jane Smith',
          email: '<EMAIL>',
          eventName: 'Tech Conference',
          dietaryRestrictions: 'Gluten-free',
          specialNotes: '',
          qrCode: 'JS-002',
          scannedAt: '',
          createdAt: '2024-01-15T10:05:00Z'
        }
      ]

      const result = attendeesToCSV(attendees)

      expect(result).toContain('Name,Email,Event,Dietary Restrictions,Special Notes,QR Code,Scanned At,Created At')
      expect(result).toContain('John Doe,<EMAIL>,Tech Conference,Vegetarian,Wheelchair access,JD-001,2024-01-20T12:00:00Z,2024-01-15T10:00:00Z')
      expect(result).toContain('Jane Smith,<EMAIL>,Tech Conference,Gluten-free,,JS-002,,2024-01-15T10:05:00Z')
    })

    it('handles empty attendees array', () => {
      const result = attendeesToCSV([])

      expect(result).toBe('Name,Email,Event,Dietary Restrictions,Special Notes,QR Code,Scanned At,Created At\n')
    })

    it('escapes fields with commas', () => {
      const attendees: AttendeeExportRow[] = [
        {
          name: 'Doe, John',
          email: '<EMAIL>',
          eventName: 'Tech Conference',
          specialNotes: 'Needs help with registration, VIP',
          qrCode: 'JD-001',
          createdAt: '2024-01-15T10:00:00Z'
        }
      ]

      const result = attendeesToCSV(attendees)

      expect(result).toContain('"Doe, John"')
      expect(result).toContain('"Needs help with registration, VIP"')
    })

    it('escapes fields with quotes', () => {
      const attendees: AttendeeExportRow[] = [
        {
          name: 'John Doe',
          email: '<EMAIL>',
          eventName: 'Tech Conference',
          specialNotes: 'He said "Hello" to everyone',
          qrCode: 'JD-001',
          createdAt: '2024-01-15T10:00:00Z'
        }
      ]

      const result = attendeesToCSV(attendees)

      expect(result).toContain('"He said ""Hello"" to everyone"')
    })
  })

  describe('validateCSVFile', () => {
    it('validates CSV file type', () => {
      const csvFile = new File(['test'], 'test.csv', { type: 'text/csv' })
      const result = validateCSVFile(csvFile)

      expect(result.valid).toBe(true)
    })

    it('validates CSV file extension', () => {
      const csvFile = new File(['test'], 'test.csv', { type: 'text/plain' })
      const result = validateCSVFile(csvFile)

      expect(result.valid).toBe(true)
    })

    it('rejects non-CSV files', () => {
      const txtFile = new File(['test'], 'test.txt', { type: 'text/plain' })
      const result = validateCSVFile(txtFile)

      expect(result.valid).toBe(false)
      expect(result.error).toBe('Please select a CSV file')
    })

    it('rejects files that are too large', () => {
      const largeContent = 'x'.repeat(11 * 1024 * 1024) // 11MB
      const largeFile = new File([largeContent], 'large.csv', { type: 'text/csv' })
      const result = validateCSVFile(largeFile)

      expect(result.valid).toBe(false)
      expect(result.error).toBe('File size must be less than 10MB')
    })
  })

  describe('generateCSVTemplate', () => {
    it('generates CSV template with headers and sample data', () => {
      const template = generateCSVTemplate()

      expect(template).toContain('Name,Email,Dietary Restrictions,Special Notes')
      expect(template).toContain('John Doe,<EMAIL>,Vegetarian,Wheelchair accessible seating')
      expect(template).toContain('Jane Smith,<EMAIL>,Gluten-free,')
      expect(template).toContain('Mike Johnson,<EMAIL>,,VIP guest')
    })

    it('properly escapes sample data', () => {
      const template = generateCSVTemplate()

      // Check that the wheelchair access note is properly handled
      expect(template).toContain('Wheelchair accessible seating')
    })
  })

  describe('edge cases', () => {
    it('handles CSV with different header variations', () => {
      const csvContent = `Full Name,Email Address,Dietary Requirements,Notes
John Doe,<EMAIL>,Vegetarian,Test`

      const result = parseCSV(csvContent)

      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('John Doe')
      expect(result[0].email).toBe('<EMAIL>')
      expect(result[0].dietaryRestrictions).toBe('Vegetarian')
      expect(result[0].specialNotes).toBe('Test')
    })

    it('handles CSV with extra whitespace', () => {
      const csvContent = `  Name  ,  Email  
  John Doe  ,  <EMAIL>  `

      const result = parseCSV(csvContent)

      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('John Doe')
      expect(result[0].email).toBe('<EMAIL>')
    })

    it('handles CSV with mixed case headers', () => {
      const csvContent = `NAME,email,DIETARY restrictions
John Doe,<EMAIL>,Vegetarian`

      const result = parseCSV(csvContent)

      expect(result).toHaveLength(1)
      expect(result[0].name).toBe('John Doe')
      expect(result[0].email).toBe('<EMAIL>')
      expect(result[0].dietaryRestrictions).toBe('Vegetarian')
    })
  })
})
