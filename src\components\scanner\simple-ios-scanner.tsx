"use client"

import { useEffect, useRef, useState, useCallback } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Camera, 
  CameraOff, 
  Upload, 
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Smartphone,
  Hand,
  Zap
} from "lucide-react"

interface SimpleIOSScannerProps {
  onScanResult: (data: string) => void
  onScanError: (error: string) => void
}

export function SimpleIOSScanner({ onScanResult, onScanError }: SimpleIOSScannerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const streamRef = useRef<MediaStream | null>(null)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)
  
  const [isIOS, setIsIOS] = useState(false)
  const [status, setStatus] = useState<'waiting' | 'loading' | 'ready' | 'error'>('waiting')
  const [error, setError] = useState<string | null>(null)
  const [scanCount, setScanCount] = useState(0)
  const [needsUserGesture, setNeedsUserGesture] = useState(true)

  // Detect iOS
  useEffect(() => {
    const userAgent = navigator.userAgent.toLowerCase()
    const isIOSDevice = /iphone|ipad|ipod/.test(userAgent)
    setIsIOS(isIOSDevice)
    console.log('📱 Simple iOS Scanner: iOS detected:', isIOSDevice)
  }, [])

  // Simple camera initialization for iOS
  const startCamera = useCallback(async () => {
    if (!videoRef.current) return

    console.log('🍎 Simple iOS Scanner: Starting camera...')
    setStatus('loading')
    setError(null)
    setNeedsUserGesture(false)

    try {
      // Very simple constraints for iOS compatibility
      const constraints = {
        video: {
          facingMode: 'environment',
          width: { ideal: 640 },
          height: { ideal: 480 }
        },
        audio: false
      }

      console.log('📷 Simple iOS Scanner: Requesting camera...')
      const stream = await navigator.mediaDevices.getUserMedia(constraints)
      streamRef.current = stream

      // Set up video element for iOS
      const video = videoRef.current
      video.srcObject = stream
      video.setAttribute('playsinline', 'true')
      video.setAttribute('webkit-playsinline', 'true')
      video.muted = true
      video.autoplay = false

      // Wait for video to be ready
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => reject(new Error('Video timeout')), 10000)
        
        video.onloadedmetadata = async () => {
          clearTimeout(timeout)
          try {
            await video.play()
            console.log('✅ Simple iOS Scanner: Video playing')
            resolve(true)
          } catch (playError) {
            console.warn('⚠️ Simple iOS Scanner: Play error, retrying...', playError)
            video.muted = true
            video.volume = 0
            await video.play()
            resolve(true)
          }
        }
        
        video.onerror = (err) => {
          clearTimeout(timeout)
          reject(err)
        }
      })

      setStatus('ready')
      console.log('🎉 Simple iOS Scanner: Camera ready!')

      // Start simple QR scanning
      startScanning()

    } catch (err) {
      console.error('❌ Simple iOS Scanner: Camera failed:', err)
      const errorMessage = err instanceof Error ? err.message : 'Camera access failed'
      setError(errorMessage)
      setStatus('error')
      onScanError(errorMessage)
    }
  }, [onScanError])

  // Simple QR code scanning using canvas
  const startScanning = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return

    console.log('🔍 Simple iOS Scanner: Starting QR scanning...')

    intervalRef.current = setInterval(async () => {
      try {
        const video = videoRef.current!
        const canvas = canvasRef.current!
        
        if (video.readyState !== video.HAVE_ENOUGH_DATA) return

        const context = canvas.getContext('2d')
        if (!context) return

        // Set canvas size to match video
        canvas.width = video.videoWidth
        canvas.height = video.videoHeight

        // Draw current frame
        context.drawImage(video, 0, 0, canvas.width, canvas.height)

        // Convert to blob and scan
        canvas.toBlob(async (blob) => {
          if (!blob) return

          try {
            // Dynamic import for better iOS compatibility
            const QrScannerModule = await import('qr-scanner')
            const QrScanner = QrScannerModule.default
            const result = await QrScanner.scanImage(blob)
            
            console.log('🎉 Simple iOS Scanner: QR found:', result)
            setScanCount(prev => prev + 1)
            onScanResult(result)

            // iOS haptic feedback
            if ('vibrate' in navigator) {
              navigator.vibrate([100])
            }

          } catch (scanError) {
            // Silent - no QR code found
          }
        }, 'image/jpeg', 0.8)

      } catch (err) {
        console.debug('🔍 Simple iOS Scanner: Scan attempt failed:', err)
      }
    }, 1000) // Scan every second for iOS performance

  }, [onScanResult])

  // Cleanup
  const cleanup = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
    
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
  }, [])

  useEffect(() => {
    return cleanup
  }, [cleanup])

  // File upload handler
  const handleFileUpload = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith('image/')) {
      onScanError('Please select an image file')
      return
    }

    try {
      console.log('📁 Simple iOS Scanner: Scanning uploaded image...')
      const QrScannerModule = await import('qr-scanner')
      const QrScanner = QrScannerModule.default
      const result = await QrScanner.scanImage(file)
      onScanResult(result)
      setScanCount(prev => prev + 1)
      console.log('✅ Simple iOS Scanner: QR found in uploaded image')
    } catch (err) {
      console.error('❌ Simple iOS Scanner: No QR in image:', err)
      onScanError('No QR code found in the uploaded image')
    }

    e.target.value = ""
  }, [onScanResult, onScanError])

  // Waiting for user gesture (iOS requirement)
  if (needsUserGesture) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Hand className="h-5 w-5" />
            <span>Ready to Start</span>
            {isIOS && <Badge variant="secondary">iOS</Badge>}
          </CardTitle>
          <CardDescription>
            {isIOS 
              ? "iOS Safari requires user interaction to access camera"
              : "Tap to start camera access"
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isIOS && (
            <Alert>
              <Smartphone className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <div className="font-medium">📱 iPhone Instructions:</div>
                  <div className="text-sm space-y-1">
                    <div>• Tap "Start Camera" below</div>
                    <div>• Allow camera access when prompted</div>
                    <div>• Hold iPhone steady with good lighting</div>
                    <div>• QR codes will be detected automatically</div>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="text-center py-8">
            <Camera className="mx-auto h-16 w-16 text-muted-foreground" />
            <div className="mt-4">
              <h3 className="text-lg font-semibold">Camera Access Required</h3>
              <p className="text-muted-foreground mt-2">
                {isIOS 
                  ? "Tap the button below to start the camera and begin scanning QR codes"
                  : "Click to initialize the QR code scanner"
                }
              </p>
            </div>
            <Button onClick={startCamera} size="lg" className="mt-4 w-full max-w-sm">
              <Camera className="mr-2 h-5 w-5" />
              Start Camera
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Loading state
  if (status === 'loading') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            <span>Starting Camera</span>
            {isIOS && <Badge variant="secondary">iOS</Badge>}
          </CardTitle>
          <CardDescription>
            {isIOS 
              ? "Initializing camera for iOS Safari..."
              : "Setting up QR code scanner..."
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center py-8">
          <div className="space-y-4">
            <RefreshCw className="mx-auto h-12 w-12 animate-spin text-muted-foreground" />
            <div>
              <p className="text-muted-foreground">Please wait while we start your camera...</p>
              {isIOS && (
                <p className="text-xs text-muted-foreground mt-2">
                  This may take a few seconds on iOS devices
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Error state
  if (status === 'error') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CameraOff className="h-5 w-5" />
            <span>Camera Error</span>
            {isIOS && <Badge variant="secondary">iOS</Badge>}
          </CardTitle>
          <CardDescription>
            Unable to access camera
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Error:</strong> {error}
            </AlertDescription>
          </Alert>

          {isIOS && (
            <Alert>
              <Smartphone className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-2">
                  <div className="font-medium">iOS Troubleshooting:</div>
                  <div className="text-sm space-y-1">
                    <div>1. Go to Settings → Safari → Camera</div>
                    <div>2. Ensure "Ask" or "Allow" is selected</div>
                    <div>3. Refresh this page and try again</div>
                    <div>4. Make sure no other apps are using the camera</div>
                  </div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          <div className="flex flex-col gap-2">
            <Button onClick={() => {
              setStatus('waiting')
              setNeedsUserGesture(true)
              setError(null)
            }}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
            
            <Button onClick={() => fileInputRef.current?.click()} variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Upload QR Code Image
            </Button>
            
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>
        </CardContent>
      </Card>
    )
  }

  // Working scanner
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Camera className="h-5 w-5 text-green-600" />
              <span>Simple QR Scanner</span>
              <Badge variant="outline" className="text-green-600 border-green-600">
                <CheckCircle className="h-3 w-3 mr-1" />
                Active
              </Badge>
              {isIOS && <Badge variant="secondary">iOS</Badge>}
            </CardTitle>
            <CardDescription>
              Point camera at QR code • {scanCount} scans completed
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Video Container */}
        <div className="relative aspect-square bg-black rounded-lg overflow-hidden">
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            playsInline
            muted
            style={{
              WebkitPlaysinline: true,
              objectFit: 'cover'
            } as any}
          />
          
          {/* Simple scanning overlay */}
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="w-48 h-48 border-2 border-primary rounded-lg relative">
              <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-primary rounded-tl-lg"></div>
              <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-primary rounded-tr-lg"></div>
              <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-primary rounded-bl-lg"></div>
              <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-primary rounded-br-lg"></div>
            </div>
          </div>

          {/* Status indicator */}
          <div className="absolute top-4 left-4 flex space-x-2">
            <Badge variant="secondary" className="bg-black bg-opacity-50 text-white border-none">
              <Camera className="h-3 w-3 mr-1" />
              Live
            </Badge>
            {isIOS && (
              <Badge variant="secondary" className="bg-blue-500 bg-opacity-80 text-white border-none">
                🍎 iOS
              </Badge>
            )}
          </div>
        </div>

        {/* Controls */}
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            title="Upload QR code image"
          >
            <Upload className="h-4 w-4" />
          </Button>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            className="hidden"
          />
        </div>

        {/* Instructions */}
        <div className="text-center text-sm text-muted-foreground">
          <p>📱 Position QR code within the frame</p>
          {isIOS && (
            <p className="text-xs mt-1">
              💡 For best results on iPhone: ensure good lighting and hold steady
            </p>
          )}
        </div>

        {/* Hidden canvas for frame capture */}
        <canvas ref={canvasRef} className="hidden" />
      </CardContent>
    </Card>
  )
}
