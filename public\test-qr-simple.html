<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple QR Scanner Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            background: #f0f0f0;
        }
        .error {
            background: #ffebee;
            color: #c62828;
        }
        .success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        video {
            width: 100%;
            max-width: 500px;
            height: 300px;
            background: #000;
            border-radius: 8px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <h1>🔍 Simple QR Scanner Test</h1>
    <p>This is a minimal test to isolate QR scanner issues.</p>

    <div id="status" class="status">Ready to test</div>

    <div>
        <button onclick="testBasicCamera()">Test Basic Camera</button>
        <button onclick="testQRScanner()">Test QR Scanner</button>
        <button onclick="clearStatus()">Clear Status</button>
    </div>

    <div style="margin: 20px 0;">
        <video id="video" playsinline muted autoplay></video>
    </div>

    <div>
        <h3>Upload QR Code Image:</h3>
        <input type="file" id="fileInput" accept="image/*" onchange="testImageScan(event)">
    </div>

    <div>
        <h3>Console Output:</h3>
        <div id="console" style="background: #f5f5f5; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; max-height: 200px; overflow-y: auto;"></div>
    </div>

    <script type="module">
        const statusDiv = document.getElementById('status');
        const video = document.getElementById('video');
        const consoleDiv = document.getElementById('console');

        // Capture console logs
        const originalLog = console.log;
        const originalError = console.error;
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            consoleDiv.textContent += '[LOG] ' + args.join(' ') + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            consoleDiv.textContent += '[ERROR] ' + args.join(' ') + '\n';
            consoleDiv.scrollTop = consoleDiv.scrollHeight;
        };

        function updateStatus(message, isError = false, isSuccess = false) {
            statusDiv.textContent = message;
            statusDiv.className = 'status' + (isError ? ' error' : isSuccess ? ' success' : '');
            console.log('Status:', message);
        }

        window.testBasicCamera = async function() {
            updateStatus('Testing basic camera access...');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: { facingMode: 'environment' }
                });
                
                video.srcObject = stream;
                updateStatus('✅ Basic camera working!', false, true);
                
            } catch (error) {
                console.error('Camera error:', error);
                updateStatus('❌ Camera failed: ' + error.message, true);
            }
        };

        window.testQRScanner = async function() {
            updateStatus('Loading QR Scanner library...');
            
            try {
                // Import QR Scanner
                const QrScanner = (await import('https://unpkg.com/qr-scanner@1.4.2/qr-scanner.min.js')).default;
                updateStatus('QR Scanner library loaded, creating instance...');
                
                const scanner = new QrScanner(
                    video,
                    result => {
                        console.log('QR Code detected:', result.data);
                        updateStatus('🎉 QR Code found: ' + result.data, false, true);
                    },
                    {
                        onDecodeError: error => {
                            console.debug('Decode error (normal):', error.message);
                        },
                        highlightScanRegion: true,
                        highlightCodeOutline: true,
                        preferredCamera: 'environment',
                        maxScansPerSecond: 2
                    }
                );

                updateStatus('Starting QR scanner...');
                await scanner.start();
                updateStatus('✅ QR Scanner active! Point camera at QR code.', false, true);
                
            } catch (error) {
                console.error('QR Scanner error:', error);
                updateStatus('❌ QR Scanner failed: ' + error.message, true);
            }
        };

        window.testImageScan = async function(event) {
            const file = event.target.files[0];
            if (!file) return;

            updateStatus('Scanning uploaded image...');
            
            try {
                const QrScanner = (await import('https://unpkg.com/qr-scanner@1.4.2/qr-scanner.min.js')).default;
                const result = await QrScanner.scanImage(file);
                updateStatus('🎉 QR Code found in image: ' + result, false, true);
                
            } catch (error) {
                console.error('Image scan error:', error);
                updateStatus('❌ No QR code found in image', true);
            }
            
            event.target.value = '';
        };

        window.clearStatus = function() {
            updateStatus('Status cleared');
            consoleDiv.textContent = '';
        };

        // Initial diagnostics
        updateStatus('Running initial diagnostics...');
        
        console.log('Browser:', navigator.userAgent);
        console.log('Protocol:', window.location.protocol);
        console.log('MediaDevices support:', !!navigator.mediaDevices);
        console.log('getUserMedia support:', !!navigator.mediaDevices?.getUserMedia);
        
        updateStatus('Ready to test - check console for diagnostics');
    </script>
</body>
</html>
