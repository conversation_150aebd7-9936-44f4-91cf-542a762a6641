import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"
import { MongoClient, ObjectId } from "mongodb"

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const event = await prisma.event.findUnique({
      where: { id: params.id },
      include: {
        mealTypes: true,
        attendees: {
          include: {
            mealConsumptions: {
              include: {
                mealType: true
              }
            }
          }
        },
        _count: {
          select: {
            attendees: true,
            scanLogs: true,
          }
        }
      }
    })

    if (!event) {
      return NextResponse.json({ error: "Event not found" }, { status: 404 })
    }

    return NextResponse.json(event)
  } catch (error) {
    console.error("Error fetching event:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const client = new MongoClient(process.env.DATABASE_URL!)

  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      description,
      startDate,
      endDate,
      serviceStartTime,
      serviceEndTime,
      primaryColor,
      secondaryColor,
      logoUrl,
      isActive,
      mealTypes
    } = body

    // Validate ObjectId format
    if (!params.id || params.id.length !== 24) {
      return NextResponse.json({ error: "Invalid event ID format" }, { status: 400 })
    }

    // Validate required fields
    if (!name || typeof name !== 'string' || !name.trim()) {
      return NextResponse.json({ error: "Event name is required" }, { status: 400 })
    }

    if (!startDate || !endDate) {
      return NextResponse.json({ error: "Start date and end date are required" }, { status: 400 })
    }

    // Connect to MongoDB
    await client.connect()
    const db = client.db('foodscan')
    const eventsCollection = db.collection('events')
    const mealTypesCollection = db.collection('meal_types')

    // Check if event exists
    const existingEvent = await eventsCollection.findOne({ _id: new ObjectId(params.id) })
    if (!existingEvent) {
      return NextResponse.json({ error: "Event not found" }, { status: 404 })
    }

    // Validate and parse dates
    let parsedStartDate, parsedEndDate, parsedServiceStartTime, parsedServiceEndTime

    try {
      parsedStartDate = new Date(startDate)
      parsedEndDate = new Date(endDate)

      if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {
        return NextResponse.json({ error: "Invalid date format" }, { status: 400 })
      }

      parsedServiceStartTime = serviceStartTime ? new Date(serviceStartTime) : null
      parsedServiceEndTime = serviceEndTime ? new Date(serviceEndTime) : null

      if (serviceStartTime && isNaN(parsedServiceStartTime!.getTime())) {
        return NextResponse.json({ error: "Invalid service start time format" }, { status: 400 })
      }
      if (serviceEndTime && isNaN(parsedServiceEndTime!.getTime())) {
        return NextResponse.json({ error: "Invalid service end time format" }, { status: 400 })
      }
    } catch (dateError) {
      console.error("Date parsing error:", dateError)
      return NextResponse.json({ error: "Invalid date format" }, { status: 400 })
    }

    // Update event using direct MongoDB operations
    const updateResult = await eventsCollection.updateOne(
      { _id: new ObjectId(params.id) },
      {
        $set: {
          name: name.trim(),
          description: description?.trim() || null,
          startDate: parsedStartDate,
          endDate: parsedEndDate,
          serviceStartTime: parsedServiceStartTime,
          serviceEndTime: parsedServiceEndTime,
          primaryColor: primaryColor || "#3b82f6",
          secondaryColor: secondaryColor || "#1e40af",
          logoUrl: logoUrl?.trim() || null,
          isActive: isActive ?? true,
          updatedAt: new Date()
        }
      }
    )

    if (updateResult.modifiedCount === 0) {
      return NextResponse.json({ error: "Failed to update event" }, { status: 500 })
    }

    // Handle meal types update if provided
    if (mealTypes && Array.isArray(mealTypes)) {
      // Validate meal types data
      for (const meal of mealTypes) {
        if (!meal.name || typeof meal.name !== 'string') {
          return NextResponse.json({ error: "Invalid meal type data: name is required" }, { status: 400 })
        }
      }

      // Delete existing meal types
      await mealTypesCollection.deleteMany({ eventId: params.id })

      // Create new meal types
      if (mealTypes.length > 0) {
        const newMealTypes = mealTypes.map((meal: any) => ({
          _id: new ObjectId(),
          name: meal.name.trim(),
          description: meal.description?.trim() || null,
          maxQuantityPerAttendee: Math.max(1, parseInt(meal.maxQuantityPerAttendee) || 1),
          totalQuantity: meal.totalQuantity ? Math.max(0, parseInt(meal.totalQuantity)) : null,
          consumedQuantity: 0,
          isVegetarian: Boolean(meal.isVegetarian),
          isVegan: Boolean(meal.isVegan),
          isGlutenFree: Boolean(meal.isGlutenFree),
          isAvailable: true,
          eventId: params.id,
          createdAt: new Date(),
          updatedAt: new Date()
        }))

        await mealTypesCollection.insertMany(newMealTypes)
      }
    }

    // Fetch the complete updated event with meal types and counts
    const updatedEvent = await eventsCollection.findOne({ _id: new ObjectId(params.id) })
    const eventMealTypes = await mealTypesCollection.find({ eventId: params.id }).toArray()

    // Get counts using aggregation
    const attendeesCount = await db.collection('attendees').countDocuments({ eventId: params.id })
    const scanLogsCount = await db.collection('scan_logs').countDocuments({ eventId: params.id })

    // Format response to match Prisma structure
    const response = {
      ...updatedEvent,
      id: updatedEvent._id.toString(),
      mealTypes: eventMealTypes.map(mt => ({
        ...mt,
        id: mt._id.toString()
      })),
      _count: {
        attendees: attendeesCount,
        scanLogs: scanLogsCount
      }
    }

    // Remove MongoDB _id field
    delete response._id

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error updating event:", error)
    console.error("Error details:", {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      eventId: params.id
    })
    return NextResponse.json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  } finally {
    await client.close()
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    await prisma.event.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: "Event deleted successfully" })
  } catch (error) {
    console.error("Error deleting event:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
