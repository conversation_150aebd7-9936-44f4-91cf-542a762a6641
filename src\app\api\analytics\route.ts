import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const eventId = searchParams.get('eventId')

    // Get basic stats
    const totalEvents = await prisma.event.count()
    const totalAttendees = await prisma.attendee.count({
      where: eventId ? { eventId } : undefined
    })
    const totalScans = await prisma.scanLog.count({
      where: {
        success: true,
        ...(eventId && { eventId })
      }
    })

    // Get today's scans
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const tomorrow = new Date(today)
    tomorrow.setDate(tomorrow.getDate() + 1)

    const todayScans = await prisma.scanLog.count({
      where: {
        success: true,
        scannedAt: {
          gte: today,
          lt: tomorrow
        },
        ...(eventId && { eventId })
      }
    })

    // Get hourly scan data for today
    const hourlyScans = []
    for (let hour = 0; hour < 24; hour++) {
      const hourStart = new Date(today)
      hourStart.setHours(hour, 0, 0, 0)
      const hourEnd = new Date(today)
      hourEnd.setHours(hour + 1, 0, 0, 0)

      const scans = await prisma.scanLog.count({
        where: {
          success: true,
          scannedAt: {
            gte: hourStart,
            lt: hourEnd
          },
          ...(eventId && { eventId })
        }
      })

      hourlyScans.push({
        hour: `${hour.toString().padStart(2, '0')}:00`,
        scans
      })
    }

    // Get meal type distribution
    const mealConsumptions = await prisma.mealConsumption.findMany({
      include: {
        mealType: true,
        attendee: eventId ? {
          where: { eventId }
        } : true
      },
      ...(eventId && {
        where: {
          attendee: {
            eventId
          }
        }
      })
    })

    const mealTypeDistribution = mealConsumptions.reduce((acc, consumption) => {
      const mealTypeName = consumption.mealType.name
      acc[mealTypeName] = (acc[mealTypeName] || 0) + consumption.quantity
      return acc
    }, {} as Record<string, number>)

    const totalMeals = Object.values(mealTypeDistribution).reduce((sum, count) => sum + count, 0)
    const mealTypeStats = Object.entries(mealTypeDistribution).map(([mealType, count]) => ({
      mealType,
      count,
      percentage: totalMeals > 0 ? Math.round((count / totalMeals) * 100) : 0
    }))

    // Get daily trends for the last 7 days
    const dailyTrends = []
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)
      const nextDate = new Date(date)
      nextDate.setDate(nextDate.getDate() + 1)

      const dayScans = await prisma.scanLog.count({
        where: {
          success: true,
          scannedAt: {
            gte: date,
            lt: nextDate
          },
          ...(eventId && { eventId })
        }
      })

      const dayAttendees = await prisma.attendee.count({
        where: {
          createdAt: {
            lt: nextDate
          },
          ...(eventId && { eventId })
        }
      })

      dailyTrends.push({
        date: date.toISOString().split('T')[0],
        scans: dayScans,
        completion: dayAttendees > 0 ? Math.round((dayScans / dayAttendees) * 100) : 0
      })
    }

    // Get recent activity
    const recentScans = await prisma.scanLog.findMany({
      where: {
        success: true,
        ...(eventId && { eventId })
      },
      include: {
        attendee: {
          select: {
            name: true,
            qrCode: true
          }
        },
        event: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        scannedAt: 'desc'
      },
      take: 10
    })

    const completionRate = totalAttendees > 0 ? Math.round((totalScans / totalAttendees) * 100) : 0

    return NextResponse.json({
      overview: {
        totalEvents,
        totalAttendees,
        totalScans,
        todayScans,
        completionRate
      },
      hourlyScans,
      mealTypeDistribution: mealTypeStats,
      dailyTrends,
      recentActivity: recentScans.map(scan => ({
        id: scan.id,
        attendeeName: scan.attendee?.name || 'Unknown',
        qrCode: scan.attendee?.qrCode || 'Unknown',
        eventName: scan.event.name,
        scannedAt: scan.scannedAt.toISOString(),
        success: scan.success
      }))
    })

  } catch (error) {
    console.error("Error fetching analytics:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
