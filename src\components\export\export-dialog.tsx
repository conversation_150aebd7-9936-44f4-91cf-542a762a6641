"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { 
  Download, 
  FileText, 
  Image, 
  Archive,
  Calendar,
  Users,
  QrCode,
  BarChart3,
  X
} from "lucide-react"
import { 
  generateAttendeePDF,
  generateAttendeeZIP,
  generateAnalyticsExport,
  downloadBlob,
  getExportFilename,
  type ExportOptions,
  type AttendeeExportData,
  type AnalyticsExportData
} from "@/lib/export-utils"

interface ExportDialogProps {
  type: 'attendees' | 'analytics' | 'events'
  data: AttendeeExportData[] | AnalyticsExportData | any
  eventName?: string
  onClose: () => void
}

export function ExportDialog({ type, data, eventName, onClose }: ExportDialogProps) {
  const [isExporting, setIsExporting] = useState(false)
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'csv',
    includeQRCodes: true,
    includeImages: false,
    customFields: []
  })
  const [selectedFormat, setSelectedFormat] = useState<'pdf' | 'csv' | 'zip' | 'json'>('csv')

  const handleExport = async () => {
    setIsExporting(true)
    
    try {
      let blob: Blob
      let filename: string

      switch (type) {
        case 'attendees':
          const attendeeData = data as AttendeeExportData[]
          
          switch (selectedFormat) {
            case 'pdf':
              blob = await generateAttendeePDF(attendeeData, { ...exportOptions, format: 'pdf' })
              filename = getExportFilename('attendees', 'pdf', eventName)
              break
            
            case 'zip':
              blob = await generateAttendeeZIP(attendeeData, { ...exportOptions, format: 'zip' })
              filename = getExportFilename('attendees', 'zip', eventName)
              break
            
            case 'json':
              blob = new Blob([JSON.stringify(attendeeData, null, 2)], { type: 'application/json' })
              filename = getExportFilename('attendees', 'json', eventName)
              break
            
            default: // csv
              const csvContent = attendeeData.map(a => ({
                name: a.name,
                email: a.email || '',
                eventName: a.eventName,
                qrCode: a.qrCode,
                dietaryRestrictions: a.dietaryRestrictions || '',
                specialNotes: a.specialNotes || '',
                scannedAt: a.scannedAt || '',
                createdAt: a.createdAt
              }))
              blob = new Blob([JSON.stringify(csvContent)], { type: 'text/csv' })
              filename = getExportFilename('attendees', 'csv', eventName)
          }
          break

        case 'analytics':
          const analyticsData = data as AnalyticsExportData
          blob = await generateAnalyticsExport(analyticsData, selectedFormat as 'csv' | 'json' | 'pdf')
          filename = getExportFilename('analytics', selectedFormat, eventName)
          break

        default:
          throw new Error(`Unsupported export type: ${type}`)
      }

      downloadBlob(blob, filename)
      onClose()
    } catch (error) {
      console.error('Export failed:', error)
      alert('Export failed. Please try again.')
    } finally {
      setIsExporting(false)
    }
  }

  const getDataCount = () => {
    if (type === 'attendees') {
      return (data as AttendeeExportData[]).length
    }
    return 1
  }

  const getFormatOptions = () => {
    switch (type) {
      case 'attendees':
        return [
          { value: 'csv', label: 'CSV', icon: FileText, description: 'Spreadsheet format' },
          { value: 'pdf', label: 'PDF', icon: FileText, description: 'Printable badges' },
          { value: 'zip', label: 'ZIP', icon: Archive, description: 'Complete package with QR codes' },
          { value: 'json', label: 'JSON', icon: FileText, description: 'Raw data format' }
        ]
      case 'analytics':
        return [
          { value: 'csv', label: 'CSV', icon: FileText, description: 'Spreadsheet format' },
          { value: 'pdf', label: 'PDF', icon: FileText, description: 'Formatted report' },
          { value: 'json', label: 'JSON', icon: FileText, description: 'Raw data format' }
        ]
      default:
        return []
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <Card className="w-full max-w-2xl mx-4">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Download className="h-5 w-5" />
              <div>
                <CardTitle>Export {type.charAt(0).toUpperCase() + type.slice(1)}</CardTitle>
                <CardDescription>
                  Export {getDataCount()} {type === 'attendees' ? 'attendees' : 'records'} 
                  {eventName && ` from ${eventName}`}
                </CardDescription>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Format Selection */}
          <div className="space-y-3">
            <Label>Export Format</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {getFormatOptions().map((format) => {
                const Icon = format.icon
                return (
                  <div
                    key={format.value}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedFormat === format.value
                        ? 'border-primary bg-primary/5'
                        : 'border-muted hover:border-primary/50'
                    }`}
                    onClick={() => setSelectedFormat(format.value as any)}
                  >
                    <div className="flex items-center space-x-2">
                      <Icon className="h-4 w-4" />
                      <div>
                        <div className="font-medium">{format.label}</div>
                        <div className="text-xs text-muted-foreground">{format.description}</div>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Options */}
          {type === 'attendees' && (
            <div className="space-y-4">
              <Label>Export Options</Label>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="includeQR">Include QR Codes</Label>
                    <p className="text-sm text-muted-foreground">
                      Add QR code images to the export
                    </p>
                  </div>
                  <Switch
                    id="includeQR"
                    checked={exportOptions.includeQRCodes}
                    onCheckedChange={(checked) => 
                      setExportOptions(prev => ({ ...prev, includeQRCodes: checked }))
                    }
                  />
                </div>

                {selectedFormat === 'zip' && (
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="includeImages">Include Individual Images</Label>
                      <p className="text-sm text-muted-foreground">
                        Add separate QR code image files
                      </p>
                    </div>
                    <Switch
                      id="includeImages"
                      checked={exportOptions.includeImages}
                      onCheckedChange={(checked) => 
                        setExportOptions(prev => ({ ...prev, includeImages: checked }))
                      }
                    />
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Preview */}
          <div className="p-4 bg-muted/50 rounded-lg">
            <h4 className="font-medium mb-2">Export Preview</h4>
            <div className="space-y-2 text-sm text-muted-foreground">
              <div className="flex items-center space-x-2">
                <FileText className="h-4 w-4" />
                <span>Format: {selectedFormat.toUpperCase()}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span>Records: {getDataCount()}</span>
              </div>
              {exportOptions.includeQRCodes && (
                <div className="flex items-center space-x-2">
                  <QrCode className="h-4 w-4" />
                  <span>QR codes included</span>
                </div>
              )}
              {eventName && (
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span>Event: {eventName}</span>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={onClose} disabled={isExporting}>
              Cancel
            </Button>
            <Button onClick={handleExport} disabled={isExporting}>
              {isExporting ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Exporting...
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Export {selectedFormat.toUpperCase()}
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export function QuickExportButtons({ 
  type, 
  data, 
  eventName 
}: { 
  type: 'attendees' | 'analytics'
  data: any
  eventName?: string 
}) {
  const [showDialog, setShowDialog] = useState(false)

  const quickExports = [
    { format: 'csv', label: 'CSV', icon: FileText },
    { format: 'pdf', label: 'PDF', icon: FileText },
    { format: 'zip', label: 'ZIP', icon: Archive }
  ]

  const handleQuickExport = async (format: string) => {
    // Quick export without dialog
    try {
      let blob: Blob
      let filename: string

      switch (format) {
        case 'csv':
          if (type === 'attendees') {
            const csvContent = JSON.stringify(data, null, 2) // Simplified for demo
            blob = new Blob([csvContent], { type: 'text/csv' })
          } else {
            blob = await generateAnalyticsExport(data, 'csv')
          }
          filename = getExportFilename(type, 'csv', eventName)
          break
        
        default:
          setShowDialog(true)
          return
      }

      downloadBlob(blob, filename)
    } catch (error) {
      console.error('Quick export failed:', error)
      setShowDialog(true)
    }
  }

  return (
    <>
      <div className="flex space-x-2">
        {quickExports.map((exportType) => {
          const Icon = exportType.icon
          return (
            <Button
              key={exportType.format}
              variant="outline"
              size="sm"
              onClick={() => handleQuickExport(exportType.format)}
            >
              <Icon className="mr-2 h-3 w-3" />
              {exportType.label}
            </Button>
          )
        })}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowDialog(true)}
        >
          More Options...
        </Button>
      </div>

      {showDialog && (
        <ExportDialog
          type={type}
          data={data}
          eventName={eventName}
          onClose={() => setShowDialog(false)}
        />
      )}
    </>
  )
}
