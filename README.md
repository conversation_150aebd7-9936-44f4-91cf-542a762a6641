# FoodScan - Multi-Event Food Management System

A comprehensive, modern web application for managing food distribution across multiple events with QR code scanning, real-time analytics, and offline support.

## 🚀 Features

### Core Functionality

- **Multi-Event Management**: Create and manage multiple events with custom branding and meal configurations
- **QR Code Generation & Scanning**: Automatic QR code generation for attendees with camera-based scanning
- **Real-time Analytics**: Live dashboard with comprehensive metrics and reporting
- **Offline Support**: PWA with offline scanning and automatic sync when online
- **Export & Reporting**: Multiple export formats (CSV, PDF, ZIP) with customizable reports

### Technical Highlights

- **Progressive Web App (PWA)**: Installable app with offline capabilities
- **Real-time Synchronization**: WebSocket-based live updates across all connected devices
- **Responsive Design**: Mobile-first design that works on all devices
- **Accessibility Compliant**: WCAG 2.1 AA compliant with screen reader support
- **Performance Optimized**: Virtual scrolling, lazy loading, and performance monitoring
- **Comprehensive Testing**: Unit tests, integration tests, and accessibility tests

## 🛠 Tech Stack

### Frontend

- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Accessible component primitives
- **Recharts** - Data visualization library

### Backend & Database

- **Next.js API Routes** - Serverless API endpoints
- **Prisma** - Database ORM
- **PostgreSQL** - Primary database
- **NextAuth.js** - Authentication system

### Real-time & Offline

- **Socket.IO** - Real-time communication
- **Service Workers** - Offline functionality
- **IndexedDB** - Client-side storage

### QR Code & Scanning

- **QRCode.js** - QR code generation
- **qr-scanner** - Camera-based QR code scanning
- **Canvas API** - Image processing

### Testing & Quality

- **Jest** - Testing framework
- **React Testing Library** - Component testing
- **ESLint** - Code linting
- **Prettier** - Code formatting

## 📦 Installation

### Prerequisites

- Node.js 18+
- PostgreSQL database
- npm or yarn package manager

### Setup

1. **Clone the repository**

   ```bash
   git clone <repository-url>
   cd food-management-system
   ```

2. **Install dependencies**

   ```bash
   npm install
   ```

3. **Environment setup**

   ```bash
   cp .env.example .env.local
   ```

   Configure the following environment variables:

   ```env
   # Database
   DATABASE_URL="postgresql://username:password@localhost:5432/foodscan"

   # NextAuth
   NEXTAUTH_SECRET="your-secret-key"
   NEXTAUTH_URL="http://localhost:3000"

   # WebSocket (optional)
   NEXT_PUBLIC_WS_URL="ws://localhost:3001"
   ```

4. **Database setup**

   ```bash
   npx prisma generate
   npx prisma db push
   npx prisma db seed
   ```

5. **Start development server**

   ```bash
   npm run dev
   ```

6. **Access the application**
   - Open http://localhost:3000
   - Default admin: <EMAIL> / admin123
   - Default scanner: <EMAIL> / scanner123

## 🏗 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── dashboard/         # Dashboard page
│   ├── scanner/           # QR scanning interface
│   ├── events/            # Event management
│   ├── attendees/         # Attendee management
│   └── auth/              # Authentication pages
├── components/            # React components
│   ├── ui/                # Base UI components
│   ├── layout/            # Layout components
│   ├── scanner/           # Scanner components
│   ├── analytics/         # Analytics components
│   ├── export/            # Export components
│   ├── realtime/          # Real-time components
│   └── pwa/               # PWA components
├── lib/                   # Utility libraries
│   ├── utils.ts           # General utilities
│   ├── qr-code.ts         # QR code utilities
│   ├── csv-utils.ts       # CSV processing
│   ├── export-utils.ts    # Export functionality
│   ├── websocket.ts       # WebSocket client
│   ├── pwa.ts             # PWA utilities
│   ├── performance.ts     # Performance monitoring
│   ├── error-handling.ts  # Error management
│   └── accessibility.ts   # Accessibility utilities
├── __tests__/             # Test files
└── types/                 # TypeScript type definitions
```

## 🎯 Usage Guide

### For Event Administrators

1. **Create an Event**

   - Navigate to Events → Create Event
   - Configure event details, dates, and branding
   - Set up meal types and quantities

2. **Manage Attendees**

   - Import attendees via CSV upload
   - Generate QR codes for all attendees
   - Export attendee lists and badges

3. **Monitor Progress**
   - View real-time dashboard
   - Track scanning progress
   - Generate comprehensive reports

### For Scanner Operators

1. **Access Scanner Interface**

   - Login with scanner credentials
   - Navigate to Scanner page
   - Grant camera permissions

2. **Scan QR Codes**

   - Point camera at attendee QR codes
   - Verify successful scans
   - Handle offline scenarios

3. **View Scan History**
   - Review recent scans
   - Check for errors
   - Monitor sync status

## 📊 Analytics & Reporting

### Real-time Dashboard

- Live scan counts and completion rates
- Hourly activity charts
- Meal type distribution
- System health monitoring

### Export Options

- **CSV**: Spreadsheet-compatible data
- **PDF**: Formatted reports and badges
- **ZIP**: Complete package with QR codes
- **JSON**: Raw data for integrations

### Performance Metrics

- Page load times
- Scan success rates
- System response times
- Error tracking

## 🧪 Testing

### Run Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- qr-scanner.test.tsx
```

### Test Categories

- **Unit Tests**: Individual component and utility testing
- **Integration Tests**: Feature workflow testing
- **Accessibility Tests**: WCAG compliance verification
- **Performance Tests**: Load time and responsiveness testing

## 🚀 Deployment

### Production Build

```bash
npm run build
npm start
```

### Environment Variables (Production)

```env
NODE_ENV=production
DATABASE_URL="your-production-database-url"
NEXTAUTH_SECRET="your-production-secret"
NEXTAUTH_URL="https://your-domain.com"
```

### PWA Deployment

- Ensure HTTPS is enabled
- Configure service worker caching
- Set up push notification certificates
- Test offline functionality

## 🔒 Security

### Authentication

- NextAuth.js with secure session management
- Role-based access control (Admin, Scanner)
- CSRF protection enabled

### Data Protection

- Input validation and sanitization
- SQL injection prevention via Prisma
- XSS protection with Content Security Policy

### Privacy

- GDPR compliant data handling
- Configurable data retention policies
- Secure QR code generation

## 🌐 Browser Support

### Supported Browsers

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

### Required Features

- Camera API (for QR scanning)
- Service Workers (for offline support)
- IndexedDB (for local storage)
- WebSocket (for real-time updates)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

### Development Guidelines

- Follow TypeScript best practices
- Maintain test coverage above 70%
- Ensure accessibility compliance
- Document new features

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

### Common Issues

- **Camera not working**: Check browser permissions and HTTPS
- **Offline sync failing**: Verify service worker registration
- **QR codes not generating**: Check canvas API support

### Getting Help

- Check the documentation
- Review test files for usage examples
- Open an issue on GitHub

## 🔄 Changelog

### Version 1.0.0

- Initial release with core functionality
- Multi-event management
- QR code scanning and generation
- Real-time analytics
- PWA support with offline capabilities
- Comprehensive export system
- Performance optimizations and testing suite

---

Built with ❤️ using Next.js, TypeScript, and modern web technologies.
