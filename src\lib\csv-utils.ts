export interface AttendeeCSVRow {
  name: string
  email?: string
  dietaryRestrictions?: string
  specialNotes?: string
  qrCode?: string
}

export interface AttendeeExportRow extends AttendeeCSVRow {
  eventName: string
  scannedAt?: string
  createdAt: string
}

/**
 * Parse CSV content to attendee data
 */
export function parseCSV(csvContent: string): AttendeeCSVRow[] {
  const lines = csvContent.trim().split('\n')
  if (lines.length < 2) {
    throw new Error('CSV must contain at least a header row and one data row')
  }

  const headers = lines[0].split(',').map(h => h.trim().toLowerCase())
  const requiredHeaders = ['name']
  const optionalHeaders = ['email', 'dietaryrestrictions', 'specialnotes', 'qrcode']

  // Check for required headers
  const missingHeaders = requiredHeaders.filter(header => 
    !headers.some(h => h.includes(header))
  )
  
  if (missingHeaders.length > 0) {
    throw new Error(`Missing required headers: ${missingHeaders.join(', ')}`)
  }

  // Map headers to their indices
  const headerMap: Record<string, number> = {}
  headers.forEach((header, index) => {
    if (header.includes('name')) headerMap.name = index
    if (header.includes('email')) headerMap.email = index
    if (header.includes('dietary')) headerMap.dietaryRestrictions = index
    if (header.includes('special') || header.includes('note')) headerMap.specialNotes = index
    if (header.includes('qr') || header.includes('code')) headerMap.qrCode = index
  })

  const attendees: AttendeeCSVRow[] = []
  const errors: string[] = []

  for (let i = 1; i < lines.length; i++) {
    const line = lines[i].trim()
    if (!line) continue // Skip empty lines

    const values = parseCSVLine(line)
    
    try {
      const attendee: AttendeeCSVRow = {
        name: values[headerMap.name]?.trim() || '',
        email: values[headerMap.email]?.trim() || undefined,
        dietaryRestrictions: values[headerMap.dietaryRestrictions]?.trim() || undefined,
        specialNotes: values[headerMap.specialNotes]?.trim() || undefined,
        qrCode: values[headerMap.qrCode]?.trim() || undefined,
      }

      // Validate required fields
      if (!attendee.name) {
        errors.push(`Row ${i + 1}: Name is required`)
        continue
      }

      // Validate email format if provided
      if (attendee.email && !isValidEmail(attendee.email)) {
        errors.push(`Row ${i + 1}: Invalid email format`)
        continue
      }

      attendees.push(attendee)
    } catch (error) {
      errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  if (errors.length > 0) {
    throw new Error(`CSV parsing errors:\n${errors.join('\n')}`)
  }

  return attendees
}

/**
 * Parse a single CSV line handling quoted values
 */
function parseCSVLine(line: string): string[] {
  const result: string[] = []
  let current = ''
  let inQuotes = false
  let i = 0

  while (i < line.length) {
    const char = line[i]
    
    if (char === '"') {
      if (inQuotes && line[i + 1] === '"') {
        // Escaped quote
        current += '"'
        i += 2
      } else {
        // Toggle quote state
        inQuotes = !inQuotes
        i++
      }
    } else if (char === ',' && !inQuotes) {
      // End of field
      result.push(current)
      current = ''
      i++
    } else {
      current += char
      i++
    }
  }
  
  result.push(current) // Add the last field
  return result
}

/**
 * Convert attendees to CSV format
 */
export function attendeesToCSV(attendees: AttendeeExportRow[]): string {
  if (attendees.length === 0) {
    return 'Name,Email,Event,Dietary Restrictions,Special Notes,QR Code,Scanned At,Created At\n'
  }

  const headers = [
    'Name',
    'Email', 
    'Event',
    'Dietary Restrictions',
    'Special Notes',
    'QR Code',
    'Scanned At',
    'Created At'
  ]

  const csvRows = [headers.join(',')]

  attendees.forEach(attendee => {
    const row = [
      escapeCSVField(attendee.name),
      escapeCSVField(attendee.email || ''),
      escapeCSVField(attendee.eventName),
      escapeCSVField(attendee.dietaryRestrictions || ''),
      escapeCSVField(attendee.specialNotes || ''),
      escapeCSVField(attendee.qrCode || ''),
      escapeCSVField(attendee.scannedAt || ''),
      escapeCSVField(attendee.createdAt)
    ]
    csvRows.push(row.join(','))
  })

  return csvRows.join('\n')
}

/**
 * Escape CSV field value
 */
function escapeCSVField(value: string): string {
  if (!value) return ''
  
  // If the value contains comma, newline, or quote, wrap in quotes
  if (value.includes(',') || value.includes('\n') || value.includes('"')) {
    // Escape existing quotes by doubling them
    const escaped = value.replace(/"/g, '""')
    return `"${escaped}"`
  }
  
  return value
}

/**
 * Validate email format
 */
function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Download CSV file
 */
export function downloadCSV(csvContent: string, filename: string): void {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  const link = document.createElement('a')
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', filename)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}

/**
 * Read file as text
 */
export function readFileAsText(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (event) => {
      const result = event.target?.result
      if (typeof result === 'string') {
        resolve(result)
      } else {
        reject(new Error('Failed to read file as text'))
      }
    }
    
    reader.onerror = () => {
      reject(new Error('Error reading file'))
    }
    
    reader.readAsText(file)
  })
}

/**
 * Validate CSV file
 */
export function validateCSVFile(file: File): { valid: boolean; error?: string } {
  // Check file type
  if (!file.type.includes('csv') && !file.name.endsWith('.csv')) {
    return { valid: false, error: 'Please select a CSV file' }
  }

  // Check file size (max 10MB)
  const maxSize = 10 * 1024 * 1024 // 10MB
  if (file.size > maxSize) {
    return { valid: false, error: 'File size must be less than 10MB' }
  }

  return { valid: true }
}

/**
 * Generate CSV template for attendee import
 */
export function generateCSVTemplate(): string {
  const headers = [
    'Name',
    'Email',
    'Dietary Restrictions',
    'Special Notes'
  ]

  const sampleData = [
    ['John Doe', '<EMAIL>', 'Vegetarian', 'Wheelchair accessible seating'],
    ['Jane Smith', '<EMAIL>', 'Gluten-free', ''],
    ['Mike Johnson', '<EMAIL>', '', 'VIP guest']
  ]

  const csvRows = [headers.join(',')]
  sampleData.forEach(row => {
    csvRows.push(row.map(escapeCSVField).join(','))
  })

  return csvRows.join('\n')
}

/**
 * Download CSV template
 */
export function downloadCSVTemplate(): void {
  const template = generateCSVTemplate()
  downloadCSV(template, 'attendee-import-template.csv')
}
