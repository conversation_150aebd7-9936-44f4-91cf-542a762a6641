const { MongoClient } = require('mongodb');
const bcrypt = require('bcryptjs');

const MONGODB_URI = "mongodb://localhost:27017";
const DB_NAME = "foodscan";

async function testUsersCRUD() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db(DB_NAME);
    const usersCollection = db.collection('users');
    const preferencesCollection = db.collection('user_preferences');
    
    console.log('\n👤 Testing Users CRUD Operations...\n');
    
    // 1. CREATE - Test user creation
    console.log('1️⃣ Testing CREATE operation...');
    const hashedPassword = await bcrypt.hash('testpassword123', 12);
    const newUser = {
      id: 'test-user-' + Date.now(),
      name: 'Test User',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'ADMIN',
      createdAt: new Date(),
      updatedAt: new Date(),
      lastLoginAt: null
    };
    
    const createResult = await usersCollection.insertOne(newUser);
    console.log('✅ User created with ID:', createResult.insertedId);
    console.log('   Email:', newUser.email);
    console.log('   Role:', newUser.role);
    
    // 2. READ - Test user retrieval
    console.log('\n2️⃣ Testing READ operation...');
    const foundUser = await usersCollection.findOne({ id: newUser.id });
    if (foundUser) {
      console.log('✅ User found:', foundUser.name);
      console.log('   Email:', foundUser.email);
      console.log('   Role:', foundUser.role);
      console.log('   Created At:', foundUser.createdAt);
      
      // Test password verification
      const isPasswordValid = await bcrypt.compare('testpassword123', foundUser.password);
      console.log('   Password verification:', isPasswordValid ? '✅ Valid' : '❌ Invalid');
    } else {
      console.log('❌ User not found');
    }
    
    // Test reading all users
    const allUsers = await usersCollection.find({}).toArray();
    console.log('✅ Found', allUsers.length, 'total users in database');
    allUsers.forEach(user => {
      console.log('   -', user.name, '(' + user.email + ') - Role:', user.role);
    });
    
    // 3. UPDATE - Test user modification
    console.log('\n3️⃣ Testing UPDATE operation...');
    
    // Test name update
    const nameUpdateResult = await usersCollection.updateOne(
      { id: newUser.id },
      { 
        $set: { 
          name: 'Updated Test User',
          updatedAt: new Date()
        }
      }
    );
    
    if (nameUpdateResult.modifiedCount > 0) {
      console.log('✅ User name updated successfully');
      const updatedUser = await usersCollection.findOne({ id: newUser.id });
      console.log('   New name:', updatedUser.name);
    }
    
    // Test password update
    const newHashedPassword = await bcrypt.hash('newpassword456', 12);
    const passwordUpdateResult = await usersCollection.updateOne(
      { id: newUser.id },
      { 
        $set: { 
          password: newHashedPassword,
          updatedAt: new Date()
        }
      }
    );
    
    if (passwordUpdateResult.modifiedCount > 0) {
      console.log('✅ User password updated successfully');
      const userWithNewPassword = await usersCollection.findOne({ id: newUser.id });
      const isNewPasswordValid = await bcrypt.compare('newpassword456', userWithNewPassword.password);
      console.log('   New password verification:', isNewPasswordValid ? '✅ Valid' : '❌ Invalid');
    }
    
    // Test last login update
    const loginUpdateResult = await usersCollection.updateOne(
      { id: newUser.id },
      { 
        $set: { 
          lastLoginAt: new Date()
        }
      }
    );
    
    if (loginUpdateResult.modifiedCount > 0) {
      console.log('✅ Last login time updated successfully');
    }
    
    // 4. Test User Preferences CRUD
    console.log('\n🎛️ Testing USER PREFERENCES...');
    const userPreferences = {
      userId: newUser.id,
      theme: 'dark',
      notifications: true,
      emailNotifications: false,
      autoLogout: 60,
      language: 'en',
      timezone: 'UTC',
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const prefsCreateResult = await preferencesCollection.insertOne(userPreferences);
    console.log('✅ User preferences created');
    
    // Update preferences
    const prefsUpdateResult = await preferencesCollection.updateOne(
      { userId: newUser.id },
      { 
        $set: { 
          theme: 'light',
          autoLogout: 30,
          updatedAt: new Date()
        }
      }
    );
    
    if (prefsUpdateResult.modifiedCount > 0) {
      console.log('✅ User preferences updated');
      const updatedPrefs = await preferencesCollection.findOne({ userId: newUser.id });
      console.log('   Theme:', updatedPrefs.theme);
      console.log('   Auto logout:', updatedPrefs.autoLogout, 'minutes');
    }
    
    // 5. DELETE - Test user deletion
    console.log('\n4️⃣ Testing DELETE operation...');
    
    // Delete user preferences first
    const prefsDeleteResult = await preferencesCollection.deleteOne({ userId: newUser.id });
    console.log('✅ User preferences deleted');
    
    // Delete the user
    const deleteResult = await usersCollection.deleteOne({ id: newUser.id });
    if (deleteResult.deletedCount > 0) {
      console.log('✅ User deleted successfully');
      
      // Verify deletion
      const deletedUser = await usersCollection.findOne({ id: newUser.id });
      if (!deletedUser) {
        console.log('✅ Confirmed: User no longer exists in database');
      } else {
        console.log('❌ Error: User still exists after deletion');
      }
    } else {
      console.log('❌ User deletion failed');
    }
    
    // Test role-based access
    console.log('\n🔐 Testing ROLE-BASED ACCESS...');
    const roleUsers = await usersCollection.find({}).toArray();
    const adminUsers = roleUsers.filter(u => u.role === 'ADMIN');
    const scannerUsers = roleUsers.filter(u => u.role === 'SCANNER');
    
    console.log('✅ Admin users:', adminUsers.length);
    adminUsers.forEach(user => console.log('   -', user.name, '(' + user.email + ')'));
    
    console.log('✅ Scanner users:', scannerUsers.length);
    scannerUsers.forEach(user => console.log('   -', user.name, '(' + user.email + ')'));
    
    console.log('\n🎉 Users CRUD testing completed!');
    
  } catch (error) {
    console.error('❌ Error during CRUD testing:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testUsersCRUD().catch(console.error);
