const { MongoClient } = require('mongodb');

const MONGODB_URI = "mongodb://localhost:27017";
const DB_NAME = "foodscan";

async function testEventsCRUD() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db(DB_NAME);
    const eventsCollection = db.collection('Event');
    const mealTypesCollection = db.collection('MealType');
    
    console.log('\n📊 Testing Events CRUD Operations...\n');
    
    // 1. CREATE - Test event creation
    console.log('1️⃣ Testing CREATE operation...');
    const newEvent = {
      id: 'test-event-' + Date.now(),
      name: 'Test Event CRUD',
      description: 'Testing CRUD operations for events',
      startDate: new Date('2024-03-15T09:00:00Z'),
      endDate: new Date('2024-03-15T17:00:00Z'),
      serviceStartTime: new Date('2024-03-15T12:00:00Z'),
      serviceEndTime: new Date('2024-03-15T14:00:00Z'),
      primaryColor: '#ff6b6b',
      secondaryColor: '#ee5a52',
      logoUrl: 'https://example.com/logo.png',
      creatorId: '688aa226fcef298f4140e860', // Admin user ID
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    const createResult = await eventsCollection.insertOne(newEvent);
    console.log('✅ Event created with ID:', createResult.insertedId);
    
    // Create associated meal types
    const mealTypes = [
      {
        id: 'meal-1-' + Date.now(),
        name: 'Main Course',
        description: 'Delicious main course meal',
        maxQuantityPerAttendee: 1,
        totalQuantity: 100,
        isVegetarian: false,
        isVegan: false,
        isGlutenFree: false,
        eventId: newEvent.id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'meal-2-' + Date.now(),
        name: 'Vegetarian Option',
        description: 'Healthy vegetarian meal',
        maxQuantityPerAttendee: 1,
        totalQuantity: 50,
        isVegetarian: true,
        isVegan: false,
        isGlutenFree: true,
        eventId: newEvent.id,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    await mealTypesCollection.insertMany(mealTypes);
    console.log('✅ Meal types created for event');
    
    // 2. READ - Test event retrieval
    console.log('\n2️⃣ Testing READ operation...');
    const foundEvent = await eventsCollection.findOne({ id: newEvent.id });
    if (foundEvent) {
      console.log('✅ Event found:', foundEvent.name);
      console.log('   Description:', foundEvent.description);
      console.log('   Start Date:', foundEvent.startDate);
      console.log('   Primary Color:', foundEvent.primaryColor);
    } else {
      console.log('❌ Event not found');
    }
    
    // Get meal types for the event
    const eventMealTypes = await mealTypesCollection.find({ eventId: newEvent.id }).toArray();
    console.log('✅ Found', eventMealTypes.length, 'meal types for event');
    eventMealTypes.forEach(meal => {
      console.log('   -', meal.name, '(Vegetarian:', meal.isVegetarian + ')');
    });
    
    // 3. UPDATE - Test event modification
    console.log('\n3️⃣ Testing UPDATE operation...');
    const updateData = {
      name: 'Updated Test Event CRUD',
      description: 'Updated description for CRUD testing',
      primaryColor: '#4ecdc4',
      secondaryColor: '#44a08d',
      updatedAt: new Date()
    };
    
    const updateResult = await eventsCollection.updateOne(
      { id: newEvent.id },
      { $set: updateData }
    );
    
    if (updateResult.modifiedCount > 0) {
      console.log('✅ Event updated successfully');
      const updatedEvent = await eventsCollection.findOne({ id: newEvent.id });
      console.log('   New name:', updatedEvent.name);
      console.log('   New description:', updatedEvent.description);
      console.log('   New primary color:', updatedEvent.primaryColor);
    } else {
      console.log('❌ Event update failed');
    }
    
    // 4. DELETE - Test event deletion
    console.log('\n4️⃣ Testing DELETE operation...');
    
    // First delete associated meal types
    const mealDeleteResult = await mealTypesCollection.deleteMany({ eventId: newEvent.id });
    console.log('✅ Deleted', mealDeleteResult.deletedCount, 'meal types');
    
    // Then delete the event
    const deleteResult = await eventsCollection.deleteOne({ id: newEvent.id });
    if (deleteResult.deletedCount > 0) {
      console.log('✅ Event deleted successfully');
      
      // Verify deletion
      const deletedEvent = await eventsCollection.findOne({ id: newEvent.id });
      if (!deletedEvent) {
        console.log('✅ Confirmed: Event no longer exists in database');
      } else {
        console.log('❌ Error: Event still exists after deletion');
      }
    } else {
      console.log('❌ Event deletion failed');
    }
    
    console.log('\n🎉 Events CRUD testing completed!');
    
  } catch (error) {
    console.error('❌ Error during CRUD testing:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testEventsCRUD().catch(console.error);
