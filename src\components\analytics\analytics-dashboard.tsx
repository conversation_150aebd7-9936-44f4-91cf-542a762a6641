"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart
} from "recharts"
import { 
  TrendingUp, 
  Users, 
  Clock, 
  Calendar,
  Download,
  RefreshCw,
  Filter
} from "lucide-react"

// Mock data - will be replaced with real data
const hourlyData = [
  { hour: '08:00', scans: 12, attendees: 45 },
  { hour: '09:00', scans: 28, attendees: 67 },
  { hour: '10:00', scans: 45, attendees: 89 },
  { hour: '11:00', scans: 67, attendees: 123 },
  { hour: '12:00', scans: 89, attendees: 156 },
  { hour: '13:00', scans: 134, attendees: 234 },
  { hour: '14:00', scans: 98, attendees: 198 },
  { hour: '15:00', scans: 76, attendees: 167 },
  { hour: '16:00', scans: 54, attendees: 134 },
  { hour: '17:00', scans: 32, attendees: 98 },
]

const mealTypeData = [
  { name: 'Main Course', value: 450, color: '#3b82f6' },
  { name: 'Appetizer', value: 320, color: '#10b981' },
  { name: 'Dessert', value: 280, color: '#f59e0b' },
  { name: 'Beverage', value: 520, color: '#ef4444' },
]

const dailyTrends = [
  { date: '2024-01-15', scans: 234, completion: 78 },
  { date: '2024-01-16', scans: 267, completion: 82 },
  { date: '2024-01-17', scans: 298, completion: 85 },
  { date: '2024-01-18', scans: 312, completion: 89 },
  { date: '2024-01-19', scans: 345, completion: 92 },
  { date: '2024-01-20', scans: 378, completion: 95 },
  { date: '2024-01-21', scans: 402, completion: 98 },
]

interface AnalyticsDashboardProps {
  eventId?: string
  dateRange?: { from: Date; to: Date }
}

export function AnalyticsDashboard({ eventId, dateRange }: AnalyticsDashboardProps) {
  const [selectedMetric, setSelectedMetric] = useState<'scans' | 'completion'>('scans')
  const [timeRange, setTimeRange] = useState<'today' | 'week' | 'month'>('today')

  const totalScans = hourlyData.reduce((sum, item) => sum + item.scans, 0)
  const totalAttendees = 850
  const completionRate = ((totalScans / totalAttendees) * 100).toFixed(1)
  const peakHour = hourlyData.reduce((max, item) => item.scans > max.scans ? item : max)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Real-time insights and performance metrics
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Scans</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalScans}</div>
            <p className="text-xs text-muted-foreground">
              +12% from yesterday
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completionRate}%</div>
            <p className="text-xs text-muted-foreground">
              {totalScans} of {totalAttendees} attendees
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Peak Hour</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{peakHour.hour}</div>
            <p className="text-xs text-muted-foreground">
              {peakHour.scans} scans
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Scan Time</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0.8s</div>
            <p className="text-xs text-muted-foreground">
              -0.2s from yesterday
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts Grid */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Hourly Activity */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle>Hourly Activity</CardTitle>
                <CardDescription>
                  Scan activity throughout the day
                </CardDescription>
              </div>
              <div className="flex space-x-1">
                <Button
                  variant={selectedMetric === 'scans' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedMetric('scans')}
                >
                  Scans
                </Button>
                <Button
                  variant={selectedMetric === 'completion' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedMetric('completion')}
                >
                  Attendees
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={hourlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Area
                  type="monotone"
                  dataKey={selectedMetric === 'scans' ? 'scans' : 'attendees'}
                  stroke="#3b82f6"
                  fill="#3b82f6"
                  fillOpacity={0.3}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Meal Type Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Meal Type Distribution</CardTitle>
            <CardDescription>
              Breakdown of meal consumption
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={mealTypeData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {mealTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Weekly Trends */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Weekly Trends</CardTitle>
              <CardDescription>
                Daily scan activity and completion rates
              </CardDescription>
            </div>
            <div className="flex space-x-1">
              {(['today', 'week', 'month'] as const).map((range) => (
                <Button
                  key={range}
                  variant={timeRange === range ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setTimeRange(range)}
                  className="capitalize"
                >
                  {range}
                </Button>
              ))}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={dailyTrends}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="date" 
                tickFormatter={(value) => new Date(value).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
              />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip 
                labelFormatter={(value) => new Date(value).toLocaleDateString()}
              />
              <Bar yAxisId="left" dataKey="scans" fill="#3b82f6" name="Scans" />
              <Line 
                yAxisId="right" 
                type="monotone" 
                dataKey="completion" 
                stroke="#10b981" 
                strokeWidth={2}
                name="Completion %"
              />
            </LineChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Performance Insights */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Performance Insights</CardTitle>
            <CardDescription>
              Key performance indicators
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Success Rate</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-muted rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '98%' }} />
                </div>
                <span className="text-sm font-medium">98%</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm">Error Rate</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-muted rounded-full h-2">
                  <div className="bg-red-500 h-2 rounded-full" style={{ width: '2%' }} />
                </div>
                <span className="text-sm font-medium">2%</span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm">Avg. Queue Time</span>
              <Badge variant="success">0.3s</Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm">Peak Throughput</span>
              <Badge variant="info">134 scans/hour</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Health</CardTitle>
            <CardDescription>
              Real-time system status
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Scanner Devices</span>
              <Badge variant="success">3 Active</Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm">Network Status</span>
              <Badge variant="success">Online</Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm">Database</span>
              <Badge variant="success">Connected</Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm">Last Sync</span>
              <span className="text-sm text-muted-foreground">2 seconds ago</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
