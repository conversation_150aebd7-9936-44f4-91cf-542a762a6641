import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/prisma"

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { theme, notifications, emailNotifications, autoLogout, language, timezone } = body

    // Check if user preferences exist
    const existingPreferences = await prisma.userPreferences.findUnique({
      where: { userId: session.user.id }
    })

    const preferencesData = {
      theme: theme || 'light',
      notifications: notifications ?? true,
      emailNotifications: emailNotifications ?? true,
      autoLogout: autoLogout || 30,
      language: language || 'en',
      timezone: timezone || 'UTC'
    }

    if (existingPreferences) {
      // Update existing preferences
      await prisma.userPreferences.update({
        where: { userId: session.user.id },
        data: preferencesData
      })
    } else {
      // Create new preferences
      await prisma.userPreferences.create({
        data: {
          userId: session.user.id,
          ...preferencesData
        }
      })
    }

    return NextResponse.json({ message: "Preferences updated successfully" })

  } catch (error) {
    console.error("Error updating preferences:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const preferences = await prisma.userPreferences.findUnique({
      where: { userId: session.user.id }
    })

    // Return default preferences if none exist
    const defaultPreferences = {
      theme: 'light',
      notifications: true,
      emailNotifications: true,
      autoLogout: 30,
      language: 'en',
      timezone: 'UTC'
    }

    return NextResponse.json(preferences || defaultPreferences)

  } catch (error) {
    console.error("Error fetching preferences:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
