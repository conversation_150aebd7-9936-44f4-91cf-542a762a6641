"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  CheckCircle, 
  Clock,
  AlertCircle,
  Upload
} from "lucide-react"
import { OfflineStorage, requestBackgroundSync, useNetworkStatus } from "@/lib/pwa"

export function OfflineSyncStatus() {
  const { isOnline, isOffline } = useNetworkStatus()
  const [pendingScans, setPendingScans] = useState(0)
  const [isSyncing, setIsSyncing] = useState(false)
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null)
  const [syncError, setSyncError] = useState<string | null>(null)

  const offlineStorage = new OfflineStorage()

  useEffect(() => {
    checkPendingScans()
    
    // Listen for sync completion
    const handleSyncComplete = (event: CustomEvent) => {
      console.log('Offline sync completed:', event.detail)
      setIsSyncing(false)
      setLastSyncTime(new Date())
      setSyncError(null)
      checkPendingScans()
    }

    const handleSyncError = (event: CustomEvent) => {
      console.error('Offline sync error:', event.detail)
      setIsSyncing(false)
      setSyncError(event.detail.error || 'Sync failed')
    }

    window.addEventListener('offline-sync-complete', handleSyncComplete as EventListener)
    window.addEventListener('offline-sync-error', handleSyncError as EventListener)

    return () => {
      window.removeEventListener('offline-sync-complete', handleSyncComplete as EventListener)
      window.removeEventListener('offline-sync-error', handleSyncError as EventListener)
    }
  }, [])

  useEffect(() => {
    // Auto-sync when coming back online
    if (isOnline && pendingScans > 0) {
      handleManualSync()
    }
  }, [isOnline, pendingScans])

  const checkPendingScans = async () => {
    try {
      const unsynced = await offlineStorage.getUnsyncedScans()
      setPendingScans(unsynced.length)
    } catch (error) {
      console.error('Error checking pending scans:', error)
    }
  }

  const handleManualSync = async () => {
    if (!isOnline || isSyncing) return

    setIsSyncing(true)
    setSyncError(null)

    try {
      await requestBackgroundSync('sync-offline-scans')
      console.log('Manual sync requested')
    } catch (error) {
      console.error('Manual sync failed:', error)
      setSyncError('Failed to start sync')
      setIsSyncing(false)
    }
  }

  const formatLastSync = (date: Date | null) => {
    if (!date) return 'Never'
    
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`
    return date.toLocaleDateString()
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {isOnline ? (
              <Wifi className="h-5 w-5 text-green-500" />
            ) : (
              <WifiOff className="h-5 w-5 text-red-500" />
            )}
            <CardTitle className="text-lg">Sync Status</CardTitle>
          </div>
          <Badge variant={isOnline ? "success" : "warning"}>
            {isOnline ? "Online" : "Offline"}
          </Badge>
        </div>
        <CardDescription>
          {isOnline 
            ? "Connected - data syncs automatically" 
            : "Offline - scans saved locally"
          }
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Pending Scans */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">Pending Scans</span>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={pendingScans > 0 ? "warning" : "success"}>
              {pendingScans}
            </Badge>
            {pendingScans > 0 && isOnline && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleManualSync}
                disabled={isSyncing}
              >
                {isSyncing ? (
                  <>
                    <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                    Syncing...
                  </>
                ) : (
                  <>
                    <Upload className="h-3 w-3 mr-1" />
                    Sync Now
                  </>
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Last Sync */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm">Last Sync</span>
          </div>
          <span className="text-sm text-muted-foreground">
            {formatLastSync(lastSyncTime)}
          </span>
        </div>

        {/* Sync Error */}
        {syncError && (
          <div className="flex items-start space-x-2 p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
            <AlertCircle className="h-4 w-4 text-destructive mt-0.5" />
            <div className="text-sm text-destructive">
              <div className="font-medium">Sync Error</div>
              <div className="mt-1">{syncError}</div>
            </div>
          </div>
        )}

        {/* Offline Mode Info */}
        {isOffline && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg dark:bg-yellow-950 dark:border-yellow-800">
            <div className="flex items-start space-x-2">
              <WifiOff className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800 dark:text-yellow-200">
                <div className="font-medium">Offline Mode</div>
                <div className="mt-1">
                  Scans are being saved locally and will sync automatically when connection is restored.
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Sync Success */}
        {isOnline && pendingScans === 0 && lastSyncTime && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-lg dark:bg-green-950 dark:border-green-800">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <div className="text-sm text-green-800 dark:text-green-200">
                <span className="font-medium">All data synced</span>
                <span className="ml-2">• {formatLastSync(lastSyncTime)}</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export function OfflineBanner() {
  const { isOffline } = useNetworkStatus()
  const [pendingScans, setPendingScans] = useState(0)

  const offlineStorage = new OfflineStorage()

  useEffect(() => {
    if (isOffline) {
      checkPendingScans()
    }
  }, [isOffline])

  const checkPendingScans = async () => {
    try {
      const unsynced = await offlineStorage.getUnsyncedScans()
      setPendingScans(unsynced.length)
    } catch (error) {
      console.error('Error checking pending scans:', error)
    }
  }

  if (!isOffline) return null

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-yellow-500 text-yellow-900 px-4 py-2">
      <div className="flex items-center justify-center space-x-2 text-sm font-medium">
        <WifiOff className="h-4 w-4" />
        <span>
          You're offline. {pendingScans > 0 && `${pendingScans} scans pending sync.`}
        </span>
      </div>
    </div>
  )
}
