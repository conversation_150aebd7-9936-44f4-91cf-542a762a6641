import { NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"

export async function GET() {
  try {
    // Test database connection
    await prisma.$connect()
    
    // Get basic stats
    const userCount = await prisma.user.count()
    const eventCount = await prisma.event.count()
    const attendeeCount = await prisma.attendee.count()
    
    return NextResponse.json({
      status: "healthy",
      timestamp: new Date().toISOString(),
      database: {
        status: "connected",
        provider: "mongodb",
        stats: {
          users: userCount,
          events: eventCount,
          attendees: attendeeCount
        }
      },
      version: "1.0.0"
    })
  } catch (error) {
    console.error("Health check failed:", error)
    return NextResponse.json({
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      database: {
        status: "disconnected",
        error: error instanceof Error ? error.message : "Unknown error"
      }
    }, { status: 503 })
  }
}
