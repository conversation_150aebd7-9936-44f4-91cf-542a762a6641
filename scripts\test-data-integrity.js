const { MongoClient } = require('mongodb');

const MONGODB_URI = "mongodb://localhost:27017";
const DB_NAME = "foodscan";

async function testDataIntegrity() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB');
    
    const db = client.db(DB_NAME);
    
    console.log('\n🔍 Testing Data Integrity & Relationships...\n');
    
    // 1. Test Collection Existence and Counts
    console.log('1️⃣ Testing COLLECTION EXISTENCE...');
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    
    const expectedCollections = [
      'users', 'events', 'attendees', 'meal_types', 
      'scan_logs', 'meal_consumptions', 'user_preferences'
    ];
    
    expectedCollections.forEach(expectedCol => {
      if (collectionNames.includes(expectedCol)) {
        console.log(`✅ Collection '${expectedCol}' exists`);
      } else {
        console.log(`❌ Collection '${expectedCol}' missing`);
      }
    });
    
    // Count documents in each collection
    console.log('\n📊 Document counts:');
    for (const colName of expectedCollections) {
      if (collectionNames.includes(colName)) {
        const count = await db.collection(colName).countDocuments();
        console.log(`   ${colName}: ${count} documents`);
      }
    }
    
    // 2. Test User-Event Relationships
    console.log('\n2️⃣ Testing USER-EVENT RELATIONSHIPS...');
    const events = await db.collection('events').find({}).toArray();
    const users = await db.collection('users').find({}).toArray();

    for (const event of events) {
      const creator = users.find(u => u._id.toString() === event.creatorId.toString());
      if (creator) {
        console.log(`✅ Event '${event.name}' has valid creator: ${creator.name}`);
      } else {
        console.log(`❌ Event '${event.name}' has invalid creator ID: ${event.creatorId}`);
      }
    }
    
    // 3. Test Event-Attendee Relationships
    console.log('\n3️⃣ Testing EVENT-ATTENDEE RELATIONSHIPS...');
    const attendees = await db.collection('attendees').find({}).toArray();

    for (const attendee of attendees) {
      const event = events.find(e => e._id.toString() === attendee.eventId.toString());
      if (event) {
        console.log(`✅ Attendee '${attendee.name}' belongs to valid event: ${event.name}`);
      } else {
        console.log(`❌ Attendee '${attendee.name}' has invalid event ID: ${attendee.eventId}`);
      }
    }
    
    // 4. Test Event-MealType Relationships
    console.log('\n4️⃣ Testing EVENT-MEALTYPE RELATIONSHIPS...');
    const mealTypes = await db.collection('meal_types').find({}).toArray();

    for (const mealType of mealTypes) {
      const event = events.find(e => e._id.toString() === mealType.eventId.toString());
      if (event) {
        console.log(`✅ Meal type '${mealType.name}' belongs to valid event: ${event.name}`);
      } else {
        console.log(`❌ Meal type '${mealType.name}' has invalid event ID: ${mealType.eventId}`);
      }
    }
    
    // 5. Test Scan Log Relationships
    console.log('\n5️⃣ Testing SCAN LOG RELATIONSHIPS...');
    const scanLogs = await db.collection('scan_logs').find({}).toArray();
    
    for (const scanLog of scanLogs) {
      const attendee = attendees.find(a => a._id.toString() === scanLog.attendeeId.toString());
      const scanner = users.find(u => u._id.toString() === scanLog.scannedBy.toString());

      if (attendee && scanner) {
        console.log(`✅ Scan log: ${attendee.name} scanned by ${scanner.name}`);
      } else {
        if (!attendee) console.log(`❌ Scan log has invalid attendee ID: ${scanLog.attendeeId}`);
        if (!scanner) console.log(`❌ Scan log has invalid scanner ID: ${scanLog.scannedBy}`);
      }
    }
    
    // 6. Test QR Code Uniqueness
    console.log('\n6️⃣ Testing QR CODE UNIQUENESS...');
    const qrCodes = await db.collection('attendees').distinct('qrCode');
    const totalAttendees = await db.collection('attendees').countDocuments();
    
    if (qrCodes.length === totalAttendees) {
      console.log('✅ All QR codes are unique');
    } else {
      console.log('❌ Duplicate QR codes found!');
      
      // Find duplicates
      const qrCodeCounts = await db.collection('attendees').aggregate([
        { $group: { _id: '$qrCode', count: { $sum: 1 } } },
        { $match: { count: { $gt: 1 } } }
      ]).toArray();
      
      qrCodeCounts.forEach(duplicate => {
        console.log(`   Duplicate QR code: ${duplicate._id} (${duplicate.count} times)`);
      });
    }
    
    // 7. Test Email Uniqueness
    console.log('\n7️⃣ Testing EMAIL UNIQUENESS...');
    const userEmails = await db.collection('users').distinct('email');
    const totalUsers = await db.collection('users').countDocuments();
    
    if (userEmails.length === totalUsers) {
      console.log('✅ All user emails are unique');
    } else {
      console.log('❌ Duplicate user emails found!');
    }
    
    // 8. Test Data Consistency
    console.log('\n8️⃣ Testing DATA CONSISTENCY...');
    
    // Check for required fields
    const usersWithoutEmail = await db.collection('users').countDocuments({ email: { $exists: false } });
    const attendeesWithoutQR = await db.collection('attendees').countDocuments({ qrCode: { $exists: false } });
    const eventsWithoutName = await db.collection('events').countDocuments({ name: { $exists: false } });
    
    console.log(`✅ Users without email: ${usersWithoutEmail} (should be 0)`);
    console.log(`✅ Attendees without QR code: ${attendeesWithoutQR} (should be 0)`);
    console.log(`✅ Events without name: ${eventsWithoutName} (should be 0)`);
    
    // Check date consistency
    const eventsWithInvalidDates = await db.collection('events').countDocuments({
      $expr: { $gt: ['$startDate', '$endDate'] }
    });
    console.log(`✅ Events with invalid dates: ${eventsWithInvalidDates} (should be 0)`);
    
    // 9. Test Aggregation Queries
    console.log('\n9️⃣ Testing AGGREGATION QUERIES...');
    
    // Events with attendee counts
    const eventsWithCounts = await db.collection('events').aggregate([
      {
        $lookup: {
          from: 'attendees',
          localField: '_id',
          foreignField: 'eventId',
          as: 'attendees'
        }
      },
      {
        $addFields: {
          attendeeCount: { $size: '$attendees' }
        }
      },
      {
        $project: {
          name: 1,
          attendeeCount: 1
        }
      }
    ]).toArray();
    
    console.log('✅ Events with attendee counts:');
    eventsWithCounts.forEach(event => {
      console.log(`   ${event.name}: ${event.attendeeCount} attendees`);
    });
    
    // Users with scan counts
    const usersWithScanCounts = await db.collection('users').aggregate([
      {
        $lookup: {
          from: 'scan_logs',
          localField: '_id',
          foreignField: 'scannedBy',
          as: 'scans'
        }
      },
      {
        $addFields: {
          scanCount: { $size: '$scans' }
        }
      },
      {
        $project: {
          name: 1,
          role: 1,
          scanCount: 1
        }
      }
    ]).toArray();
    
    console.log('✅ Users with scan counts:');
    usersWithScanCounts.forEach(user => {
      console.log(`   ${user.name} (${user.role}): ${user.scanCount} scans`);
    });
    
    // 10. Test Index Performance
    console.log('\n🔟 Testing INDEX PERFORMANCE...');
    
    // Test QR code lookup performance
    const startTime = Date.now();
    const qrLookupResult = await db.collection('attendees').findOne({ qrCode: 'JD-2024-001' });
    const endTime = Date.now();
    
    console.log(`✅ QR code lookup took ${endTime - startTime}ms`);
    if (qrLookupResult) {
      console.log(`   Found: ${qrLookupResult.name}`);
    }
    
    console.log('\n🎉 Data integrity testing completed!');
    
  } catch (error) {
    console.error('❌ Error during data integrity testing:', error);
  } finally {
    await client.close();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the test
testDataIntegrity().catch(console.error);
