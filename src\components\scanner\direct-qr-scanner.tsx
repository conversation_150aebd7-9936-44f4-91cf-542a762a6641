"use client"

import { useEffect, useRef, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Camera, 
  CameraOff, 
  Upload, 
  Flashlight,
  FlashlightOff,
  RefreshCw,
  CheckCircle,
  AlertTriangle
} from "lucide-react"

interface DirectQRScannerProps {
  onScanResult: (data: string) => void
  onScanError: (error: string) => void
}

export function DirectQRScanner({ onScanResult, onScanError }: DirectQRScannerProps) {
  const videoRef = useRef<HTMLVideoElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const scannerRef = useRef<any>(null)
  const [status, setStatus] = useState<'loading' | 'ready' | 'error'>('loading')
  const [error, setError] = useState<string | null>(null)
  const [scanCount, setScanCount] = useState(0)
  const [isFlashlightOn, setIsFlashlightOn] = useState(false)

  // Initialize scanner immediately when component mounts
  useEffect(() => {
    let mounted = true

    const initScanner = async () => {
      if (!videoRef.current) return

      console.log('🎥 DirectQRScanner: Starting initialization...')
      setStatus('loading')
      setError(null)

      try {
        // Import QR Scanner
        console.log('📦 DirectQRScanner: Importing library...')
        const QrScannerModule = await import('qr-scanner')
        const QrScanner = QrScannerModule.default

        if (!mounted) return

        console.log('🔧 DirectQRScanner: Creating scanner instance...')
        const scanner = new QrScanner(
          videoRef.current,
          (result) => {
            console.log('✅ DirectQRScanner: QR Code detected:', result.data)
            setScanCount(prev => prev + 1)
            onScanResult(result.data)
            
            // Haptic feedback
            if ('vibrate' in navigator) {
              navigator.vibrate(100)
            }
          },
          {
            onDecodeError: (error) => {
              console.debug('🔍 DirectQRScanner: Decode attempt:', error.message)
            },
            highlightScanRegion: true,
            highlightCodeOutline: true,
            preferredCamera: 'environment',
            maxScansPerSecond: 2
          }
        )

        if (!mounted) {
          scanner.destroy()
          return
        }

        scannerRef.current = scanner

        console.log('🚀 DirectQRScanner: Starting scanner...')
        await scanner.start()

        if (!mounted) {
          scanner.stop()
          scanner.destroy()
          return
        }

        setStatus('ready')
        console.log('🎉 DirectQRScanner: Ready!')

      } catch (err) {
        console.error('❌ DirectQRScanner: Failed:', err)
        if (mounted) {
          const errorMessage = err instanceof Error ? err.message : 'Scanner initialization failed'
          setError(errorMessage)
          setStatus('error')
          onScanError(errorMessage)
        }
      }
    }

    initScanner()

    return () => {
      mounted = false
      if (scannerRef.current) {
        console.log('🧹 DirectQRScanner: Cleaning up...')
        scannerRef.current.stop()
        scannerRef.current.destroy()
        scannerRef.current = null
      }
    }
  }, [onScanResult, onScanError])

  const toggleFlashlight = async () => {
    if (!scannerRef.current) return

    try {
      if (isFlashlightOn) {
        await scannerRef.current.turnFlashlightOff()
        setIsFlashlightOn(false)
      } else {
        await scannerRef.current.turnFlashlightOn()
        setIsFlashlightOn(true)
      }
    } catch (err) {
      onScanError('Flashlight not supported on this device')
    }
  }

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith('image/')) {
      onScanError('Please select an image file')
      return
    }

    try {
      console.log('📁 DirectQRScanner: Scanning uploaded image...')
      const QrScannerModule = await import('qr-scanner')
      const QrScanner = QrScannerModule.default
      const result = await QrScanner.scanImage(file)
      onScanResult(result)
      setScanCount(prev => prev + 1)
      console.log('✅ DirectQRScanner: QR code found in uploaded image')
    } catch (err) {
      console.error('❌ DirectQRScanner: No QR code in image:', err)
      onScanError('No QR code found in the uploaded image')
    }

    e.target.value = ""
  }

  const retry = () => {
    setStatus('loading')
    setError(null)
    // Component will re-initialize via useEffect
    window.location.reload()
  }

  // Loading state
  if (status === 'loading') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <RefreshCw className="h-5 w-5 animate-spin" />
            <span>Initializing Scanner</span>
          </CardTitle>
          <CardDescription>
            Setting up camera and QR detection...
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center py-8">
          <div className="space-y-4">
            <RefreshCw className="mx-auto h-12 w-12 animate-spin text-muted-foreground" />
            <p className="text-muted-foreground">Starting camera, please wait...</p>
            <p className="text-xs text-muted-foreground">This should take less than 10 seconds</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Error state
  if (status === 'error') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CameraOff className="h-5 w-5" />
            <span>Scanner Error</span>
          </CardTitle>
          <CardDescription>
            Unable to initialize QR scanner
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Error:</strong> {error}
            </AlertDescription>
          </Alert>

          <div className="flex flex-col gap-2">
            <Button onClick={retry}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry Scanner
            </Button>
            
            <Button onClick={() => fileInputRef.current?.click()} variant="outline">
              <Upload className="mr-2 h-4 w-4" />
              Upload QR Code Image
            </Button>
            
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>
        </CardContent>
      </Card>
    )
  }

  // Working scanner
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Camera className="h-5 w-5 text-green-600" />
              <span>QR Scanner</span>
              <Badge variant="outline" className="text-green-600 border-green-600">
                <CheckCircle className="h-3 w-3 mr-1" />
                Active
              </Badge>
            </CardTitle>
            <CardDescription>
              Point camera at QR code • {scanCount} scans completed
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Video Container */}
        <div className="relative aspect-square bg-black rounded-lg overflow-hidden">
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            playsInline
            muted
            autoPlay
          />
          
          {/* Scanning overlay */}
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="w-48 h-48 border-2 border-primary rounded-lg relative">
              <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-primary rounded-tl-lg"></div>
              <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-primary rounded-tr-lg"></div>
              <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-primary rounded-bl-lg"></div>
              <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-primary rounded-br-lg"></div>
            </div>
          </div>

          {/* Status */}
          <div className="absolute top-4 left-4 flex space-x-2">
            <Badge variant="secondary" className="bg-black bg-opacity-50 text-white border-none">
              <Camera className="h-3 w-3 mr-1" />
              Live
            </Badge>
            {isFlashlightOn && (
              <Badge variant="secondary" className="bg-yellow-500 bg-opacity-80 text-black border-none">
                <Flashlight className="h-3 w-3 mr-1" />
                Flash
              </Badge>
            )}
          </div>
        </div>

        {/* Controls */}
        <div className="flex justify-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={toggleFlashlight}
            title={isFlashlightOn ? "Turn off flashlight" : "Turn on flashlight"}
          >
            {isFlashlightOn ? (
              <FlashlightOff className="h-4 w-4" />
            ) : (
              <Flashlight className="h-4 w-4" />
            )}
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => fileInputRef.current?.click()}
            title="Upload QR code image"
          >
            <Upload className="h-4 w-4" />
          </Button>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            className="hidden"
          />
        </div>

        {/* Instructions */}
        <div className="text-center text-sm text-muted-foreground">
          <p>📱 Position QR code within the frame for automatic detection</p>
          <p className="text-xs mt-1">Scanner initializes automatically when page loads</p>
        </div>
      </CardContent>
    </Card>
  )
}
