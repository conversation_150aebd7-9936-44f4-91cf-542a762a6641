"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Info,
  Shield,
  Camera,
  Globe
} from "lucide-react"

interface CompatibilityResult {
  test: string
  status: 'pass' | 'fail' | 'warning'
  message: string
  details?: string
}

export function CompatibilityChecker() {
  const [results, setResults] = useState<CompatibilityResult[]>([])
  const [isVisible, setIsVisible] = useState(false)

  const runCompatibilityCheck = () => {
    const checks: CompatibilityResult[] = []

    // 1. HTTPS Check
    const isSecure = location.protocol === 'https:' || location.hostname === 'localhost'
    checks.push({
      test: 'Secure Context (HTTPS)',
      status: isSecure ? 'pass' : 'fail',
      message: isSecure ? 'Secure connection detected' : 'HTTPS required for camera access',
      details: isSecure 
        ? 'Camera access is allowed on HTTPS or localhost'
        : 'Mobile devices require HTTPS for camera access. Use ngrok or setup local HTTPS.'
    })

    // 2. MediaDevices API
    const hasMediaDevices = !!(navigator.mediaDevices)
    checks.push({
      test: 'MediaDevices API',
      status: hasMediaDevices ? 'pass' : 'fail',
      message: hasMediaDevices ? 'MediaDevices API available' : 'MediaDevices API not supported',
      details: hasMediaDevices 
        ? 'Your browser supports modern camera access'
        : 'Your browser is too old or doesn\'t support camera access'
    })

    // 3. getUserMedia API
    const hasGetUserMedia = !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
    checks.push({
      test: 'getUserMedia API',
      status: hasGetUserMedia ? 'pass' : 'fail',
      message: hasGetUserMedia ? 'getUserMedia available' : 'getUserMedia not supported',
      details: hasGetUserMedia 
        ? 'Camera access API is available'
        : 'Camera access API is not available. Update your browser or use HTTPS.'
    })

    // 4. Device Detection
    const userAgent = navigator.userAgent.toLowerCase()
    const isIOS = /iphone|ipad|ipod/.test(userAgent)
    const isAndroid = /android/.test(userAgent)
    const isSafari = /safari/.test(userAgent) && !/chrome/.test(userAgent)
    const isChrome = /chrome/.test(userAgent)

    let deviceStatus: 'pass' | 'warning' | 'fail' = 'pass'
    let deviceMessage = 'Compatible device/browser'
    let deviceDetails = ''

    if (isIOS && !isSecure) {
      deviceStatus = 'fail'
      deviceMessage = 'iOS device requires HTTPS'
      deviceDetails = 'iOS Safari blocks camera access on non-HTTPS sites'
    } else if (isIOS && isSafari) {
      deviceStatus = 'pass'
      deviceMessage = 'iOS Safari detected'
      deviceDetails = 'iOS Safari with HTTPS should work'
    } else if (isAndroid) {
      deviceStatus = 'pass'
      deviceMessage = 'Android device detected'
      deviceDetails = 'Android browsers generally support camera access'
    } else if (isChrome) {
      deviceStatus = 'pass'
      deviceMessage = 'Chrome browser detected'
      deviceDetails = 'Chrome has excellent camera support'
    }

    checks.push({
      test: 'Device/Browser Compatibility',
      status: deviceStatus,
      message: deviceMessage,
      details: deviceDetails
    })

    // 5. Camera Enumeration Test
    if (hasMediaDevices && navigator.mediaDevices.enumerateDevices) {
      navigator.mediaDevices.enumerateDevices()
        .then(devices => {
          const videoDevices = devices.filter(device => device.kind === 'videoinput')
          checks.push({
            test: 'Camera Detection',
            status: videoDevices.length > 0 ? 'pass' : 'warning',
            message: `${videoDevices.length} camera(s) detected`,
            details: videoDevices.length > 0 
              ? `Found: ${videoDevices.map(d => d.label || 'Unknown Camera').join(', ')}`
              : 'No cameras found or permission not granted yet'
          })
          setResults([...checks])
        })
        .catch(() => {
          checks.push({
            test: 'Camera Detection',
            status: 'warning',
            message: 'Could not enumerate cameras',
            details: 'Camera detection failed - may work after permission is granted'
          })
          setResults([...checks])
        })
    } else {
      checks.push({
        test: 'Camera Detection',
        status: 'fail',
        message: 'Cannot detect cameras',
        details: 'Device enumeration not supported'
      })
      setResults(checks)
    }

    if (!hasMediaDevices || !navigator.mediaDevices.enumerateDevices) {
      setResults(checks)
    }
  }

  useEffect(() => {
    if (isVisible) {
      runCompatibilityCheck()
    }
  }, [isVisible])

  const getStatusIcon = (status: CompatibilityResult['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'fail':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
    }
  }

  const getStatusBadge = (status: CompatibilityResult['status']) => {
    switch (status) {
      case 'pass':
        return <Badge variant="default" className="bg-green-600">Pass</Badge>
      case 'fail':
        return <Badge variant="destructive">Fail</Badge>
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-600 text-white">Warning</Badge>
    }
  }

  const getOverallStatus = () => {
    if (results.length === 0) return 'unknown'
    
    const hasFailures = results.some(r => r.status === 'fail')
    const hasWarnings = results.some(r => r.status === 'warning')
    
    if (hasFailures) return 'fail'
    if (hasWarnings) return 'warning'
    return 'pass'
  }

  const getRecommendations = () => {
    const overallStatus = getOverallStatus()
    const isSecure = location.protocol === 'https:' || location.hostname === 'localhost'
    const userAgent = navigator.userAgent.toLowerCase()
    const isIOS = /iphone|ipad|ipod/.test(userAgent)

    if (overallStatus === 'fail') {
      if (!isSecure && isIOS) {
        return [
          '🔒 Use HTTPS for iPhone camera access',
          '📱 Try: ngrok http 3000 (creates HTTPS tunnel)',
          '🌐 Or setup local HTTPS certificates',
          '📁 Use image upload as fallback'
        ]
      } else if (!navigator.mediaDevices) {
        return [
          '🔄 Update your browser to a modern version',
          '🌐 Try Chrome, Firefox, or Safari',
          '📁 Use image upload instead of camera',
          '💻 Check if you\'re on a supported device'
        ]
      }
    }

    if (overallStatus === 'warning') {
      return [
        '✅ Most features should work',
        '📷 Camera access may require permission',
        '🔄 Try refreshing after granting permissions',
        '📁 Image upload available as backup'
      ]
    }

    return [
      '🎉 Your device/browser is fully compatible!',
      '📷 Camera access should work smoothly',
      '🔍 QR code scanning should be fast and accurate',
      '📱 All features are supported'
    ]
  }

  if (!isVisible) {
    return (
      <div className="mb-4">
        <button 
          onClick={() => setIsVisible(true)}
          className="text-sm text-blue-600 hover:text-blue-800 underline"
        >
          🔍 Check Device Compatibility
        </button>
      </div>
    )
  }

  return (
    <Card className="mb-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <Shield className="h-5 w-5" />
              <span>Compatibility Check</span>
            </CardTitle>
            <CardDescription>
              Verify camera access and QR scanner compatibility
            </CardDescription>
          </div>
          <button 
            onClick={() => setIsVisible(false)}
            className="text-gray-500 hover:text-gray-700"
          >
            ×
          </button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {results.length > 0 && (
          <div className="space-y-3">
            {results.map((result, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 border rounded-lg">
                {getStatusIcon(result.status)}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium">{result.test}</h4>
                    {getStatusBadge(result.status)}
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">{result.message}</p>
                  {result.details && (
                    <p className="text-xs text-muted-foreground mt-1">{result.details}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <div className="font-medium">Recommendations:</div>
              <div className="text-sm space-y-1">
                {getRecommendations().map((rec, index) => (
                  <div key={index}>{rec}</div>
                ))}
              </div>
            </div>
          </AlertDescription>
        </Alert>

        <div className="text-xs text-muted-foreground">
          <strong>Current URL:</strong> {window.location.href}
          <br />
          <strong>Protocol:</strong> {window.location.protocol}
          <br />
          <strong>User Agent:</strong> {navigator.userAgent}
        </div>
      </CardContent>
    </Card>
  )
}
