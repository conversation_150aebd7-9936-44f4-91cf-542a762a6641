import { PrismaClient } from '@prisma/client'
import * as bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Check if admin user already exists
  let admin = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  })

  if (!admin) {
    const adminPassword = await bcrypt.hash('admin123', 10)
    admin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'Admin User',
        password: adminPassword,
        role: 'ADMIN',
      },
    })
  }

  // Check if scanner user already exists
  let scanner = await prisma.user.findUnique({
    where: { email: '<EMAIL>' }
  })

  if (!scanner) {
    const scannerPassword = await bcrypt.hash('scanner123', 10)
    scanner = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: '<PERSON>anner User',
        password: scannerPassword,
        role: 'SCANNER',
      },
    })
  }

  // Check if sample event already exists
  let event = await prisma.event.findFirst({
    where: { name: 'Tech Conference 2024' }
  })

  if (!event) {
    event = await prisma.event.create({
      data: {
        name: 'Tech Conference 2024',
        description: 'Annual technology conference with networking and meals',
        startDate: new Date('2024-03-15T08:00:00Z'),
        endDate: new Date('2024-03-15T18:00:00Z'),
        serviceStartTime: new Date('2024-03-15T12:00:00Z'),
        serviceEndTime: new Date('2024-03-15T14:00:00Z'),
        primaryColor: '#3b82f6',
        secondaryColor: '#1e40af',
        creatorId: admin.id,
      },
    })
  }

  // Create meal types if they don't exist
  let mainCourse = await prisma.mealType.findFirst({
    where: { name: 'Main Course', eventId: event.id }
  })

  if (!mainCourse) {
    mainCourse = await prisma.mealType.create({
      data: {
        name: 'Main Course',
        description: 'Primary meal with protein and sides',
        maxQuantityPerAttendee: 1,
        totalQuantity: 500,
        isVegetarian: false,
        eventId: event.id,
      },
    })
  }

  let vegetarianOption = await prisma.mealType.findFirst({
    where: { name: 'Vegetarian Option', eventId: event.id }
  })

  if (!vegetarianOption) {
    vegetarianOption = await prisma.mealType.create({
      data: {
        name: 'Vegetarian Option',
        description: 'Plant-based main course',
        maxQuantityPerAttendee: 1,
        totalQuantity: 100,
        isVegetarian: true,
        eventId: event.id,
      },
    })
  }

  // Create sample attendees
  const attendees = [
    {
      name: 'John Doe',
      email: '<EMAIL>',
      qrCode: 'JD-2024-001',
      dietaryRestrictions: 'None',
      eventId: event.id,
    },
    {
      name: 'Jane Smith',
      email: '<EMAIL>',
      qrCode: 'JS-2024-002',
      dietaryRestrictions: 'Vegetarian',
      eventId: event.id,
    },
    {
      name: 'Mike Johnson',
      email: '<EMAIL>',
      qrCode: 'MJ-2024-003',
      dietaryRestrictions: 'Gluten-free',
      specialNotes: 'VIP guest',
      eventId: event.id,
    },
  ]

  for (const attendeeData of attendees) {
    const existingAttendee = await prisma.attendee.findUnique({
      where: { qrCode: attendeeData.qrCode }
    })

    if (!existingAttendee) {
      await prisma.attendee.create({
        data: attendeeData,
      })
    }
  }

  console.log('✅ Database seeded successfully!')
  console.log('👤 Admin user: <EMAIL> / admin123')
  console.log('📱 Scanner user: <EMAIL> / scanner123')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
